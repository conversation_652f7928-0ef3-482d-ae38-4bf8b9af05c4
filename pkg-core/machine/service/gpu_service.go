package service

import (
	"server/pkg-core/machine/model"
	"server/pkg-core/module_definition"
	"server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/queue"
	"time"

	"gorm.io/gorm"
)

type GpuService struct {
	machine module_definition.MachineInference
	log     *logger.Logger
	q       *queue.Q
}

const GpuServiceModuleName = "gpu_service"

func NewGpuServiceProvider(machine module_definition.MachineInference, q *queue.Q) *GpuService {
	return &GpuService{machine: machine, log: logger.NewLogger(GpuServiceModuleName), q: q}
}

func (svc *GpuService) Create(params *model.CreateGpuTypeParams) (err error) {
	if params == nil {
		svc.log.Error("create gpu_type params is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	//gpuType := &model.GPUType{GpuName: params.GpuName, GpuRank: params.GpuRank, GpuMemory: params.GpuMemory, GpuSeriesType: params.GpuSeriesType, FloatingPointHashRate: params.FloatingPointHashRate, CreatedAt: time.Now()}
	//err = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{}, InsertPayload: gpuType}).GetError()
	//if err != nil {
	//	svc.log.WithField("err", err).Error("create gpu_type failed.")
	//	err = businesserror.ErrDatabaseError
	//	return
	//}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()

	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		gpuType := &model.GPUType{GpuName: params.GpuName, GpuRank: params.GpuRank, GpuMemory: params.GpuMemory, GpuSeriesType: params.GpuSeriesType, FloatingPointHashRate: params.FloatingPointHashRate, ChipCorp: params.ChipCorp, CpuArch: params.CpuArch, CreatedAt: time.Now()}
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.GPUType{},
			InsertPayload:           gpuType,
		})
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("create gpu failed.")
			return
		}
		queueTxMsgUUIDList, errDB = svc.pubInsertGpu(tx, gpuType.Map())
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq insert gpu failed.")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).Error("create gpu_type failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuService) Update(params *model.GpuTypeInfo) (err error) {
	if params == nil {
		svc.log.Error("update gpu_type params is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()

	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		gpuType := &model.GPUType{GpuName: params.GpuName, GpuRank: params.GpuRank, GpuMemory: params.GpuMemory, GpuSeriesType: params.GpuSeriesType, FloatingPointHashRate: params.FloatingPointHashRate, ChipCorp: params.ChipCorp, CpuArch: params.CpuArch}
		errDB = db_helper.UpdateOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.GPUType{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.GPUTypeID}}}, gpuType)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("update gpu failed.")
			return
		}
		data := map[string]interface{}{
			"id":                       params.GPUTypeID,
			"gpu_name":                 params.GpuName,
			"gpu_rank":                 params.GpuRank,
			"gpu_memory":               params.GpuMemory,
			"gpu_series_type":          params.GpuSeriesType,
			"floating_point_hash_rate": params.FloatingPointHashRate,
			"chip_corp":                params.ChipCorp,
			"cpu_arch":                 params.CpuArch,
		}
		queueTxMsgUUIDList, errDB = svc.pubUpdateGpu(tx, data)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update gpu failed.")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("id ", params.GPUTypeID).Error("update gpu_type failed")
		err = businesserror.ErrDatabaseError
		return
	}

	//gpuType := &model.GPUType{GpuName: params.GpuName, GpuRank: params.GpuRank, GpuMemory: params.GpuMemory, GpuSeriesType: params.GpuSeriesType, FloatingPointHashRate: params.FloatingPointHashRate}
	//err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{},
	//	Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.GPUTypeID}}}, gpuType).GetError()
	//if err != nil {
	//	svc.log.WithField("err", err).WithField("id ", params.GPUTypeID).Error("update gpu_type failed")
	//	err = businesserror.ErrDatabaseError
	//	return
	//}
	return
}

func (svc *GpuService) List(gpuName string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.GPUType, err error) {
	list = make([]*model.GPUType, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameGpuType).Where("deleted_at is null")

	if gpuName != "" {
		name := "%" + gpuName + "%"
		db = db.Where("gpu_name like ?", name)
	}
	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithField("err", err).Error("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)
	paged.List = &list

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithField("gpu_type_name", gpuName).WithError(err).Error("get gpu_type failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuService) Delete(GpuTypeID int) (err error) {
	if GpuTypeID < 1 {
		svc.log.WithField("gpu_type_id", GpuTypeID).Error("gpu_type_id must greater than 0")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.Delete(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.GPUType{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": GpuTypeID}}}, &model.GPUType{},
		)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("delete gpu failed.")
			return
		}
		data := map[string]interface{}{"id": GpuTypeID}
		queueTxMsgUUIDList, errDB = svc.pubDeleteGpu(tx, data)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq delete gpu failed.")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("gpu_type_id", GpuTypeID).Error("delete gpu_type failed")
		err = businesserror.ErrDatabaseError
		return
	}

	//err = db_helper.Delete(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{},
	//	Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": GpuTypeID}}}, &model.GPUType{}).GetError()
	//if err != nil {
	//	svc.log.WithField("err", err).WithField("gpu_type_id", GpuTypeID).Error("delete gpu_type failed")
	//	err = businesserror.ErrDatabaseError
	//	return
	//}
	return
}

func (svc *GpuService) CheckGpuTypeName(gpuName string, gpuTypeID int) (existed bool, err error) {
	if len(gpuName) <= 0 {
		svc.log.Error("gpu_name can not be null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	gpuType := &model.GPUType{}
	err = db_helper.GetOne(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"gpu_name": gpuName}}}, gpuType).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		svc.log.WithField("gpu_type_name", gpuName).Error("get gpu_type by name failed")
		err = businesserror.ErrDatabaseError
		return
	}
	if gpuTypeID > 0 {
		if gpuType.ID == gpuTypeID {
			return false, nil
		}
	}
	return true, nil
}

func (svc *GpuService) GetGpuID(gpuNames []string) (ids []int, err error) {
	if len(gpuNames) <= 0 {
		svc.log.Error("gpu_name can not be null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	gpuTypeList := make([]*model.GPUType, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "gpu_name", InSet: gpuNames}},
		},
	}, &gpuTypeList).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get gpu_type list failed")
		err = businesserror.ErrDatabaseError
		return
	}
	idMap := map[int]int{}
	for _, gpu := range gpuTypeList {
		_, ok := idMap[gpu.ID]
		if !ok {
			idMap[gpu.ID] = 1
			ids = append(ids, gpu.ID)
		}
	}
	return
}

func (svc *GpuService) GetGpu(gpuID int) (gpuType *model.GPUType, err error) {
	if gpuID < 1 {
		svc.log.WithField("gpu_id", gpuID).Error("gpu_id can not less than 0")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	gpuType = &model.GPUType{}
	err = db_helper.GetOne(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": gpuID}}}, gpuType).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get gpu_type failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuService) CheckGpuOccupation(gpuTypeID int) (info string, err error) {
	_, err = svc.machine.GetMachineByGpuTypeID(gpuTypeID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", nil
		}
		svc.log.WithField("gpu_type_id", gpuTypeID).Error("the gpu_type is used by machine")
		return "", businesserror.ErrDatabaseError
	}
	return businesserror.ErrGpuTypeUsed.Error(), nil
}

func (svc *GpuService) GetGpuByIDs(gpuIDs []int) (gpuTypes []*model.GPUType, err error) {
	if len(gpuIDs) < 1 {
		svc.log.WithField("gpu_ids", gpuIDs).Error("gpu_id can not less than 0")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	err = db_helper.GetAll(db_helper.QueryDefinition{ModelDefinition: &model.GPUType{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "id", InSet: gpuIDs}},
			Orders:    []string{"gpu_rank desc"},
		},
		NoLimit: true,
	}, &gpuTypes).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get gpu_type failed")
		err = businesserror.ErrDatabaseError
		return
	}
	//
	//if len(gpuTypes) <= 1 {
	//	return
	//}
	//for i := 0; i < len(gpuTypes); i++ {
	//	flag := false
	//	for j := 0; j < len(gpuTypes)-i-1; j++ {
	//		if gpuTypes[j].GpuRank < gpuTypes[j+1].GpuRank {
	//			gpuTypes[j], gpuTypes[j+1] = gpuTypes[j+1], gpuTypes[j]
	//			flag = true
	//		}
	//	}
	//	if !flag {
	//		break
	//	}
	//}
	return
}
