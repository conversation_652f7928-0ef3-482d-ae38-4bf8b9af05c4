package service

import (
	"context"
	"server/pkg-agent/agent_constant"
	"server/pkg-core/api/coreapi"
	dataDiskModel "server/pkg-core/data_disk_stock/model"
	gpuStockModel "server/pkg-core/gpu_stock/model"
	"server/pkg-core/machine/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	tsc "server/plugin/redis_plugin/time_service_center"
	"time"
)

func (svc *McService) MachineMonitor(ctx context.Context) {
	// redis维护所有机器状态集合，定时监控机器状态
	go svc.MachineStatusMonitor(ctx)
	// 定时同步machine和machine_gpu_occupation中的gpu占用数据
	go svc.MachineGpuNumMonitor(ctx)
	// 定时健康机器可租时间是否到期,到期就下架机器
	go svc.MonitorMachineRentDeadline(ctx)
	// GPU占用如果没有commit，则回滚释放
	go svc.MonitorGPURelease(ctx)
	// 磁盘占用如果没有commit，则回滚释放
	go svc.MonitorDataDiskStockRelease(ctx)
}

func (svc *McService) MachineStatusMonitor(ctx context.Context) {
	machineStatusTicker := time.NewTicker(constant.MachineStatusMonitorTime) // 5s
	for {
		select {
		case <-ctx.Done():
			return
		case <-machineStatusTicker.C:
			err := svc.SyncMachineStatus()
			if err != nil {
				svc.log.WithField("err", err).Info("sync machine status failed")
			}
		}
	}
}

func (svc *McService) MachineGpuNumMonitor(ctx context.Context) {
	syncGpuInfoTicker := time.NewTicker(constant.MachineSyncGpuInfoAndBindingInstanceNumTime) // 2s
	for {
		select {
		case <-ctx.Done():
			return
		case <-syncGpuInfoTicker.C:
			err := svc.SyncMachineGpuInfo()
			if err != nil {
				svc.log.WithField("err", err).Info("sync machine gpu num failed")
			}
		}
	}
}

func (svc *McService) MonitorMachineRentDeadline(ctx context.Context) {
	rentDeadlineTicker := time.NewTicker(constant.MachineRentDeadlineTime) //10min
	for {
		select {
		case <-ctx.Done():
			return
		case <-rentDeadlineTicker.C:
			err := svc.MonitorMachineRentDeadlineInfo()
			if err != nil {
				svc.log.WithField("err", err).Info("sync machine rent deadline failed")
			}
		}
	}
}

func (svc *McService) MonitorGPURelease(ctx context.Context) {
	rentDeadlineTicker := time.NewTicker(time.Minute) //1min
	for {
		select {
		case <-ctx.Done():
			return
		case <-rentDeadlineTicker.C:
			err := svc.MonitorSyncGPUReleaseStatus()
			if err != nil {
				svc.log.WithField("err", err).Info("MonitorSyncGPUReleaseStatus failed")
			}
		}
	}
}

func (svc *McService) MonitorDataDiskStockRelease(ctx context.Context) {
	rentDeadlineTicker := time.NewTicker(time.Minute) //1min
	for {
		select {
		case <-ctx.Done():
			return
		case <-rentDeadlineTicker.C:
			err := svc.MonitorSyncDataDiskStockReleaseStatus()
			if err != nil {
				svc.log.WithField("err", err).Info("MonitorSyncDataDiskStockReleaseStatus failed")
			}
		}
	}
}

func (svc *McService) SyncMachineStatus() (err error) {
	machines, err := svc.getAllMachine()
	if err != nil {
		svc.log.Error("get all machine failed")
		return
	}
	data, err := svc.machineRedis.ConnectionInfoHGetAll()
	if err != nil {
		svc.log.Error("redis get all machine status failed")
		return
	}

	now := time.Now()
	for _, machine := range machines {
		var isUpdate bool
		_, ok := data[machine.MachineID]
		if ok {
			createAt, err := time.Parse(time.RFC3339, data[machine.MachineID])
			if err != nil {
				svc.log.WithField("machine_id", machine.MachineID).WarnE(err, "time.Parse machine connection active at failed")
			}
			// 间隔时间设置为1min
			if now.Sub(createAt) > agent_constant.MachineStatusConnectTime {
				err = svc.machineRedis.HDel(machine.MachineID)
				if err != nil {
					svc.log.WithField("err", err).WithField("machine_id", machine.MachineID).Warn("redis delete hash filed failed")
					continue
				}
				isUpdate = true
			}
		} else {
			isUpdate = true
		}
		if machine.Status == agent_constant.NetworkConnectionException && (machine.Online == constant.Offline || machine.Online == constant.AdminOffline) {
			continue
		}
		if isUpdate && !machine.FakeRecord {
			data := map[string]interface{}{"status": agent_constant.NetworkConnectionException, "machine_cc": machine.MachineCC + 1}
			if machine.Online != constant.AdminOffline {
				data["on_line"] = constant.Offline
			}
			err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.Machine{},
				Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machine.MachineID, "machine_cc": machine.MachineCC}}},
				data).GetError()
			if err != nil {
				svc.log.WithField("err", err).WithField("machine_id ", machine.MachineID).Error("update machine failed")
				continue
			}
			data["machine_id"] = machine.MachineID
			err = svc.pubUpdateMachineSimple(data)
			if err != nil {
				svc.log.WithField("err", err).Error("pub mq update machine failed.")
				continue
			}
		}
	}
	return nil
}

func (svc *McService) SyncMachineGpuInfo(machineIds ...string) (err error) {
	var machines []*model.Machine
	machines, err = svc.getAllMachine(machineIds...)
	if err != nil {
		svc.log.Error("get all machine failed")
		return
	}

	for _, machine := range machines {
		var instanceGpuStock []*gpuStockModel.GpuStock
		instanceGpuStock, err = svc.gpuStock.GetGpuStockUsed(machine.MachineID, constant.DevelopmentLowLevel)
		if err != nil {
			svc.log.WithField("err", err).WithField("machine_id", machine.MachineID).Error("Get machine all instance occupied gpu failed.")
			continue
		}
		gpuUsed10 := 0
		gpuUsed5 := 0
		gpuUserOnly10 := 0
		for _, v := range instanceGpuStock {
			if v.Priority >= 10 {
				gpuUsed10++
			}
			if v.Priority >= 5 {
				gpuUsed5++
			}

			if v.Priority == 10 {
				gpuUserOnly10++
			}
		}

		data := map[string]interface{}{
			"gpu_used":         gpuUsed10,                            // 统计高优先级的占用，10，11
			"gpu_idle_num":     machine.GpuNumber - int64(gpuUsed10), // 优先级等于5
			"gpu_used_5":       gpuUsed5,                             // 统计优先级大于等于5占用，5，10，11.
			"gpu_used_only_10": gpuUserOnly10,                        // 优先级等于10
		}
		rows, modelErr := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{ModelDefinition: &model.Machine{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machine.MachineID}}},
			data)
		if modelErr.IsNotNil() {
			svc.log.WithField("err", modelErr.GetError()).WithField("machine_id ", machine.MachineID).Error("update machine failed")
			continue
		}
		if rows == 0 {
			continue
		}
		data["machine_id"] = machine.MachineID
		data["gpu_num_last_changed_at"] = time.Now().Unix()
		err = svc.pubUpdateMachineSimple(data)
		if err != nil {
			svc.log.WithField("err", err).Error("pub mq update machine failed.")
			return
		}
	}
	return nil
}

func (svc *McService) MonitorMachineRentDeadlineInfo() (err error) {
	machines, err := svc.getAllMachine()
	if err != nil {
		svc.log.Error("get all machine failed")
		return
	}

	now := time.Now()
	for _, machine := range machines {
		if machine.RentDeadline == nil {
			continue
		}
		if now.Sub(*machine.RentDeadline) > 0 {
			data := map[string]interface{}{
				"on_line": constant.AdminOffline,
			}
			err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.Machine{},
				Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machine.MachineID}}},
				data).GetError()
			if err != nil {
				svc.log.WithField("err", err).WithField("machine_id ", machine.MachineID).Error("update machine failed")
				continue
			}
			data["machine_id"] = machine.MachineID
			err = svc.pubUpdateMachineSimple(data)
			if err != nil {
				svc.log.WithField("err", err).Error("pub mq update machine failed.")
				continue
			}
		}
	}
	return nil
}

// 最多获取100条记录
func (svc *McService) GetMachineByIDs(machinesIDs []string) (machines model.MachineList, err error) {
	if len(machinesIDs) < 1 {
		svc.log.Error("machine_ids should be greater than 0")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Machine{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "machine_id", InSet: machinesIDs}},
		},
		NoLimit: true,
	}, &machines).GetError()
	if err != nil {
		svc.log.WithField("machine_ids", machinesIDs).Error("get machine faield")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *McService) MachineStatusConnect(ctx context.Context, machineID string) {
	machineSetStatusTimeTicker := time.NewTicker(constant.MachineSetHeartStatusTime) //
	for {
		select {
		case <-ctx.Done():
			svc.log.Warn("agent-server %s quit gracefully", machineID)
			err := svc.updateMachineHeartTime(false, machineID)
			if err != nil {
				svc.log.WithField("machine_id", machineID).Error("update machine heart status from redis failed")
			}
			return
		case <-machineSetStatusTimeTicker.C:
			//更新机器心跳状态
			err := svc.updateMachineHeartTime(true, machineID)
			if err != nil {
				svc.log.WithField("machine_id", machineID).Error("update machine heart status from redis failed")
			}
		}
	}
}

func (svc *McService) MonitorSyncGPUReleaseStatus() error {
	machines, err := svc.getAllMachine()
	if err != nil {
		svc.log.Error("get all machine failed")
		return err
	}

	now := time.Now()

	for _, machine := range machines {
		instanceGpuStock, err := svc.gpuStock.GetGpuStockUsed(machine.MachineID, constant.InstanceLevel)
		if err != nil {
			svc.log.WithField("err", err).WithField("machine_id", machine.MachineID).Error("Get machine all instance occupied gpu failed.")
			continue
		}

		for _, gpuStock := range instanceGpuStock {
			// 释放GPU分两种情况，一种是上一次预占了但是没有commit，此时状态是prepared直接释放即可
			// 新增加一个判断，如果上一次的占用时间超过2分钟才释放，2分钟以内不释放。因为预占后还没有commit恰好此时定时任务执行就直接释放了。
			if gpuStock.TxStatus == constant.TxStatusPrepared && now.Sub(gpuStock.UpdatedAt).Minutes() > 2 {
				err = svc.gpuStock.Release(&gpuStockModel.GpuRelease{
					RuntimeUUID: gpuStock.RuntimeUUUID.String(),
					MachineID:   gpuStock.MachineID,
					DebugMsg:    "called by MonitorSyncGPUReleaseStatus",
					Timestamp:   tsc.Timestamp(),
				})
				if err != nil {
					svc.log.WithField("err", err).WithField("machine_id ", gpuStock.MachineID).WithField("runtime_uuid", gpuStock.RuntimeUUUID).Error("release gpu stock failed")
					continue
				}
				svc.log.WithField("machine_id ", gpuStock.MachineID).WithField("runtime_uuid", gpuStock.RuntimeUUUID).ErrorE(err, "MonitorSyncGPUReleaseStatus release gpu stock success:status:%s, updated_at:%s, now:%s", gpuStock.TxStatus, gpuStock.UpdatedAt, now)
			}
			//else { // 还有一种是表里的状态不是prepared，但是关联的容器状态是结束状态，此时也需要释放（包年包月的实例不能释放，core无法知道容器对应的实例是什么类型，所以这里不根据容器判断了）
			//container, err := svc.container.GetContainerRecord(gpuStock.RuntimeUUUID)
			//if err != nil {
			//	svc.log.WithField("err", err).WithField("runtime_uuid ", gpuStock.RuntimeUUUID).Error("get container failed")
			//	continue
			//}
			//if container.LatestStatus.ContainerStatus.IsStopFinished() {
			//	err = svc.gpuStock.Release(&gpuStockModel.GpuRelease{
			//		RuntimeUUID: gpuStock.RuntimeUUUID.String(),
			//		MachineID:   gpuStock.MachineID,
			//	})
			//	if err != nil {
			//		svc.log.WithField("err", err).WithField("machine_id ", gpuStock.MachineID).Error("release gpu stock failed")
			//		continue
			//	}
			//}
			//}
		}
	}

	return nil
}

func (svc *McService) MonitorSyncDataDiskStockReleaseStatus() error {
	svc.log.Info("MonitorSyncDataDiskStockReleaseStatus begin")
	machines, err := svc.getAllMachine()
	if err != nil {
		svc.log.Error("get all machine failed")
		return err
	}

	now := time.Now()

	for _, machine := range machines {
		var ddsr []dataDiskModel.DataDiskStockRecord
		err := db_helper.GetAll(db_helper.QueryDefinition{
			ModelDefinition: &dataDiskModel.DataDiskStockRecord{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"machine_id": machine.MachineID,
				},
			},
		}, &ddsr).GetError()
		if err != nil {
			svc.log.WithField("err", err).WithField("machine_id", machine.MachineID).Error("Get machine all data_disk_stock_record ailed.")
			return err
		}

		for _, d := range ddsr {
			if d.TxStatus != constant.TxStatusPrepared {
				continue
			}
			if now.Sub(d.UpdatedAt).Seconds() < 60 {
				continue
			}
			svc.log.WithField("tx_uuid ", d.TxUUID).Info("rollback begin")
			err = svc.dds.TxRollback(coreapi.DataDiskTxRollbackReq{
				TxUUID: d.TxUUID,
			})
			if err != nil {
				svc.log.WithField("err", err).WithField("machine_id ", d.MachineID).WithField("tx_uuid ", d.TxUUID).Error("release data disk stock rollback failed")
				return err
			}
		}
	}

	return nil
}
