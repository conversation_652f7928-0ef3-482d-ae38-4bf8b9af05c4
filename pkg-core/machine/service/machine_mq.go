package service

import (
	"encoding/json"
	message_model "server/pkg-core/message/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func (svc *McService) pubInsertMachine(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBInsert,
		Data:     data,
	}
	return svc.pubModifyMachine(tx, payload)
}

func (svc *McService) pubUpdateMachine(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBUpdate,
		Key:      constant.ModelKeyMachine,
		Data:     data,
	}
	return svc.pubModifyMachine(tx, payload)
}

func (svc *McService) pubUpdateMachineSimple(data map[string]interface{}) error {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBUpdate,
		Key:      constant.ModelKeyMachine,
		Data:     data,
	}
	return svc.pubModifyMachineSimple(payload)
}

func (svc *McService) pubDeleteMachine(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBDelete,
		Key:      constant.ModelKeyMachine,
		Data:     data,
	}
	return svc.pubModifyMachine(tx, payload)
}

// pubModifyMachine 机器信息发生改变时 pub 到 mq，遇到错误内部处理，不需要调用方处理
func (svc *McService) pubModifyMachine(tx *gorm.DB, payload constant.MQCoreBusinessModifyDataPayload) ([]string, db_helper.ModelError) {
	msgUUIDs := make([]string, 0, 2)
	b, _ := json.Marshal(payload)
	for _, s := range constant.GetTenants() {
		msgUUID, errDB := message_model.TxPubMessage(tx, &queue_interface.NewQueueForCoreToBusinessModifyData{
			Tenant: s,
			Req: constant.MQCoreToBusinessModifyDataReq{
				Tenant:  s,
				OptType: constant.MQCoreToBusinessModifyMachineOpt,
				Payload: string(b),
				Caller:  constant.OptCallerModifyMachine,
			},
		})
		if errDB.IsNotNil() {
			return nil, errDB
		}
		msgUUIDs = append(msgUUIDs, msgUUID)
	}
	return msgUUIDs, db_helper.GetModelError(nil)
}

// pubModifyMachineSimple 直接发送 MQ 不依赖 DB
func (svc *McService) pubModifyMachineSimple(payload constant.MQCoreBusinessModifyDataPayload) error {
	var err error
	b, _ := json.Marshal(payload)
	for _, s := range constant.GetTenants() {
		queueData := &queue_interface.NewQueueForCoreToBusinessModifyData{
			Tenant: s,
			Req: constant.MQCoreToBusinessModifyDataReq{
				Tenant:  s,
				OptType: constant.MQCoreToBusinessModifyMachineOpt,
				Payload: string(b),
				Caller:  constant.OptCallerModifyMachine,
			},
		}
		err = svc.q.FastPub(svc.log, queue.ElementPayload{
			Type:    queueData.Type(),
			MsgUUID: uuid.NewV4().String(),
			Payload: queueData.ToString(),
			ValidAt: time.Now(),
		})
		//err = message_model.SimplePubMessage(svc.log, svc.q, &queue_interface.NewQueueForCoreToBusinessModifyData{
		//	Tenant: s,
		//	Req: constant.MQCoreToBusinessModifyDataReq{
		//		Tenant:  s,
		//		OptType: constant.MQCoreToBusinessModifyMachineOpt,
		//		Payload: string(b),
		//		Caller:  constant.OptCallerModifyMachine,
		//	},
		//})
	}
	return err
}
