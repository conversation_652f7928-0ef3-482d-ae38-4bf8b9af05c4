package model

import (
	"gorm.io/gorm"
	"server/pkg/db_helper"
	"time"
)

const TableNameMachineWorkOrderRecord = "core_machine_worker_order_record"

type MachineWorkOrderRecord struct {
	ID            int    `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	MachineID     string `gorm:"type:varchar(255);column:machine_id;NOT NULL" json:"machine_id"`
	WorkOrderUUID string `gorm:"column:work_order_uuid;type:varchar(255);index" json:"work_order_uuid"`
	Describe      string `gorm:"column:describe;default ''" json:"describe"`

	CreatedAt time.Time `gorm:"type:datetime;column:created_at;" json:"created_at"`
}

func (m *MachineWorkOrderRecord) TableName() string {
	return TableNameMachineWorkOrderRecord
}

func (m *MachineWorkOrderRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&MachineWorkOrderRecord{})
}

func (m *MachineWorkOrderRecord) WorkOrderRecordCreate(tx *gorm.DB) (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         m,
		InsertPayload:           m,
	}).GetError()
}
