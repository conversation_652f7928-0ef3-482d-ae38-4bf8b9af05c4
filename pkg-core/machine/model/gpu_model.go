package model

import (
	"server/pkg/constant"
	"time"

	"gorm.io/gorm"
)

const TableNameGpuType = "core_gpu_type"

type GPUType struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	GpuRank               int    `gorm:"type:int;column:gpu_rank; DEFAULT 1" json:"gpu_rank"` // 排序值,gpu市场,排序值越大的排在更前边
	GpuName               string `gorm:"type:varchar(255);column:gpu_name;NOT NULL;" json:"gpu_name"`
	GpuSeriesType         string `gorm:"type:varchar(255);column:gpu_series_type;" json:"gpu_series_type"` // gpu系列类型
	FloatingPointHashRate string `gorm:"type:varchar(255);column:floating_point_hash_rate;" json:"floating_point_hash_rate"`
	GpuMemory             int64  `gorm:"type:bigint(20);column:gpu_memory;" json:"gpu_memory"`                    // 单位为byte
	ChipCorp              string `gorm:"type:varchar(255);column:chip_corp;not null;default ''" json:"chip_corp"` //芯片厂商，nvidia=英伟达，huawei=华为,mthreads=摩尔线程
	CpuArch               string `gorm:"type:varchar(255);column:cpu_arch;not null;default ''" json:"cpu_arch"`   // cpu架构
}

func (m *GPUType) TableName() string {
	return TableNameGpuType
}

// Init 实现 db_helper 接口.
func (m *GPUType) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&GPUType{})
}

func (m *GPUType) Map() map[string]interface{} {
	var deletedAt interface{}
	if m.DeletedAt.Valid {
		deletedAt = m.DeletedAt.Time.Format(constant.FormatTimeString)
	}
	return map[string]interface{}{
		"id":                       m.ID,
		"created_at":               m.CreatedAt.Format(constant.FormatTimeString),
		"updated_at":               m.UpdatedAt.Format(constant.FormatTimeString),
		"gpu_rank":                 m.GpuRank,
		"gpu_name":                 m.GpuName,
		"gpu_series_type":          m.GpuSeriesType,
		"floating_point_hash_rate": m.FloatingPointHashRate,
		"gpu_memory":               m.GpuMemory,
		"deleted_at":               deletedAt,
		"chip_corp":                m.ChipCorp,
		"cpu_arch":                 m.CpuArch,
	}
}

type CreateGpuTypeParams struct {
	GpuRank               int    `json:"gpu_rank"`                 // 排序值
	GpuName               string `json:"gpu_name"`                 // 售卖型号
	GpuMemory             int64  `json:"gpu_memory"`               // 显存
	GpuSeriesType         string `json:"gpu_series_type"`          // 系列类型
	FloatingPointHashRate string `json:"floating_point_hash_rate"` // 浮点算力
	ChipCorp              string `json:"chip_corp"`                // 芯片厂商，nvidia，huawei
	CpuArch               string `json:"cpu_arch"`                 // cpu架构
}

type GpuTypeInfo struct {
	CreateGpuTypeParams
	GPUTypeID int `json:"gpu_type_id"`
}
