package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-core/machine/model"
	"server/pkg/businesserror"
	"server/pkg/http"
)

func (ctrl *MachineController) WorkOrderCreate(c *gin.Context) {
	var req model.WorkOrderCreateParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	workOrder, err := ctrl.machineService.WorkOrderCreate(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, workOrder)
}

func (ctrl *MachineController) WorkOrderUpdate(c *gin.Context) {
	var req model.WorkOrderUpdateParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.machineService.WorkOrderUpdate(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *MachineController) WorkOrderList(c *gin.Context) {
	var req model.WorkOrderListParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	paged, err := ctrl.machineService.WorkOrderList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, paged)
}

func (ctrl *MachineController) WorkOrderRecordCreate(c *gin.Context) {
	var req model.WorkOrderRecordCreateParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.machineService.WorkOrderRecordCreate(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *MachineController) WorkOrderRecordList(c *gin.Context) {
	var req model.WorkOrderRecordListParams
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	paged, err := ctrl.machineService.WorkOrderRecordList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, paged)
}
