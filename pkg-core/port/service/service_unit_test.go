package service_test

import (
	"context"
	"fmt"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"server/conf/test"
	"server/entrance/initialize"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	portModel "server/pkg-core/port/model"
	portService "server/pkg-core/port/service"
	regionModel "server/pkg-core/region/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"server/plugin/redis_plugin"
	"sync"
	"time"
)

var _ = Describe("PortServiceTest", func() {
	test.InitConfigForTest()

	pkg, err := initialize.InitServerDependency()
	if err != nil {
		panic(err)
		return
	}

	var mutexRedis = redis_plugin.NewMutexRedisProvider(pkg.RedisClient)
	var portPlugin = redis_plugin.NewPortPlugin(pkg.RedisClient)
	var regionSvcEntity = NewFooRegion()
	var portSvcEntity = portService.NewPortServiceProvider(portPlugin, regionSvcEntity, mutexRedis)

	// init foo regions
	var rA constant.RegionSignType = "test-A"
	var rB constant.RegionSignType = "test-B"
	var rC constant.RegionSignType = "test-C"

	regions := []constant.RegionSignType{rA, rB, rC}

	BeforeEach(func() {
		// clear all test ports & 3 region
		err = db_helper.GlobalDBConn().Model(&portModel.Port{}).Debug().Unscoped().Where("id > 0").Delete(&portModel.Port{}).Error
		Ω(err).Should(BeNil())

		err = db_helper.GlobalDBConn().Model(&regionModel.Region{}).Debug().Unscoped().Where("sign in (?)", regions).Delete(&regionModel.Region{}).Error
		Ω(err).Should(BeNil())
	})

	Describe("Describe.", func() {
		Context("Context", func() {
			/*
			 * 测试正常状态下端口分配能力, 并检测是否同一地区下分配了相同端口.
			 */
			It("Test normal port service.", func() {
				// init range
				var max = 45000
				var min = 44900

				_, _ = regionSvcEntity.CreateRegion(&regionModel.Region{
					Sign:         rA,
					PortRangeMin: min,
					PortRangeMax: max,
				})
				_, _ = regionSvcEntity.CreateRegion(&regionModel.Region{
					Sign:         rB,
					PortRangeMin: min,
					PortRangeMax: max,
				})
				_, _ = regionSvcEntity.CreateRegion(&regionModel.Region{
					Sign:         rC,
					PortRangeMin: min,
					PortRangeMax: max,
				})

				// init ports
				err = portSvcEntity.SyncIdlePortsFromDBToRedis(context.Background())
				Ω(err).Should(BeNil())

				// start test
				availablePortsGroupEachRegion := (max - min + 1) / 3

				regions, _ := regionSvcEntity.GetRegionList()

				// distribute ports
				var groupA []portModel.InstancePortDistributor
				var groupB []portModel.InstancePortDistributor
				var groupC []portModel.InstancePortDistributor

				var chanA = make(chan portModel.InstancePortDistributor, 1000)
				var chanB = make(chan portModel.InstancePortDistributor, 1000)
				var chanC = make(chan portModel.InstancePortDistributor, 1000)

				var wg sync.WaitGroup
				wg.Add(len(regions) * 2)

				go func() {
					for _, region := range regions {
						r := region
						go func() {
							for j := 0; j < availablePortsGroupEachRegion; j++ {
								res, err := portSvcEntity.RequirePortsForInstance(r.Sign, uuid.NewV4().String(), false)
								Ω(err).Should(BeNil())

								switch r.Sign {
								case rA:
									chanA <- res
								case rB:
									chanB <- res
								case rC:
									chanC <- res
								}
							}
							switch r.Sign {
							case rA:
								close(chanA)
							case rB:
								close(chanB)
							case rC:
								close(chanC)
							}
							wg.Done()
						}()
					}
				}()

				go func() {
					var done bool
					for {
						if done {
							break
						}
						select {
						case r, ok := <-chanA:
							if !ok {
								done = true
								break
							}
							groupA = append(groupA, r)
						}
					}
					wg.Done()
				}()

				go func() {
					var done bool
					for {
						if done {
							break
						}
						select {
						case r, ok := <-chanB:
							if !ok {
								done = true
								break
							}
							groupB = append(groupB, r)
						}
					}
					wg.Done()
				}()

				go func() {
					var done bool
					for {
						if done {
							break
						}
						select {
						case r, ok := <-chanC:
							if !ok {
								done = true
								break
							}
							groupC = append(groupC, r)
						}
					}
					wg.Done()
				}()

				wg.Wait()

				// then port is exhausted.
				_, err = portSvcEntity.RequirePortsForInstance(rA, uuid.NewV4().String(), false)
				Ω(err).ShouldNot(BeNil())
				_, err = portSvcEntity.RequirePortsForInstance(rB, uuid.NewV4().String(), false)
				Ω(err).ShouldNot(BeNil())
				_, err = portSvcEntity.RequirePortsForInstance(rC, uuid.NewV4().String(), false)
				Ω(err).ShouldNot(BeNil())

				// check conflict ports
				// TODO: 优化这种检测重复的方法
				Ω(len(groupA)).Should(Equal(availablePortsGroupEachRegion))
				Ω(len(groupB)).Should(Equal(availablePortsGroupEachRegion))
				Ω(len(groupC)).Should(Equal(availablePortsGroupEachRegion))

				var dupPortMapA = make(map[int]bool)
				var dupPortMapB = make(map[int]bool)
				var dupPortMapC = make(map[int]bool)

				for _, g := range groupA {
					var ok bool
					_, ok = dupPortMapA[g.SSHPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapA[g.JupyterPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapA[g.TensorboardPort]
					Ω(ok).Should(BeFalse())

					dupPortMapA[g.SSHPort] = true
					dupPortMapA[g.JupyterPort] = true
					dupPortMapA[g.TensorboardPort] = true
				}

				for _, g := range groupB {
					var ok bool
					_, ok = dupPortMapB[g.SSHPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapB[g.JupyterPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapB[g.TensorboardPort]
					Ω(ok).Should(BeFalse())

					dupPortMapA[g.SSHPort] = true
					dupPortMapA[g.JupyterPort] = true
					dupPortMapA[g.TensorboardPort] = true
				}

				for _, g := range groupC {
					var ok bool
					_, ok = dupPortMapC[g.SSHPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapC[g.JupyterPort]
					Ω(ok).Should(BeFalse())

					_, ok = dupPortMapC[g.TensorboardPort]
					Ω(ok).Should(BeFalse())

					dupPortMapA[g.SSHPort] = true
					dupPortMapA[g.JupyterPort] = true
					dupPortMapA[g.TensorboardPort] = true
				}
			})

			/*
			 * 测试出现冲突端口时的处理, 先正常分配一个 uuid1, 再在数据库中插入冲突记录 uuid2,
			 * 当 uuid2 再次查询端口时, 会检测到冲突并给 uuid2 分配新端口.
			 */
			It("Test conflict port service.", func() {
				// init range, only 6 ports
				var max = 45000
				var min = 44995

				// init foo regions
				_, _ = regionSvcEntity.CreateRegion(&regionModel.Region{
					Sign:         rA,
					PortRangeMin: min,
					PortRangeMax: max,
				})

				// init ports
				err = portSvcEntity.SyncIdlePortsFromDBToRedis(context.Background())
				Ω(err).Should(BeNil())

				// start test

				// require 3 ports
				res, err := portSvcEntity.RequirePortsForInstance(rA, uuid.NewV4().String(), true)
				Ω(err).Should(BeNil())

				// build conflict records
				fooConflictUUID := uuid.NewV4().String()

				err = db_helper.InsertOne(db_helper.QueryDefinition{
					ModelDefinition: &portModel.Port{},
					InsertPayload: &portModel.Port{
						PortNumber:  res.SSHPort,
						ProductUUID: fooConflictUUID,
						PortRegion:  rA,
					},
				}).GetError()
				Ω(err).Should(BeNil())

				err = db_helper.InsertOne(db_helper.QueryDefinition{
					ModelDefinition: &portModel.Port{},
					InsertPayload: &portModel.Port{
						PortNumber:  res.JupyterPort,
						ProductUUID: fooConflictUUID,
						PortRegion:  rA,
					},
				}).GetError()
				Ω(err).Should(BeNil())

				err = db_helper.InsertOne(db_helper.QueryDefinition{
					ModelDefinition: &portModel.Port{},
					InsertPayload: &portModel.Port{
						PortNumber:  res.TensorboardPort,
						ProductUUID: fooConflictUUID,
						PortRegion:  rA,
					},
				}).GetError()
				Ω(err).Should(BeNil())

				// check conflict ports
				newRes, err := portSvcEntity.RequirePortsForInstance(rA, fooConflictUUID, false)
				Ω(err).Should(BeNil())

				fmt.Printf(">>> old normal ports: %+v\n", res)
				fmt.Printf(">>> new ports after conflict check: %+v\n", newRes)

				var containsMap = make(map[int]bool)
				containsMap[res.SSHPort] = true
				containsMap[res.JupyterPort] = true
				containsMap[res.TensorboardPort] = true

				var ok bool
				_, ok = containsMap[newRes.SSHPort]
				Ω(ok).Should(BeFalse())

				_, ok = containsMap[newRes.JupyterPort]
				Ω(ok).Should(BeFalse())

				_, ok = containsMap[newRes.TensorboardPort]
				Ω(ok).Should(BeFalse())
			})

			/*
			 * 测试一个 uuid 反复申请端口的情况, 此时应该返回和上此相同的端口.
			 */
			It("Test reuse port service.", func() {
				// init range, only 3 ports
				var max = 45000
				var min = 44998

				// init foo regions
				_, _ = regionSvcEntity.CreateRegion(&regionModel.Region{
					Sign:         rA,
					PortRangeMin: min,
					PortRangeMax: max,
				})

				// init ports
				err = portSvcEntity.SyncIdlePortsFromDBToRedis(context.Background())
				Ω(err).Should(BeNil())

				// start test
				var sameUUID = uuid.NewV4().String()

				// require 3 ports
				res, err := portSvcEntity.RequirePortsForInstance(rA, sameUUID, false)
				Ω(err).Should(BeNil())

				// require 3 ports again
				res2, err := portSvcEntity.RequirePortsForInstance(rA, sameUUID, false)
				Ω(err).Should(BeNil())

				fmt.Printf(">>> get ports: %+v\n", res)
				fmt.Printf(">>> get ports again: %+v\n", res2)

				Ω(res.SSHPort).Should(Equal(res2.SSHPort))
				Ω(res.JupyterPort).Should(Equal(res2.JupyterPort))
				Ω(res.TensorboardPort).Should(Equal(res2.TensorboardPort))
			})
		})
	})
})

// ---------------------------------------------------------------------------------------------------------------------

type FooRegion struct {
	dbLock sync.Mutex
	index  int
	fooDB  sync.Map
}

func (f *FooRegion) GetFileStorageAdminList(req coreapi.GetFileStorageAdminListReq) (coreapi.GetFileStorageAdminListData, error) {
	panic("implement me")
}

func (f *FooRegion) UpdateFileStorageSetting(req coreapi.UpdateFileStorageSettingReq) error {
	panic("implement me")
}

func (f *FooRegion) SyncFileStorageUsageInfo(req constant.ADFSSyncUsage) error {
	panic("implement me")
}

func (f *FooRegion) InitNetDiskWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *regionModel.NetDisk, err error) {
	panic("implement me")
}

func (f *FooRegion) GetUserNetDiskMountForInstance(uid int, rs constant.RegionSignType) (res regionModel.GetUserNetDiskMountForInstanceRes, err error) {
	panic("implement me")
}

func (f *FooRegion) GetUserNetDiskQuotaList(req coreapi.GetUserNetDiskQuotaListReq) (paged *db_helper.PagedData, list []regionModel.UserNetDiskQuotaForAdminInfo, err error) {
	panic("implement me")
}

func (f *FooRegion) GetNetDisk(uid int, rs constant.RegionSignType) (netDisk *regionModel.NetDisk, err error) {
	panic("implement me")
}

func (f *FooRegion) InitFileStorageWithRegionForUser(uid int, rs constant.RegionSignType, tenant string) (ru *regionModel.FileStorage, err error) {
	panic("implement me")
}

func (f *FooRegion) GetFileStorageListForUser(uid int) (res []regionModel.RegionFileStorageInfo, err error) {
	panic("implement me")
}

func (f *FooRegion) GetFileStorageByUids(uids []int, rs constant.RegionSignType) (fs []regionModel.FileStorage, err error) {
	panic("implement me")
}

func (f *FooRegion) GetFileStorageByUid(uid int, rs constant.RegionSignType) (fs regionModel.FileStorage, err error) {
	panic("implement me")
}

func (f *FooRegion) GetFileStorageByTenant(tenant string, rs constant.RegionSignType) (fs []regionModel.FileStorage, err error) {
	panic("implement me")
}

func (f *FooRegion) InitFileStorageFinal(rs constant.RegionSignType, uid int, isSuccess bool, errInfo string) {
	panic("implement me")
}

func NewFooRegion() *FooRegion {
	return &FooRegion{}
}

func (f *FooRegion) CreateRegion(newRegion *regionModel.Region) (*regionModel.Region, error) {
	f.fooInsert(newRegion)
	return newRegion, nil
}

func (f *FooRegion) GetRegionDetail(rs constant.RegionSignType) (*regionModel.Region, error) {
	region, err := f.fooGetOne(rs)
	return &region, err
}

func (f *FooRegion) GetRegionList() ([]regionModel.Region, error) {
	regions, err := f.fooGetAll()
	return regions, err
}

// -----------------------------------------------

func (f *FooRegion) CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) {
	panic("implement me")
}

func (f *FooRegion) CheckRegionNameExist(name string) (exist bool, err error) {
	panic("implement me")
}

func (f *FooRegion) AuthAgent(token string) (err error) {
	panic("implement me")
}

func (f *FooRegion) RegisterAgent(rs constant.RegionSignType, token string) error {
	panic("implement me")
}

func (f *FooRegion) CheckAgentRealHealth(rs constant.RegionSignType) bool {
	panic("implement me")
}

func (f *FooRegion) SetStorageAgentOnlineOnce(rs constant.RegionSignType) {
	panic("implement me")
}

func (f *FooRegion) ConfirmRegionUserNetDiskInDB(rs constant.RegionSignType, uid int, isSuccess bool, errInfo string, quota int64) {
	panic("implement me")
}

func (f *FooRegion) ConfirmAdminSetQuota(rs constant.RegionSignType, uid int, isSuccess bool, errInfo string, quota int64) {
	panic("implement me")
}

func (f *FooRegion) GetNetDiskListForUser(uid int) (res []regionModel.RegionUsageInfo, err error) {
	panic("implement me")
}

func (f *FooRegion) GetRegionListForIndex() (res []regionModel.GetRegionListForIndexRes, err error) {
	panic("implement me")
}

func (f *FooRegion) GetSetQuotaMessage(sign constant.RegionSignType, uid int, quota int64, operateType messenger.MessageType) (msg *queue_interface.NewQueueForStorageAgentOnMachine, err error) {
	panic("implement me")
}

func (f *FooRegion) NetDiskSetQuota(Sign constant.RegionSignType, uid int, quota int64) error {
	panic("implement me")
}

func (f *FooRegion) CronJobRegister(ctx context.Context) {
	panic("implement me")
}

func (f *FooRegion) MsgRegister() []queue.RegisterInfo {
	panic("implement me")
}

func (f *FooRegion) GuardCronJobRegister(ctx context.Context, regionSign constant.RegionSignType, writer chan<- messenger.Message) {
	panic("implement me")
}

func (f *FooRegion) SyncStorageUsage(msg *constant.StorageSyncUsageModel) {
	panic("implement me")
}

func (f *FooRegion) SyncDiskAlerts(msg *constant.StorageSyncDiskAlertsModel) {
	panic("implement me")
}

func (f *FooRegion) DistributeFrpcProxy(rs constant.RegionSignType) (proxyHost string, proxyPort, proxyApiPort int, proxyToken string, region *regionModel.Region, err error) {
	panic("implement me")
}

func (f *FooRegion) GetRegionDetailWithStorageInfo(rs constant.RegionSignType) (*regionModel.Region, regionModel.RegionStorageOSSDetailList, error) {
	return nil, nil, nil
}
func (f *FooRegion) GetStorageDetail(rs agent_constant.StorageOSSSignType) (*regionModel.StorageOSS, error) {
	return nil, nil
}

// ---------------------------------------

func (f *FooRegion) fooInsert(newRegion *regionModel.Region) {
	if newRegion == nil {
		return
	}

	f.dbLock.Lock()
	defer f.dbLock.Unlock()

	newRegion.ID = f.index
	f.index++

	nowAt := time.Now()
	newRegion.CreatedAt = nowAt
	newRegion.UpdatedAt = nowAt

	f.fooDB.Store(newRegion.Sign, *newRegion)
}

func (f *FooRegion) fooGetOne(rs constant.RegionSignType) (regionModel.Region, error) {
	if region, ok := f.fooDB.Load(rs); ok {
		if r, ok := region.(regionModel.Region); ok {
			return r, nil
		}
	}
	return regionModel.Region{}, gorm.ErrRecordNotFound
}

func (f *FooRegion) fooGetAll() ([]regionModel.Region, error) {
	var regions []regionModel.Region
	f.fooDB.Range(func(key, value interface{}) bool {
		if r, ok := value.(regionModel.Region); ok {
			regions = append(regions, r)
		}
		return true
	})
	return regions, nil
}
