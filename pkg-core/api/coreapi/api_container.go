package coreapi

import (
	"encoding/json"
	"net/http"
	containerModel "server/pkg-core/container_runtime/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type CheckContainerExistUUIDReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type CheckContainerExistUUIDRes struct {
	ResBase
	Data CheckContainerExistUUIDData `json:"data"`
}

type CheckContainerExistUUIDData struct {
	Exist bool `json:"exist"`
}

func (api *Api) CheckContainerExistUUID(req CheckContainerExistUUIDReq, client *http.Client) (CheckContainerExistUUIDRes, error) {
	body, err := api.post(req, "/api/v1/container/check_exist_uuid", client)
	if err != nil {
		return CheckContainerExistUUIDRes{}, err
	}
	var res CheckContainerExistUUIDRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckContainerExistUUIDRes{}, err
	}
	return res, nil
}

//

type GetContainerRecordReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerRecordRes struct {
	ResBase
	Data containerModel.Container `json:"data"`
}

func (api *Api) GetContainerRecord(req GetContainerRecordReq, client *http.Client) (GetContainerRecordRes, error) {
	body, err := api.post(req, "/api/v1/container/get_record", client)
	if err != nil {
		return GetContainerRecordRes{}, err
	}
	var res GetContainerRecordRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerRecordRes{}, err
	}
	return res, nil
}

//

type GetContainerRecordUnScopedReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerRecordUnScopedRes struct {
	ResBase
	Data containerModel.Container `json:"data"`
}

func (api *Api) GetContainerRecordUnScoped(req GetContainerRecordUnScopedReq, client *http.Client) (GetContainerRecordUnScopedRes, error) {
	body, err := api.post(req, "/api/v1/container/get_record_unscoped", client)
	if err != nil {
		return GetContainerRecordUnScopedRes{}, err
	}
	var res GetContainerRecordUnScopedRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerRecordUnScopedRes{}, err
	}
	return res, nil
}

//

type GetContainerRecordsReq struct {
	RuntimeUUIDs []constant.ContainerRuntimeUUID `json:"runtime_uuids"`
}

type GetContainerRecordsRes struct {
	ResBase
	Data []containerModel.Container `json:"data"`
}

func (api *Api) GetContainerRecords(req GetContainerRecordsReq, client *http.Client) (GetContainerRecordsRes, error) {
	body, err := api.post(req, "/api/v1/container/get_records", client)
	if err != nil {
		return GetContainerRecordsRes{}, err
	}
	var res GetContainerRecordsRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerRecordsRes{}, err
	}
	return res, nil
}

//

type GetContainerSimplifiedInfoListReq struct {
	RuntimeUUIDs []constant.ContainerRuntimeUUID `json:"runtime_uuids"`
	TaskType     constant.ContainerRuntimeType   `json:"task_type"`
}

type GetContainerSimplifiedInfoListRes struct {
	ResBase
	Data []*containerModel.ContainerSimplifiedInfo `json:"data"`
}

func (api *Api) GetContainerSimplifiedInfoList(req GetContainerSimplifiedInfoListReq, client *http.Client) (GetContainerSimplifiedInfoListRes, error) {
	body, err := api.post(req, "/api/v1/container/get_simplified_info_list", client)
	if err != nil {
		return GetContainerSimplifiedInfoListRes{}, err
	}
	var res GetContainerSimplifiedInfoListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerSimplifiedInfoListRes{}, err
	}
	return res, nil
}

type GetContainerSimplifiedInfoReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
	TaskType    constant.ContainerRuntimeType `json:"task_type"`
}

type GetContainerSimplifiedInfoRes struct {
	ResBase
	Data *containerModel.ContainerSimplifiedInfo `json:"data"`
}

func (api *Api) GetContainerSimplifiedInfo(req GetContainerSimplifiedInfoReq, client *http.Client) (GetContainerSimplifiedInfoRes, error) {
	body, err := api.post(req, "/api/v1/container/get_simplified_info", client)
	if err != nil {
		return GetContainerSimplifiedInfoRes{}, err
	}
	var res GetContainerSimplifiedInfoRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerSimplifiedInfoRes{}, err
	}
	return res, nil
}

type ForceOperateContainerReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
	TaskType    constant.ContainerRuntimeType `json:"task_type"`
	IsStop      bool                          `json:"is_stop"`
}

type ForceOperateContainerRes struct {
	ResBase
	Data *containerModel.ContainerSimplifiedInfo `json:"data"`
}

func (api *Api) ForceOperateContainer(req ForceOperateContainerReq, client *http.Client) (ForceOperateContainerRes, error) {
	body, err := api.post(req, "/api/v1/container/force_operate", client)
	if err != nil {
		return ForceOperateContainerRes{}, err
	}
	var res ForceOperateContainerRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ForceOperateContainerRes{}, err
	}
	return res, nil
}

type GetContainerSimplifiedInfoListWithUsage struct {
	ResBase
	Data []*containerModel.ContainerSimplifiedInfoWithUsage `json:"data"`
}

func (api *Api) GetContainerSimplifiedInfoListWithUsage(req GetContainerSimplifiedInfoListReq, client *http.Client) (GetContainerSimplifiedInfoListWithUsage, error) {
	body, err := api.post(req, "/api/v1/container/get_simplified_info_list_with_usage", client)
	if err != nil {
		return GetContainerSimplifiedInfoListWithUsage{}, err
	}
	var res GetContainerSimplifiedInfoListWithUsage
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerSimplifiedInfoListWithUsage{}, err
	}
	return res, nil
}

//

type CheckContainerStatusNumReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID  `json:"runtime_uuid"`
	Status      []constant.ContainerStatusType `json:"status"`
}

type CheckContainerStatusNumRes struct {
	ResBase
	Data CheckContainerStatusNumData `json:"data"`
}

type CheckContainerStatusNumData struct {
	Count int64 `json:"count"`
}

func (api *Api) CheckContainerStatusNum(req CheckContainerStatusNumReq, client *http.Client) (CheckContainerStatusNumRes, error) {
	body, err := api.post(req, "/api/v1/container/check_status_num", client)
	if err != nil {
		return CheckContainerStatusNumRes{}, err
	}
	var res CheckContainerStatusNumRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckContainerStatusNumRes{}, err
	}
	return res, nil
}

//

type GetContainerOperateHistoryReq struct {
	RuntimeUUIDs []constant.ContainerRuntimeUUID `json:"runtime_uuids"`
	OperateType  []constant.OptType              `json:"operate_type"`
	Paged        *db_helper.GetPagedRangeRequest `json:"paged"`
}

type GetContainerOperateHistoryRes struct {
	ResBase
	Data GetContainerOperateHistoryData `json:"data"`
}

type GetContainerOperateHistoryData struct {
	Paged *db_helper.PagedData
	List  []containerModel.OperateHistory
}

func (api *Api) GetContainerOperateHistory(req GetContainerOperateHistoryReq, client *http.Client) (GetContainerOperateHistoryRes, error) {
	body, err := api.post(req, "/api/v1/container/get_operate_history", client)
	if err != nil {
		return GetContainerOperateHistoryRes{}, err
	}
	var res GetContainerOperateHistoryRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerOperateHistoryRes{}, err
	}
	return res, nil
}

type GetContainerStatusHistoryRes struct {
	ResBase
	Data GetContainerStatusHistoryData `json:"data"`
}

type GetContainerStatusHistoryData struct {
	List []containerModel.StatusHistory
}

type GetContainerStatusHistoryReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type CreateContainerMigHistoryReq struct {
	UID               int                           `json:"uid"`
	RuntimeType       constant.ContainerRuntimeType `json:"runtime_type"`
	SourceRuntimeUUID constant.ContainerRuntimeUUID `json:"source_runtime_uuid"`
	SourceMachineID   string                        `json:"source_machine_id"`
	SourceRegionSign  constant.RegionSignType       `json:"source_region_sign"`
	SourceProductUUID string                        `json:"source_product_uuid"`
	TargetRuntimeUUID constant.ContainerRuntimeUUID `json:"target_run"`
	TargetMachineID   string                        `json:"target_machine_id"`
	TargetRegionSign  constant.RegionSignType       `json:"target_region_sign"`
	TargetProductUUID string                        `json:"target_product_uuid"`
}

type CreateContainerMigHistoryRes struct {
	ResBase
	Data CreateContainerMigHistoryData `json:"data"`
}

type CreateContainerMigHistoryData struct {
	MigID int `json:"mig_id"`
}

func (api *Api) CreateContainerMigHistory(req CreateContainerMigHistoryReq, client *http.Client) (CreateContainerMigHistoryRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/create", client)
	if err != nil {
		return CreateContainerMigHistoryRes{}, err
	}
	var res CreateContainerMigHistoryRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CreateContainerMigHistoryRes{}, err
	}
	return res, nil
}

//

type CheckMigHistoryInMigratingStatusReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type CheckMigHistoryInMigratingStatusRes struct {
	ResBase
	Data CheckMigHistoryInMigratingStatusData `json:"data"`
}

type CheckMigHistoryInMigratingStatusData struct {
	Mig   *containerModel.MigrationHistory
	Exist bool `json:"exist"`
}

func (api *Api) CheckContainerMigHistoryInMigratingStatus(req CheckMigHistoryInMigratingStatusReq, client *http.Client) (CheckMigHistoryInMigratingStatusRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/check_in_migrating_status", client)
	if err != nil {
		return CheckMigHistoryInMigratingStatusRes{}, err
	}
	var res CheckMigHistoryInMigratingStatusRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CheckMigHistoryInMigratingStatusRes{}, err
	}
	return res, nil
}

//

type UpdateContainerMigHistoryResultReq struct {
	MigID  int                                     `json:"mig_id"`
	Status containerModel.MigrateHistoryStatusType `json:"status"`
	Msg    string                                  `json:"msg"`
}

type UpdateContainerMigHistoryResultRes struct {
	ResBase
}

func (api *Api) UpdateContainerMigHistoryResult(req UpdateContainerMigHistoryResultReq, client *http.Client) (UpdateContainerMigHistoryResultRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/update", client)
	if err != nil {
		return UpdateContainerMigHistoryResultRes{}, err
	}
	var res UpdateContainerMigHistoryResultRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return UpdateContainerMigHistoryResultRes{}, err
	}
	return res, nil
}

//

type RetryContainerMigHistoryReq struct {
	MigID int `json:"mig_id"`
}

type RetryContainerMigHistoryRes struct {
	ResBase
}

func (api *Api) RetryContainerMigHistory(req RetryContainerMigHistoryReq, client *http.Client) (RetryContainerMigHistoryRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/retry", client)
	if err != nil {
		return RetryContainerMigHistoryRes{}, err
	}
	var res RetryContainerMigHistoryRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return RetryContainerMigHistoryRes{}, err
	}
	return res, nil
}

//

type InsertContainerMigHistoryReq struct {
	Mig containerModel.MigrationHistory `json:"mig"`
}

type InsertContainerMigHistoryRes struct {
	ResBase
	Data InsertContainerMigHistoryData `json:"data"`
}

type InsertContainerMigHistoryData struct {
	MigID int `json:"mig_id"`
}

func (api *Api) InsertContainerMigHistory(req InsertContainerMigHistoryReq, client *http.Client) (InsertContainerMigHistoryRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/insert", client)
	if err != nil {
		return InsertContainerMigHistoryRes{}, err
	}
	var res InsertContainerMigHistoryRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return InsertContainerMigHistoryRes{}, err
	}
	return res, nil
}

//

type GetContainerMigrateByTargetRuntimeUUIDReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerMigrateByTargetRuntimeUUIDRes struct {
	ResBase
	Data *containerModel.MigrationHistory `json:"data"`
}

func (api *Api) GetContainerMigrateByTargetRuntimeUUID(req GetContainerMigrateByTargetRuntimeUUIDReq, client *http.Client) (GetContainerMigrateByTargetRuntimeUUIDRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/get_by_target_runtime_uuid", client)
	if err != nil {
		return GetContainerMigrateByTargetRuntimeUUIDRes{}, err
	}
	var res GetContainerMigrateByTargetRuntimeUUIDRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerMigrateByTargetRuntimeUUIDRes{}, err
	}
	return res, nil
}

type GetContainerMigrateListReq struct {
	UID     int                            `json:"uid"`
	PageReq db_helper.GetPagedRangeRequest `json:"page_req"`
}

type GetContainerMigrateListRes struct {
	ResBase
	Data db_helper.PagedData `json:"data"`
}

func (api *Api) GetContainerMigrateList(req GetContainerMigrateListReq, client *http.Client) (GetContainerMigrateListRes, error) {
	body, err := api.post(req, "/api/v1/container/migrate/list", client)
	if err != nil {
		return GetContainerMigrateListRes{}, err
	}
	var res GetContainerMigrateListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerMigrateListRes{}, err
	}
	return res, nil
}

//

type OperateContainerReq struct {
	Opt []constant.OptContainerReq `json:"opt"`
}

type OperateContainerRes struct {
	ResBase
}

func (api *Api) OperateContainer(req OperateContainerReq, client *http.Client) (OperateContainerRes, error) {
	body, err := api.post(req, "/api/v1/container/operate", client)
	if err != nil {
		return OperateContainerRes{}, err
	}
	var res OperateContainerRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return OperateContainerRes{}, err
	}
	return res, nil
}

//

type OperateContainerByPubMQReq struct {
	Opt constant.OptContainerReq `json:"opt"`
}

type OperateContainerByPubMQRes struct {
	ResBase
}

func (api *Api) OperateContainerByPubMQ(req OperateContainerByPubMQReq, client *http.Client) (OperateContainerByPubMQRes, error) {
	body, err := api.post(req, "/api/v1/container/operate_by_pub_mq", client)
	if err != nil {
		return OperateContainerByPubMQRes{}, err
	}
	var res OperateContainerByPubMQRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return OperateContainerByPubMQRes{}, err
	}
	return res, nil
}

type GetContainerUsageInfoReq struct {
	RuntimeUUID constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerUsageInfoRes struct {
	ResBase
	Data constant.ContainerUsageInfo
}

func (api *Api) GetContainerUsageInfo(req GetContainerUsageInfoReq, client *http.Client) (GetContainerUsageInfoRes, error) {
	body, err := api.post(req, "/api/v1/container/get_container_usage_info", client)
	if err != nil {
		return GetContainerUsageInfoRes{}, err
	}
	var res GetContainerUsageInfoRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerUsageInfoRes{}, err
	}
	return res, nil

}

type GetContainerUsageInfoListReq struct {
	RuntimeUUIDList []constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerUsageInfoListRes struct {
	ResBase
	Data map[constant.ContainerRuntimeUUID]constant.ContainerUsageInfo
}

func (api *Api) GetContainerUsageInfoList(req GetContainerUsageInfoListReq, client *http.Client) (GetContainerUsageInfoListRes, error) {
	body, err := api.post(req, "/api/v1/container/get_container_usage_info_list", client)
	if err != nil {
		return GetContainerUsageInfoListRes{}, err
	}
	var res GetContainerUsageInfoListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerUsageInfoListRes{}, err
	}
	return res, nil

}

type GetContainerRunningErrRuntimeUuidListReq struct {
	RuntimeUUIDList []constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type GetContainerRunningErrRuntimeUuidListRes struct {
	ResBase
	Data []constant.ContainerRuntimeUUID
}

func (api *Api) GetContainerStopErrRuntimeUuidList(req GetContainerRunningErrRuntimeUuidListReq, client *http.Client) (GetContainerRunningErrRuntimeUuidListRes, error) {
	body, err := api.post(req, "/api/v1/container/get_container_running_error_runtime_uuid_list", client)
	if err != nil {
		return GetContainerRunningErrRuntimeUuidListRes{}, err
	}
	var res GetContainerRunningErrRuntimeUuidListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetContainerRunningErrRuntimeUuidListRes{}, err
	}
	return res, nil
}

type RemoveContainerCacheReq struct {
	AssignHourNum int64 `json:"assign_hour_num"`
}

type RemoveContainerCacheRes struct {
	ResBase
}

func (api *Api) RemoveContainerCache(req RemoveContainerCacheReq, client *http.Client) (RemoveContainerCacheRes, error) {
	body, err := api.post(req, "/api/v1/container/remove_cache", client)
	if err != nil {
		return RemoveContainerCacheRes{}, err
	}
	var res RemoveContainerCacheRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return RemoveContainerCacheRes{}, err
	}
	return res, nil
}

type ContainerCacheRecoveryReq struct {
	RuntimeUuid constant.ContainerRuntimeUUID `json:"runtime_uuid"`
}

type ContainerCacheRecoveryRes struct {
	ResBase
}

func (api *Api) ContainerCacheRecovery(req ContainerCacheRecoveryReq, client *http.Client) (ContainerCacheRecoveryRes, error) {
	body, err := api.post(req, "/api/v1/container/recovery", client)
	if err != nil {
		return ContainerCacheRecoveryRes{}, err
	}
	var res ContainerCacheRecoveryRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ContainerCacheRecoveryRes{}, err
	}
	return res, nil
}

type ContainerDeleteByMachineReq struct {
	MachineID string `json:"machine_id"`
}

type ContainerDeleteByMachineRes struct {
	ResBase
}

func (api *Api) ContainerDeleteByMachine(req ContainerDeleteByMachineReq, client *http.Client) (ContainerDeleteByMachineRes, error) {
	body, err := api.post(req, "/api/v1/container/delete_by_machine", client)
	if err != nil {
		return ContainerDeleteByMachineRes{}, err
	}
	var res ContainerDeleteByMachineRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ContainerDeleteByMachineRes{}, err
	}
	return res, nil
}
