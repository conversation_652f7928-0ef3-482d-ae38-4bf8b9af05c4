package coreapi

import (
	"encoding/json"
	"net/http"
	gpuStockModel "server/pkg-core/gpu_stock/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

type CreateGpuStockReq []*gpuStockModel.RegisterGpuStockParam

type CreateGpuStockRes struct {
	ResBase
}

func (api *Api) CreateGpuStock(req CreateGpuStockReq, client *http.Client) (CreateGpuStockRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/create", client)
	if err != nil {
		return CreateGpuStockRes{}, err
	}
	var res CreateGpuStockRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CreateGpuStockRes{}, err
	}
	return res, nil
}

//

type GpuReserveReq *gpuStockModel.GpuReserve

type GpuReserveRes struct {
	ResBase
	Data GpuReserveData `json:"data"`
}

type GpuReserveData struct {
	OK bool `json:"ok"`
}

func (api *Api) GpuReserve(req GpuReserveReq, client *http.Client) (GpuReserveRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/reserve", client)
	if err != nil {
		return GpuReserveRes{}, err
	}
	var res GpuReserveRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuReserveRes{}, err
	}
	return res, nil
}

//

type GpuReserveCommitReq struct {
	RuntimeUUID string `json:"runtime_uuid"`
	MachineID   string `json:"machine_id"`
	Timestamp   int64  `json:"timestamp"`
}

type GpuReserveCommitRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) GpuReserveCommit(req GpuReserveCommitReq, client *http.Client) (GpuReserveCommitRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/reserve_commit", client)
	if err != nil {
		return GpuReserveCommitRes{}, err
	}
	var res GpuReserveCommitRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuReserveCommitRes{}, err
	}
	return res, nil
}

//

type GpuReleaseReq *gpuStockModel.GpuRelease

type GpuReleaseRes struct {
	ResBase
}

func (api *Api) GpuRelease(req GpuReleaseReq, client *http.Client) (GpuReleaseRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/release", client)
	if err != nil {
		return GpuReleaseRes{}, err
	}
	var res GpuReleaseRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuReleaseRes{}, err
	}
	return res, nil
}

//

type GetGpuStockUsedReq gpuStockModel.GetGpuStockUsed

type GetGpuStockUsedRes struct {
	ResBase
	Data []*gpuStockModel.GpuStock `json:"data"`
}

func (api *Api) GetGpuStockUsed(req GetGpuStockUsedReq, client *http.Client) (GetGpuStockUsedRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_used", client)
	if err != nil {
		return GetGpuStockUsedRes{}, err
	}
	var res GetGpuStockUsedRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuStockUsedRes{}, err
	}
	return res, nil
}

//

type CountGpuStockUsedReq gpuStockModel.GetGpuStockUsed

type CountGpuStockUsedRes struct {
	ResBase
	Data int64 `json:"data"`
}

func (api *Api) CountMachineGpuStockUsed(req CountGpuStockUsedReq, client *http.Client) (CountGpuStockUsedRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_used_count", client)
	if err != nil {
		return CountGpuStockUsedRes{}, err
	}
	var res CountGpuStockUsedRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CountGpuStockUsedRes{}, err
	}
	return res, nil
}

//

type CountAllMachineGpuStockUsedReq gpuStockModel.CountAllMachineGpuStockUsed

type CountAllMachineGpuStockUsedRes struct {
	ResBase
	Data map[string]int64 `json:"data"`
}

func (api *Api) CountAllMachineGpuStockUsed(req CountAllMachineGpuStockUsedReq, client *http.Client) (CountAllMachineGpuStockUsedRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_used_all", client)
	if err != nil {
		return CountAllMachineGpuStockUsedRes{}, err
	}
	var res CountAllMachineGpuStockUsedRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CountAllMachineGpuStockUsedRes{}, err
	}
	return res, nil
}

//

type GetReserveDetailReq struct {
	MachineID   string                `json:"machine_id"`
	RuntimeUUID string                `json:"runtime_uuid"`
	Priorities  constant.PriorityType `json:"priorities"`
}

type GetReserveDetailRes struct {
	ResBase
	Data GetReserveDetailData `json:"data"`
}

type GetReserveDetailData struct {
	GpuUUIDList []string `json:"gpu_uuid_list"`
	GpuCaps     []string `json:"gpu_caps"`
}

func (api *Api) GetReserveDetail(req GetReserveDetailReq, client *http.Client) (GetReserveDetailRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_reserve_detail", client)
	if err != nil {
		return GetReserveDetailRes{}, err
	}
	var res GetReserveDetailRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetReserveDetailRes{}, err
	}
	return res, nil
}

//

type GetGpuStockAllReq struct {
	MachineID string `json:"machine_id"`
}

type GetGpuStockAllRes struct {
	ResBase
	Data []*gpuStockModel.GpuStock `json:"data"`
}

func (api *Api) GetGpuStockAll(req GetGpuStockAllReq, client *http.Client) (GetGpuStockAllRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_all", client)
	if err != nil {
		return GetGpuStockAllRes{}, err
	}
	var res GetGpuStockAllRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuStockAllRes{}, err
	}
	return res, nil
}

//

type GetAllFreeGpuStockReq struct{}

type GetAllFreeGpuStockRes struct {
	ResBase
	Data []*gpuStockModel.GpuStock `json:"data"`
}

func (api *Api) GetAllFreeGpuStock(req GetAllFreeGpuStockReq, client *http.Client) (GetAllFreeGpuStockRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_free_all", client)
	if err != nil {
		return GetAllFreeGpuStockRes{}, err
	}
	var res GetAllFreeGpuStockRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetAllFreeGpuStockRes{}, err
	}
	return res, nil
}

//

type GetGpuStockByRuntimeUUIDReq struct {
	RuntimeUUIDs []constant.ContainerRuntimeUUID `json:"runtime_uuids"`
}

type GetGpuStockByRuntimeUUIDRes struct {
	ResBase
	Data []*gpuStockModel.GpuStock `json:"data"`
}

func (api *Api) GetGpuStockByRuntimeUUID(req GetGpuStockByRuntimeUUIDReq, client *http.Client) (GetGpuStockByRuntimeUUIDRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_by_runtime_uuids", client)
	if err != nil {
		return GetGpuStockByRuntimeUUIDRes{}, err
	}
	var res GetGpuStockByRuntimeUUIDRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuStockByRuntimeUUIDRes{}, err
	}
	return res, nil
}

//

type GetGpuStockByUUIDReq struct {
	UUIDs    []string `json:"uuids"`
	Priority constant.PriorityType
}

type GetGpuStockByUUIDRes struct {
	ResBase
	Data []*gpuStockModel.GpuStock `json:"data"`
}

func (api *Api) GetGpuStockByUUID(req GetGpuStockByUUIDReq, client *http.Client) (GetGpuStockByUUIDRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/get_by_uuid", client)
	if err != nil {
		return GetGpuStockByUUIDRes{}, err
	}
	var res GetGpuStockByUUIDRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetGpuStockByUUIDRes{}, err
	}
	return res, nil
}

//

type DeleteGpuStockReq struct {
	MachineID string `json:"machine_id"`
}

type DeleteGpuStockRes struct {
	ResBase
}

func (api *Api) DeleteGpuStock(req DeleteGpuStockReq, client *http.Client) (DeleteGpuStockRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/delete", client)
	if err != nil {
		return DeleteGpuStockRes{}, err
	}
	var res DeleteGpuStockRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return DeleteGpuStockRes{}, err
	}
	return res, nil
}

//

type GpuStockListForAdminReq struct {
	MachineID  string                  `form:"machine_id" json:"machine_id"`
	RegionSign constant.RegionSignType `form:"region_sign" json:"region_sign"`
	GpuName    string                  `form:"gpu_name" json:"gpu_name"`
	db_helper.GetPagedRangeRequest
}

type GpuStockListForAdminRes struct {
	ResBase
	Data *db_helper.PagedData `json:"data"`
}

func (api *Api) GpuStockListForAdmin(req GpuStockListForAdminReq, client *http.Client) (GpuStockListForAdminRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/admin/list", client)
	if err != nil {
		return GpuStockListForAdminRes{}, err
	}
	var res GpuStockListForAdminRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuStockListForAdminRes{}, err
	}
	return res, nil
}

//

type GpuStockReleaseByAdminReq struct {
	GpuUUID   string `json:"gpu_uuid"`
	ProductID string `json:"product_uuid"`
}

type GpuStockReleaseByAdminRes struct {
	ResBase
}

func (api *Api) GpuStockReleaseByAdmin(req GpuStockReleaseByAdminReq, client *http.Client) (GpuStockReleaseByAdminRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/admin/release", client)
	if err != nil {
		return GpuStockReleaseByAdminRes{}, err
	}
	var res GpuStockReleaseByAdminRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuStockReleaseByAdminRes{}, err
	}
	return res, nil
}

//

type GpuStockReserveByAdminReq struct {
	GpuUUID   string `json:"gpu_uuid"`
	ProductID string `json:"product_uuid"`
	Priority  int    `json:"priority"`
	MachineID string `json:"machine_id"`
}

type GpuStockReserveByAdminRes struct {
	ResBase
}

func (api *Api) GpuStockReserveByAdmin(req GpuStockReserveByAdminReq, client *http.Client) (GpuStockReserveByAdminRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/admin/reserve", client)
	if err != nil {
		return GpuStockReserveByAdminRes{}, err
	}
	var res GpuStockReserveByAdminRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuStockReserveByAdminRes{}, err
	}
	return res, nil
}

type GpuStockDeleteByAdminReq struct {
	GpuUUID   string `json:"gpu_uuid"`
	MachineID string `json:"machine_id"`
}

type GpuStockDeleteByAdminRes struct {
	ResBase
}

func (api *Api) GpuStockDeleteByAdmin(req GpuStockDeleteByAdminReq, client *http.Client) (GpuStockDeleteByAdminRes, error) {
	body, err := api.post(req, "/api/v1/gpu_stock/admin/delete", client)
	if err != nil {
		return GpuStockDeleteByAdminRes{}, err
	}
	var res GpuStockDeleteByAdminRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GpuStockDeleteByAdminRes{}, err
	}
	return res, nil
}
