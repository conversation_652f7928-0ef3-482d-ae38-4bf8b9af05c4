package coreapi

import (
	"encoding/json"
	"net/http"
	dataDiskModel "server/pkg-core/data_disk_stock/model"
)

type CreateDataDiskReq struct {
	MachineID string `json:"machine_id"`
}

type CreateDataDiskRes struct {
	ResBase
}

func (api *Api) CreateDataDisk(req CreateDataDiskReq, client *http.Client) (CreateDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/create", client)
	if err != nil {
		return CreateDataDiskRes{}, err
	}
	var res CreateDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return CreateDataDiskRes{}, err
	}
	return res, nil
}

//

type UpdateDataDiskReq *dataDiskModel.UpdateDDSParams

type UpdateDataDiskRes struct {
	ResBase
}

func (api *Api) UpdateDataDisk(req UpdateDataDiskReq, client *http.Client) (UpdateDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/update", client)
	if err != nil {
		return UpdateDataDiskRes{}, err
	}
	var res UpdateDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return UpdateDataDiskRes{}, err
	}
	return res, nil
}

//

type GetDataDiskReq struct {
	MachineID string `json:"machine_id"`
}

type GetDataDiskRes struct {
	ResBase
	Data *dataDiskModel.DataDiskStock `json:"data"`
}

func (api *Api) GetDataDisk(req GetDataDiskReq, client *http.Client) (GetDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/get", client)
	if err != nil {
		return GetDataDiskRes{}, err
	}
	var res GetDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetDataDiskRes{}, err
	}
	return res, nil
}

//

type GetDataDiskByMachineIdsReq struct {
	MachineIds []string `json:"machine_ids"`
}

type GetDataDiskByMachineIdsRes struct {
	ResBase
	Data []dataDiskModel.DataDiskStock `json:"data"`
}

func (api *Api) GetDataDiskByMachineIds(req GetDataDiskByMachineIdsReq, client *http.Client) (GetDataDiskByMachineIdsRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/get_by_machine_ids", client)
	if err != nil {
		return GetDataDiskByMachineIdsRes{}, err
	}
	var res GetDataDiskByMachineIdsRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetDataDiskByMachineIdsRes{}, err
	}
	return res, nil
}

//

type GetDataDiskListReq struct {
	MachineIDs []string
}

type GetDataDiskListRes struct {
	ResBase
	Data GetDataDiskListData `json:"data"`
}

type GetDataDiskListData struct {
	Slice []dataDiskModel.DataDiskStock          `json:"slice"`
	Map   map[string]dataDiskModel.DataDiskStock `json:"map"`
}

func (api *Api) GetDataDiskList(req GetDataDiskListReq, client *http.Client) (GetDataDiskListRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/get_list", client)
	if err != nil {
		return GetDataDiskListRes{}, err
	}
	var res GetDataDiskListRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return GetDataDiskListRes{}, err
	}
	return res, nil
}

//

type ReserveDataDiskReq *dataDiskModel.ExpandParams

type ReserveDataDiskRes struct {
	ResBase
	Data ReserveDataDiskData `json:"data"`
}

type ReserveDataDiskData struct {
	TxUUID string `json:"tx_uuid"`
}

func (api *Api) ReserveDataDisk(req ReserveDataDiskReq, client *http.Client) (ReserveDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/reserve", client)
	if err != nil {
		return ReserveDataDiskRes{}, err
	}
	var res ReserveDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ReserveDataDiskRes{}, err
	}
	return res, nil
}

//

type DataDiskTxCommitReq struct {
	TxUUID string `json:"tx_uuid"`
}

type DataDiskTxCommitRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) DataDiskTxCommit(req DataDiskTxCommitReq, client *http.Client) (DataDiskTxCommitRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/tx_commit", client)
	if err != nil {
		return DataDiskTxCommitRes{}, err
	}
	var res DataDiskTxCommitRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return DataDiskTxCommitRes{}, err
	}
	return res, nil
}

//

type DataDiskTxRollbackReq struct {
	TxUUID string `json:"tx_uuid"`
}

type DataDiskTxRollbackRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) DataDiskTxRollback(req DataDiskTxRollbackReq, client *http.Client) (DataDiskTxRollbackRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/tx_rollback", client)
	if err != nil {
		return DataDiskTxRollbackRes{}, err
	}
	var res DataDiskTxRollbackRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return DataDiskTxRollbackRes{}, err
	}
	return res, nil
}

//

//type DataDiskChangeSizeReq *dataDiskModel.ChangeSizeParams
//
//type DataDiskChangeSizeRes struct {
//	ResBase
//}
//
//func (api *Api) DataDiskChangeSize(req DataDiskChangeSizeReq, client *http.Client) (DataDiskChangeSizeRes, error) {
//	body, err := api.post(req, "/api/v1/data_disk/change_size", client)
//	if err != nil {
//		return DataDiskChangeSizeRes{}, err
//	}
//	var res DataDiskChangeSizeRes
//	err = json.Unmarshal(body, &res)
//	if err != nil {
//		return DataDiskChangeSizeRes{}, err
//	}
//	return res, nil
//}

//

type ReleaseDataDiskReq *dataDiskModel.ReleaseParams

type ReleaseDataDiskRes struct {
	ResBase
	Data ReleaseDataDiskData `json:"data"`
}

type ReleaseDataDiskData struct {
	TxUUID string `json:"tx_uuid"`
}

func (api *Api) ReleaseDataDisk(req ReleaseDataDiskReq, client *http.Client) (ReleaseDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/release", client)
	if err != nil {
		return ReleaseDataDiskRes{}, err
	}
	var res ReleaseDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return ReleaseDataDiskRes{}, err
	}
	return res, nil
}

//

type FixDataDiskReq struct {
	MachineID           string `json:"machine_id"`
	DiskExpandAllocated int64  `json:"disk_expand_allocated"`
}

type FixDataDiskRes struct {
	ResBase
	Data interface{} `json:"data"`
}

func (api *Api) FixDataDisk(req FixDataDiskReq, client *http.Client) (FixDataDiskRes, error) {
	body, err := api.post(req, "/api/v1/data_disk/fix", client)
	if err != nil {
		return FixDataDiskRes{}, err
	}
	var res FixDataDiskRes
	err = json.Unmarshal(body, &res)
	if err != nil {
		return FixDataDiskRes{}, err
	}
	return res, nil
}

//
