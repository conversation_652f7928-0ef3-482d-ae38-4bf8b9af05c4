package service

import (
	"server/pkg-core/api/coreapi"
	"server/pkg-core/data_disk_stock/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/logger"
	redis "server/plugin/redis_plugin"
	"time"

	uuid "github.com/satori/go.uuid"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

// Create 创建数据盘记录，在机器注册的时候.防止重复创建，复用get方法
func (svc *DataDiskService) Create(tx *gorm.DB, machineID string) (err error) {
	_, err = svc.Get(tx, machineID)
	return
}

// Update 用于更新数据盘价格以及一些配置信息。
func (svc *DataDiskService) Update(tx *gorm.DB, params *model.UpdateDDSParams) (err error) {
	if params == nil {
		return businesserror.ErrInternalError
	}

	var (
		updateMap = make(map[string]interface{})
		dds       *model.DataDiskStock
		lock      *redis.RedisMutex
	)

	// 无论哪种情况都先上锁
	lock, err = svc.setLockForUpdateDDSStock(params.MachineID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithField("machine_id", params.MachineID).Error("update dds: get dds failed")
		return err
	}

	updateMap["stock_lock"] = dds.StockLock + 1
	// 常规内容
	if dds.FreeDiskSize != params.FreeDiskSize {
		updateMap["free_disk_size"] = params.FreeDiskSize
	}

	// 是否涉及更新扩容库存总量
	if dds.DiskExpandTotal != params.DiskExpandTotal && params.DiskExpandTotal != 0 && !params.ForMachineBasicUpdate {
		if dds.DiskExpandAllocated <= 0 {
			// 处理脏数据，如果已扩容的量小于等于 0，则重置为 0
			dds.DiskExpandAllocated = 0
			updateMap["disk_expand_allocated"] = 0
		}
		diskAvailable := libs.NaturalNumber(params.DiskExpandTotal - dds.DiskExpandAllocated) // 这里现在计算出来的一定大于 0
		updateMap["disk_expand_total"] = params.DiskExpandTotal
		updateMap["disk_expand_available"] = diskAvailable
		updateMap["stock_lock"] = dds.StockLock + 1
	}

	if len(updateMap) <= 1 {
		return nil
	}
	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		rows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStock{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": params.MachineID, "stock_lock": dds.StockLock}},
		}, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).WithField("machine_id", params.MachineID).Error("update dds failed")
			err = businesserror.ErrDatabaseError
			return err
		}
		if rows == 0 {
			svc.log.Error("update dds: affect rows is 0")
			err = businesserror.ErrServerBusy
			return err
		}
		updateMap["machine_id"] = params.MachineID
		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
			err = businesserror.ErrDatabaseError
			return err
		}
		return
	})
	return
}

// UpdateFreeDiskSize 只用于更新机器免费盘大小
func (svc *DataDiskService) UpdateFreeDiskSize(tx *gorm.DB, machineID string, freeDiskSize int64) (err error) {
	var (
		updateMap = make(map[string]interface{})
		dds       *model.DataDiskStock
		lock      *redis.RedisMutex
	)

	// 无论哪种情况都先上锁
	lock, err = svc.setLockForUpdateDDSStock(machineID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(tx, machineID)
	if err != nil {
		svc.log.WithField("machine_id", machineID).Error("update dds: get dds failed")
		return err
	}

	if dds.FreeDiskSize != freeDiskSize {
		updateMap["stock_lock"] = dds.StockLock + 1
		updateMap["free_disk_size"] = freeDiskSize
	}

	svc.log.
		WithField("method", "UpdateFreeDiskSize").
		WithField("machine_id", machineID).
		Info("freeDiskSize:%+v, dds:%+v, update:%+v", freeDiskSize, *dds, updateMap)

	if len(updateMap) <= 1 {
		return nil
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	rows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.DataDiskStock{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID, "stock_lock": dds.StockLock}},
	}, updateMap)
	if errDB.IsNotNil() {
		svc.log.WithError(errDB.GetError()).WithField("machine_id", machineID).Error("update dds failed")
		err = businesserror.ErrDatabaseError
		return err
	}
	if rows == 0 {
		svc.log.Error("update dds: affect rows is 0")
		err = businesserror.ErrServerBusy
		return err
	}

	// 再查一次
	err = dds.DDSGet(tx, machineID)
	if err != nil {
		svc.log.WithField("machine_id", machineID).ErrorE(err, "get dds failed")
		return err
	}

	busUpdateMap := map[string]interface{}{
		"machine_id":     dds.MachineID,
		"free_disk_size": dds.FreeDiskSize,
	}

	queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
	if errDB.IsNotNil() {
		svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
		err = businesserror.ErrDatabaseError
		return err
	}
	return
}

// UpdateExpand 更新机器数据盘扩容大小
func (svc *DataDiskService) UpdateExpand(machineID string, diskExpandTotal int64) (err error) {
	var (
		updateMap = make(map[string]interface{})
		dds       *model.DataDiskStock
		lock      *redis.RedisMutex
	)

	// 无论哪种情况都先上锁
	lock, err = svc.setLockForUpdateDDSStock(machineID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(nil, machineID)
	if err != nil {
		svc.log.WithField("machine_id", machineID).Error("update dds: get dds failed")
		return err
	}

	if dds.DiskExpandTotal != diskExpandTotal && diskExpandTotal > 0 {
		if dds.DiskExpandAllocated <= 0 {
			// 处理脏数据，如果已扩容的量小于等于 0，则重置为 0
			dds.DiskExpandAllocated = 0
			updateMap["disk_expand_allocated"] = 0
		}
		diskAvailable := libs.NaturalNumber(diskExpandTotal - dds.DiskExpandAllocated) // 这里现在计算出来的一定大于等于 0
		updateMap["disk_expand_total"] = diskExpandTotal
		updateMap["disk_expand_available"] = diskAvailable
		updateMap["stock_lock"] = dds.StockLock + 1
	}

	svc.log.
		WithField("method", "UpdateExpand").
		WithField("machine_id", machineID).
		Info("diskExpandTotal:%+v, dds:%+v, update:%+v", diskExpandTotal, *dds, updateMap)

	if len(updateMap) <= 1 {
		return nil
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		rows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStock{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID, "stock_lock": dds.StockLock}},
		}, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).WithField("machine_id", machineID).Error("update dds failed")
			err = businesserror.ErrDatabaseError
			return err
		}
		if rows == 0 {
			svc.log.Error("update dds: affect rows is 0")
			err = businesserror.ErrServerBusy
			return err
		}

		// 再查一次
		err = dds.DDSGet(tx, machineID)
		if err != nil {
			svc.log.WithField("machine_id", machineID).ErrorE(err, "get dds failed")
			return err
		}

		busUpdateMap := map[string]interface{}{
			"machine_id":            dds.MachineID,
			"disk_expand_total":     dds.DiskExpandTotal,
			"disk_expand_allocated": dds.DiskExpandAllocated,
			"disk_expand_available": dds.DiskExpandAvailable,
		}

		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
			err = businesserror.ErrDatabaseError
			return err
		}
		return
	})
	return
}

// Get 支持查询到空记录时自动创建
func (svc *DataDiskService) Get(tx *gorm.DB, machineID string) (res *model.DataDiskStock, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStock{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}},
	}, &res).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			var queueTxMsgUUIDList []string
			defer func() {
				errMsg := svc.q.Pub(queueTxMsgUUIDList...)
				if len(errMsg) != 0 {
					logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
				}
			}()
			res = &model.DataDiskStock{
				MachineID: machineID,
			}
			err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
				errDB = db_helper.InsertOne(db_helper.QueryDefinition{
					DBTransactionConnection: tx,
					ModelDefinition:         &model.DataDiskStock{},
					InsertPayload:           res,
				})
				if errDB.IsNotNil() {
					svc.log.WithField("err", err).Error("create data_disk failed.")
					return
				}
				queueTxMsgUUIDList, errDB = svc.pubInsertDataDisk(tx, res.Map())
				if errDB.IsNotNil() {
					svc.log.WithField("err", err).Error("pub mq insert data_disk failed.")
					return
				}
				return
			}).GetError()
			if err != nil {
				svc.log.WithField("machine_id", machineID).Error("insert machine data disk record failed.")
				err = businesserror.ErrDatabaseError
			}
			return
		}
		svc.log.WithError(err).WithField("machine_id", machineID).Error("get data disk record failed")
		err = businesserror.ErrDatabaseError
	}

	return
}

// GetByMachineIds 支持通过机器 ID 批量查询
func (svc *DataDiskService) GetByMachineIds(machineIds []string) (res []model.DataDiskStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStock{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "machine_id",
					InSet: machineIds,
				},
			},
		},
		NoLimit: true,
	}, &res).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("machine_ids", machineIds).Error("get data disk record failed")
		err = businesserror.ErrDatabaseError
	}

	return
}

// GetList 获取列表
func (svc *DataDiskService) GetList(machineIDList []string) (resSlice []model.DataDiskStock, resMap map[string]model.DataDiskStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStock{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "machine_id", InSet: machineIDList}}},
	}, &resSlice).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get dds by machine id list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	resMap = make(map[string]model.DataDiskStock)
	for k := range resSlice {
		resMap[resSlice[k].MachineID] = resSlice[k]
	}
	return
}

// Reserve 占用库存, 第一次占用，变更占用大小使用changeSize
func (svc *DataDiskService) Reserve(tx *gorm.DB, params *model.ExpandParams) (txUUID string, err error) {
	var (
		now  = time.Now()
		dds  *model.DataDiskStock
		lock *redis.RedisMutex
	)

	// 加锁
	lock, err = svc.setLockForUpdateDDSStock(params.MachineID)
	if err != nil {
		svc.log.WithError(err).Warn("setLockForUpdateDDSStock: set lock failed.")
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithError(err).WithField("machine_id", params.MachineID).Error("get dds failed")
		return
	}

	//if params.ExpandDiskSize > dds.DiskExpandAvailable {
	if params.ExpandDiskSize > dds.GetAvailable() {
		return "", businesserror.ErrDataDiskReserveInsufficientDisk
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()

	allocated := dds.DiskExpandAllocated + params.ExpandDiskSize
	if allocated < 0 {
		allocated = 0
	}

	available := dds.DiskExpandTotal - allocated
	if available < 0 {
		available = 0
	}

	updateMap := map[string]interface{}{
		"disk_expand_allocated": allocated,
		"disk_expand_available": available,
		"stock_lock":            dds.StockLock + 1,
		"updated_at":            now,
	}

	ddsr := &model.DataDiskStockRecord{
		MachineID:    params.MachineID,
		ProductUUID:  params.ProductUUID,
		DiskAllocate: params.ExpandDiskSize,
		TxStatus:     constant.TxStatusPrepared,
		TxUUID:       uuid.NewV4().String(),
		OperateType:  constant.DataDiskReserve,
	}
	// operate
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStock{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": params.MachineID, "stock_lock": dds.StockLock}},
		}, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).Error("update dds failed")
			return businesserror.ErrDatabaseError
		}
		if affectRows == 0 {
			return businesserror.ErrServerBusy
		}
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStockRecord{},
			InsertPayload:           ddsr,
		})
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).Error("update dds record failed")
			return businesserror.ErrDatabaseError
		}

		// 再查一次
		err = dds.DDSGet(tx, ddsr.MachineID)
		if err != nil {
			svc.log.WithField("machine_id", ddsr.MachineID).ErrorE(err, "get dds failed")
			return err
		}

		busUpdateMap := map[string]interface{}{
			"machine_id":            dds.MachineID,
			"disk_expand_allocated": dds.DiskExpandAllocated,
			"disk_expand_available": dds.DiskExpandAvailable,
		}

		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update dds failed.")
			err = businesserror.ErrDatabaseError
			return err
		}
		return nil
	})
	return ddsr.TxUUID, nil
}

func (svc *DataDiskService) TxCommit(params coreapi.DataDiskTxCommitReq) (err error) {
	if params.TxUUID == "" {
		return businesserror.ErrRecordNotFoundError
	}

	ddsr := &model.DataDiskStockRecord{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStockRecord{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"tx_uuid": params.TxUUID,
			},
		},
	}, ddsr).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get ddsr failed.")
		return
	}

	// 如果是committed状态的话，则直接返回成功，不需要再commit了
	if ddsr.TxStatus != constant.TxStatusPrepared {
		return nil
	}

	update := map[string]interface{}{
		"tx_status": constant.TxStatusCommitted,
	}
	affectedRow, dbErr := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStockRecord{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"tx_uuid": params.TxUUID,
			},
		},
	}, update)
	if dbErr.IsNotNil() {
		svc.log.WithError(err).Error("update ddsr failed.")
		return businesserror.ErrDatabaseError
	}
	if affectedRow == 0 {
		svc.log.WithError(err).Error("update ddsr failed.")
		return businesserror.ErrDatabaseError
	}

	return nil
}

func (svc *DataDiskService) TxRollback(params coreapi.DataDiskTxRollbackReq) (err error) {
	if params.TxUUID == "" {
		return businesserror.ErrRecordNotFoundError
	}

	ddsr := &model.DataDiskStockRecord{}
	errDB := db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.DataDiskStockRecord{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"tx_uuid": params.TxUUID,
			},
		},
	}, ddsr)
	if errDB.IsNotNil() {
		svc.log.WithError(err).Error("get ddsr failed.")
		return errDB.GetError()
	}

	// 如果不是prepared状态的话，则直接返回成功，不需要再rollback了
	if ddsr.TxStatus != constant.TxStatusPrepared {
		return nil
	}

	updateMap := make(map[string]interface{})
	if ddsr.OperateType == constant.DataDiskReserve {
		updateMap = map[string]interface{}{
			"disk_expand_allocated": gorm.Expr("disk_expand_allocated - ?", ddsr.DiskAllocate),
			"disk_expand_available": gorm.Expr("disk_expand_available + ?", ddsr.DiskAllocate),
		}
	} else if ddsr.OperateType == constant.DataDiskRelease {
		updateMap = map[string]interface{}{
			"disk_expand_allocated": gorm.Expr("disk_expand_allocated + ?", ddsr.DiskAllocate),
			"disk_expand_available": gorm.Expr("disk_expand_available - ?", ddsr.DiskAllocate),
		}
	}

	queueTxMsgUUIDList := []string{}
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()

	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.UpdateOne(db_helper.QueryDefinition{
			ModelDefinition:         &model.DataDiskStockRecord{},
			DBTransactionConnection: tx,
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"tx_uuid": params.TxUUID,
				},
			},
		}, map[string]interface{}{"deleted_at": time.Now(), "tx_status": constant.TxStatusRollback})
		if errDB.IsNotNil() {
			svc.log.WithError(err).Error("delete ddsr failed.")
			return
		}

		errDB = db_helper.UpdateOne(db_helper.QueryDefinition{
			ModelDefinition:         &model.DataDiskStock{},
			DBTransactionConnection: tx,
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"machine_id": ddsr.MachineID,
				},
			},
		}, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithError(err).Error("delete dds failed.")
			return
		}

		dds := &model.DataDiskStock{}
		err = dds.DDSGet(tx, ddsr.MachineID)
		if err != nil {
			svc.log.WithField("machine_id", ddsr.MachineID).ErrorE(err, "get dds failed")
			return
		}

		busUpdateMap := map[string]interface{}{
			"machine_id":            dds.MachineID,
			"disk_expand_allocated": dds.DiskExpandAllocated,
			"disk_expand_available": dds.DiskExpandAvailable,
		}

		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq updateMap dds failed.")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("delete dds failed.")
		return
	}

	return nil
}

// ChangeSize 扩容/缩容
//func (svc *DataDiskService) ChangeSize(tx *gorm.DB, params *model.ChangeSizeParams) (err error) {
//	if params == nil {
//		return businesserror.ErrInternalError
//	}
//
//	var (
//		log       = svc.log.WithField("params", params)
//		now       = time.Now()
//		dds       *model.DataDiskStock
//		lock      *redis.RedisMutex
//		updateMap = make(map[string]interface{})
//	)
//
//	// 加锁
//	lock, err = svc.setLockForUpdateDDSStock(params.MachineID)
//	if err != nil {
//		svc.log.WithError(err).Warn("setLockForUpdateDDSStock: set lock failed.")
//		return err
//	}
//	defer func() {
//		_ = lock.UnLock()
//	}()
//
//	dds, err = svc.Get(nil, params.MachineID)
//	if err != nil {
//		svc.log.WithError(err).WithField("machine_id", params.MachineID).Error("get dds failed")
//		return
//	}
//	updateMap["stock_lock"] = dds.StockLock + 1
//	updateMap["updated_at"] = now
//
//	switch params.OptType {
//	case constant.OrderTypeDataDiskExpand:
//		if params.DiskAllocateSize+params.ChangeSize != params.FinallySize {
//			log.WithField("dda.allocate", params.DiskAllocateSize).Error("change size: expand, size error")
//			return businesserror.ErrInternalError
//		}
//
//		if params.ChangeSize > dds.DiskExpandAvailable {
//			return businesserror.ErrDataDiskMaxExpandSizeLimit
//		}
//		updateMap["disk_expand_allocated"] = dds.DiskExpandAllocated + params.ChangeSize
//		updateMap["disk_expand_available"] = dds.DiskExpandAvailable - params.ChangeSize
//	case constant.OrderTypeDataDiskReduce:
//		if params.DiskAllocateSize-params.ChangeSize != params.FinallySize {
//			log.WithField("dda.allocate", params.DiskAllocateSize).Error("change size: reduce, size error")
//			return businesserror.ErrInternalError
//		}
//		updateMap["disk_expand_allocated"] = dds.DiskExpandAllocated - params.ChangeSize
//		updateMap["disk_expand_available"] = dds.DiskExpandAvailable + params.ChangeSize
//	default:
//		log.Error("change size: opt type error.")
//		return businesserror.ErrInternalError
//	}
//	var queueTxMsgUUIDList []string
//	defer func() {
//		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
//		if len(errMsg) != 0 {
//			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
//		}
//	}()
//	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
//		affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
//			DBTransactionConnection: tx,
//			ModelDefinition:         &model.DataDiskStock{},
//			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
//				"machine_id": params.MachineID,
//				"stock_lock": dds.StockLock,
//			}},
//		}, updateMap)
//		if errDB.IsNotNil() {
//			log.WithError(errDB.GetError()).Error("update dds failed")
//			err = businesserror.ErrDatabaseError
//			return
//		}
//		if affectRows == 0 {
//			log.Error("update dds: server busy")
//			err = businesserror.ErrServerBusy
//			return
//		}
//		updateMap["machine_id"] = params.MachineID
//		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, updateMap)
//		if errDB.IsNotNil() {
//			svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
//			err = businesserror.ErrDatabaseError
//			return err
//		}
//		return
//	})
//	return
//}

// Release 释放库存
func (svc *DataDiskService) Release(tx *gorm.DB, params *model.ReleaseParams) (txUUID string, err error) {
	if params == nil {
		return "", businesserror.ErrInternalError
	}

	var (
		log  = svc.log.WithField("params", params)
		now  = time.Now()
		dds  *model.DataDiskStock
		lock *redis.RedisMutex
	)

	// 加锁
	lock, err = svc.setLockForUpdateDDSStock(params.MachineID)
	if err != nil {
		svc.log.WithError(err).Warn("setLockForUpdateDDSStock: set lock failed.")
		return "", err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(nil, params.MachineID)
	if err != nil {
		svc.log.WithError(err).WithField("machine_id", params.MachineID).Error("get dds failed")
		return
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()

	allocated := dds.DiskExpandAllocated - params.DiskAllocateSize
	if allocated < 0 {
		allocated = 0
	}

	available := dds.DiskExpandTotal - allocated
	if available < 0 {
		available = 0
	}

	updateMap := map[string]interface{}{
		"disk_expand_allocated": allocated,
		"disk_expand_available": available,
		"stock_lock":            dds.StockLock + 1,
		"updated_at":            now,
	}

	ddsr := &model.DataDiskStockRecord{
		MachineID:    params.MachineID,
		ProductUUID:  params.ProductUUID,
		DiskAllocate: params.DiskAllocateSize,
		TxStatus:     constant.TxStatusPrepared,
		TxUUID:       uuid.NewV4().String(),
		OperateType:  constant.DataDiskRelease,
	}
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStock{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"machine_id": params.MachineID,
				"stock_lock": dds.StockLock,
			}},
		}, updateMap)
		if errDB.IsNotNil() {
			log.WithError(errDB.GetError()).Error("update dds failed")
			err = businesserror.ErrDatabaseError
			return
		}
		if affectRows == 0 {
			log.Error("update dds: server busy")
			err = businesserror.ErrServerBusy
			return
		}
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStockRecord{},
			InsertPayload:           ddsr,
		})
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).Error("update dds record failed")
			return businesserror.ErrDatabaseError
		}

		// 再查一次
		err = dds.DDSGet(tx, ddsr.MachineID)
		if err != nil {
			svc.log.WithField("machine_id", ddsr.MachineID).ErrorE(err, "get dds failed")
			return err
		}

		busUpdateMap := map[string]interface{}{
			"machine_id":            dds.MachineID,
			"disk_expand_allocated": dds.DiskExpandAllocated,
			"disk_expand_available": dds.DiskExpandAvailable,
		}

		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
			err = businesserror.ErrDatabaseError
			return err
		}
		return
	})

	return ddsr.TxUUID, nil
}

// Fix 修复数值，以业务层 allocate 为准
func (svc *DataDiskService) Fix(machineID string, diskExpandAllocated int64) (err error) {
	var (
		updateMap = make(map[string]interface{})
		dds       *model.DataDiskStock
		lock      *redis.RedisMutex
	)

	// 无论哪种情况都先上锁
	lock, err = svc.setLockForUpdateDDSStock(machineID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	dds, err = svc.Get(nil, machineID)
	if err != nil {
		svc.log.WithField("machine_id", machineID).Error("update dds: get dds failed")
		return err
	}

	if dds.DiskExpandAllocated != diskExpandAllocated {
		// 如果 core 里的和业务层的不一样，则需要按照业务层数据更新
		if diskExpandAllocated <= 0 {
			diskExpandAllocated = 0
		}
		diskAvailable := libs.NaturalNumber(dds.DiskExpandTotal - diskExpandAllocated) // 这里现在计算出来的一定大于等于 0
		updateMap["disk_expand_allocated"] = diskExpandAllocated
		updateMap["disk_expand_available"] = diskAvailable
		updateMap["stock_lock"] = dds.StockLock + 1
	}

	svc.log.
		WithField("method", "Fix").
		WithField("machine_id", machineID).
		Info("diskExpandAllocated:%+v, dds:%+v, update:%+v", diskExpandAllocated, *dds, updateMap)

	if len(updateMap) <= 1 {
		return nil
	}

	var queueTxMsgUUIDList []string
	defer func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		rows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.DataDiskStock{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID, "stock_lock": dds.StockLock}},
		}, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).WithField("machine_id", machineID).Error("update dds failed")
			err = businesserror.ErrDatabaseError
			return err
		}
		if rows == 0 {
			svc.log.Error("update dds: affect rows is 0")
			err = businesserror.ErrServerBusy
			return err
		}

		// 再查一次
		err = dds.DDSGet(tx, machineID)
		if err != nil {
			svc.log.WithField("machine_id", machineID).ErrorE(err, "get dds failed")
			return err
		}

		busUpdateMap := map[string]interface{}{
			"machine_id":            dds.MachineID,
			"disk_expand_total":     dds.DiskExpandTotal,
			"disk_expand_allocated": dds.DiskExpandAllocated,
			"disk_expand_available": dds.DiskExpandAvailable,
		}

		queueTxMsgUUIDList, errDB = svc.pubUpdateDataDisk(tx, busUpdateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).Error("pub mq update data_disk failed.")
			err = businesserror.ErrDatabaseError
			return err
		}
		return
	})
	return
}
