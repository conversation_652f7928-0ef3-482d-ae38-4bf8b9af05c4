package service

import (
	"server/pkg/businesserror"
	"server/pkg/constant"
	redis "server/plugin/redis_plugin"
)

func (svc *DataDiskService) setLockForUpdateDDSStock(machineID string) (lock *redis.RedisMutex, err error) {
	lock = svc.mutex.NewRedisMutex(redis.MutexUpdateDDSStock, "update_"+machineID)
	var locked bool
	locked, err = lock.Lock(constant.UpdateDDSStockTimeout)
	if err != nil || !locked {
		svc.log.WithError(err).WithField("machine_id", machineID).WithField("is locked", locked).Warn("setLockForUpdateDDSStock: get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}
	return lock, nil
}
