package service

import (
	"server/pkg/logger"
	"server/plugin/queue"
	redis "server/plugin/redis_plugin"
)

const DDSModuleName = "data_disk_stock"

type DataDiskService struct {
	log   *logger.Logger
	mutex *redis.MutexRedis
	q     *queue.Q
}

func NewDataDiskServiceProvider(mutex *redis.MutexRedis, q *queue.Q) *DataDiskService {
	return &DataDiskService{
		log:   logger.NewLogger(DDSModuleName),
		mutex: mutex,
		q:     q,
	}
}
