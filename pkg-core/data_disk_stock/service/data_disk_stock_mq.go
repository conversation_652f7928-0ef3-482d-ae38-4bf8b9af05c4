package service

import (
	"encoding/json"
	message_model "server/pkg-core/message/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/plugin/queue_interface"

	"gorm.io/gorm"
)

func (svc *DataDiskService) pubInsertDataDisk(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBInsert,
		Data:     data,
	}
	return svc.pubModifyDataDisk(tx, payload)
}

func (svc *DataDiskService) pubUpdateDataDisk(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBUpdate,
		Key:      constant.ModelKeyDataDisk,
		Data:     data,
	}
	return svc.pubModifyDataDisk(tx, payload)
}

func (svc *DataDiskService) pubDeleteDataDisk(tx *gorm.DB, data map[string]interface{}) ([]string, db_helper.ModelError) {
	payload := constant.MQCoreBusinessModifyDataPayload{
		DBAction: constant.DBDelete,
		Data:     data,
	}
	return svc.pubModifyDataDisk(tx, payload)
}

// pubModifyDataDisk 数据盘信息发生改变时 pub 到 mq，遇到错误内部处理，不需要调用方处理
func (svc *DataDiskService) pubModifyDataDisk(tx *gorm.DB, payload constant.MQCoreBusinessModifyDataPayload) ([]string, db_helper.ModelError) {
	msgUUIDs := make([]string, 0, 2)
	b, _ := json.Marshal(payload)
	for _, s := range constant.GetTenants() {
		msgUUID, errDB := message_model.TxPubMessage(tx, &queue_interface.NewQueueForCoreToBusinessModifyData{
			Tenant: s,
			Req: constant.MQCoreToBusinessModifyDataReq{
				Tenant:  s,
				OptType: constant.MQCoreToBusinessModifyDataDiskOpt,
				Payload: string(b),
				Caller:  constant.OptCallerModifyDataDisk,
			},
		})
		if errDB.IsNotNil() {
			return nil, errDB
		}
		msgUUIDs = append(msgUUIDs, msgUUID)
	}
	return msgUUIDs, db_helper.GetModelError(nil)
}
