package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"server/pkg-core/api/coreapi"
	"server/pkg-core/module_definition"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/logger"
)

const ModuleName = "gpu_stock_controller"

type GpuStockController struct {
	logger          *logger.Logger
	gpuStockService module_definition.GpuStockInterface
}

func NewGpuStockControllerProvider(gpuStockService module_definition.GpuStockInterface) *GpuStockController {
	return &GpuStockController{
		logger:          logger.NewLogger(ModuleName),
		gpuStockService: gpuStockService,
	}
}

func (ctrl *GpuStockController) Create(c *gin.Context) {
	var req coreapi.CreateGpuStockReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuStockService.Create(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "create gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) Reserve(c *gin.Context) {
	var req coreapi.GpuReserveReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	ok, err := ctrl.gpuStockService.Reserve(req)
	if err != nil {
		if errors.Is(err, businesserror.ErrMachineGpuNumNotEnough) {
			ctrl.logger.WarnE(err, "reserve gpu stock failed")
		} else {
			ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		}
		http.SendError(c, err)
		return
	}
	data := coreapi.GpuReserveData{
		OK: ok,
	}
	http.SendOK(c, data)
}

func (ctrl *GpuStockController) ReserveCommit(c *gin.Context) {
	var req coreapi.GpuReserveCommitReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuStockService.ReserveCommit(&req)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) Release(c *gin.Context) {
	var req coreapi.GpuReleaseReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuStockService.Release(req)
	if err != nil {
		ctrl.logger.ErrorE(err, "release gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) GetGpuStockUsed(c *gin.Context) {
	var req coreapi.GetGpuStockUsedReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuStocks, err := ctrl.gpuStockService.GetGpuStockUsed(req.MachineID, req.Priorities)
	if err != nil {
		ctrl.logger.ErrorE(err, "get gpu stock used failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuStocks)
}

func (ctrl *GpuStockController) CountMachineGpuStockUsed(c *gin.Context) {
	var req coreapi.CountGpuStockUsedReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	count, err := ctrl.gpuStockService.CountMachineGpuStockUsed(req.MachineID, req.Priorities)
	if err != nil {
		ctrl.logger.ErrorE(err, "get gpu stock used failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, int(count))
}

func (ctrl *GpuStockController) CountAllMachineGpuStockUsed(c *gin.Context) {
	var req coreapi.CountAllMachineGpuStockUsedReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	res, err := ctrl.gpuStockService.CountAllMachineGpuStockUsed(req.Priorities)
	if err != nil {
		ctrl.logger.ErrorE(err, "get gpu stock used failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, res)
}

func (ctrl *GpuStockController) GetReserveDetail(c *gin.Context) {
	var req coreapi.GetReserveDetailReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuUUIDList, gpuCaps, err := ctrl.gpuStockService.GetReserveDetail(req.RuntimeUUID, req.MachineID, req.Priorities)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.GetReserveDetailData{
		GpuUUIDList: gpuUUIDList,
		GpuCaps:     gpuCaps,
	}
	http.SendOK(c, data)
}

func (ctrl *GpuStockController) GetGpuStockAll(c *gin.Context) {
	var req coreapi.GetGpuStockAllReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuStocks, err := ctrl.gpuStockService.GetGpuStockAll(req.MachineID)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuStocks)
}

func (ctrl *GpuStockController) GetAllFreeGpuStock(c *gin.Context) {
	var req coreapi.GetAllFreeGpuStockReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuStocks, err := ctrl.gpuStockService.GetAllFreeGpuStock()
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuStocks)
}

func (ctrl *GpuStockController) GetGpuStockByRuntimeUUID(c *gin.Context) {
	var req coreapi.GetGpuStockByRuntimeUUIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuStocks, err := ctrl.gpuStockService.GetGpuStockByRuntimeUUID(req.RuntimeUUIDs)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuStocks)
}

func (ctrl *GpuStockController) GetGpuStockByUUID(c *gin.Context) {
	var req coreapi.GetGpuStockByUUIDReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	gpuStocks, err := ctrl.gpuStockService.GetGpuStockByUUID(req.UUIDs, req.Priority)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, gpuStocks)
}

func (ctrl *GpuStockController) DeleteGpuStock(c *gin.Context) {
	var req coreapi.DeleteGpuStockReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	err := ctrl.gpuStockService.DeleteGpuStock(req.MachineID)
	if err != nil {
		ctrl.logger.ErrorE(err, "reserve gpu stock failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) GpuStockListForAdmin(c *gin.Context) {
	var req coreapi.GpuStockListForAdminReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	paged, err := ctrl.gpuStockService.GpuStockListForAdmin(&req)
	if err != nil {
		ctrl.logger.ErrorE(err, "GpuStockListForAdmin failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, paged)
	return
}

func (ctrl *GpuStockController) GpuStockReleaseByAdmin(c *gin.Context) {
	var req coreapi.GpuStockReleaseByAdminReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.gpuStockService.GpuStockReleaseByAdmin(&req)
	if err != nil {
		ctrl.logger.ErrorE(err, "GpuStockReleaseByAdmin failed")
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) GpuStockReserveByAdmin(c *gin.Context) {
	var req coreapi.GpuStockReserveByAdminReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.gpuStockService.GpuStockReserveByAdmin(&req)
	if err != nil {
		ctrl.logger.ErrorE(err, "GpuStockReserveByAdmin failed")
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *GpuStockController) GpuStockDeleteByAdmin(c *gin.Context) {
	var req coreapi.GpuStockDeleteByAdminReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.gpuStockService.GpuStockDeleteByAdmin(&req)
	if err != nil {
		ctrl.logger.ErrorE(err, "GpuStockDeleteByAdmin failed")
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}
