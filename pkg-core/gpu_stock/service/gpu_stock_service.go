package service

import (
	"fmt"
	"server/pkg-core/api/coreapi"
	"server/pkg-core/gpu_stock/model"
	coreMachineModel "server/pkg-core/machine/model"
	message_model "server/pkg-core/message/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/international"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	redis "server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"

	"gorm.io/gorm"
)

const GpuStockServiceModuleName = "gpu_stock_service"

type GpuStockService struct {
	log   *logger.Logger
	mutex *redis.MutexRedis
	q     *queue.Q
}

func NewGpuStockServiceProvider(mutex *redis.MutexRedis, q *queue.Q) *GpuStockService {
	return &GpuStockService{log: logger.NewLogger(GpuStockServiceModuleName), mutex: mutex, q: q}
}

func (svc *GpuStockService) Create(params []*model.RegisterGpuStockParam) (err error) {
	if len(params) < 1 {
		svc.log.Error("register gpu stock params is null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		for _, param := range params {
			errDB = db_helper.GetOne(db_helper.QueryDefinition{
				ModelDefinition: &model.GpuStock{},
				Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"gpu_uuid": param.GpuUUID, "machine_id": param.MachineID}},
			}, &model.GpuStock{})
			if errDB.IsNotNil() {
				if errDB.IsRecordNotFoundError() {
					gpuStock := &model.GpuStock{
						MachineID: param.MachineID,
						Index:     param.Index,
						GpuName:   param.GpuName,
						GpuMemory: param.GpuMemory,
						GpuUUID:   param.GpuUUID,
						CreatedAt: time.Now(),
					}
					errDB = db_helper.InsertOne(db_helper.QueryDefinition{ModelDefinition: &model.GpuStock{},
						DBTransactionConnection: tx,
						InsertPayload:           gpuStock})
					if errDB.IsNotNil() {
						svc.log.WithField("err", err).Error("create gpu_stock failed.")
						return
					}
				} else {
					svc.log.WithField("gpu_uuid", param.GpuUUID).WithField("machine_id", param.MachineID).Error("get machine gpu stock failed")
					return
				}
			}
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("create machine gpu stock transaction failed.")
		err = businesserror.ErrDatabaseError
	}
	return
}

func (svc *GpuStockService) Reserve(request *model.GpuReserve) (ok bool, err error) {
	// 加锁, 防止多个实例抢占同一个gpu 锁2s过期
	lock := svc.mutex.NewWatchDog(redis.MutexGpuStockReserveOption, "reserve_"+request.MachineID)
	var locked bool
	locked, err = lock.Lock(constant.GpuStockReserveTimeOut)
	if err != nil || !locked {
		svc.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}

	svc.log.Debug("get lock, machine_id: %s", request.MachineID)
	defer func() {
		_ = lock.UnLock()
		svc.log.Debug("unlock, machine_id: %s", request.MachineID)
	}()

	runtimeUUIDs := buildRuntimeUUID(request.RuntimeUUID)

	// 先检测一下当前runtimeUUID是否已经占用了gpu
	gs := &model.GpuStock{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": request.MachineID},
			InFilters: []db_helper.In{
				{
					Key:   "runtime_uuid",
					InSet: runtimeUUIDs,
				},
			},
		}}, gs).GetError()
	if err == nil {
		svc.log.WithFields(logger.Fields{
			"product_uuid": request.UUID,
			"runtime_uuid": request.RuntimeUUID,
			"machine_id":   request.MachineID,
		}).WithField("err", err).Info("gpu reserved by job")
		return true, nil
	}

	var gpuStock []*model.GpuStock
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters:   map[string]interface{}{"machine_id": request.MachineID},
			CompareFilters: []db_helper.Compare{{Key: "priority", Sign: db_helper.SmallerThan, CompareValue: request.Priority}},
		},
	}, &gpuStock).GetError()
	if err != nil {
		svc.log.WithField("machine_id", request.MachineID).Error("get machine gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}

	if len(gpuStock) < request.GpuNum {
		svc.log.WithField("machine_id", request.MachineID).Warn("machine can be used gpu number less than request gpu_num")
		err = businesserror.ErrMachineGpuNumNotEnough
		return
	}

	var freeGpuStock []*model.GpuStock
	var idlerJobGpuStock []*model.GpuStock
	for _, g := range gpuStock {
		if g.Priority == constant.FreeLevel {
			freeGpuStock = append(freeGpuStock, g)
		}
		if g.Priority == constant.DevelopmentLowLevel {
			idlerJobGpuStock = append(idlerJobGpuStock, g)
		}
	}
	if request.Priority == constant.DevelopmentLowLevel {
		if request.GpuNum > len(freeGpuStock) {
			svc.log.WithField("machine_id", request.MachineID).WithField("product_uuid", request.UUID).WithField("runtime_uuid", request.RuntimeUUID).Warn("idle work request gpu num greater than free gpu num")
			err = businesserror.ErrMachineGpuNumNotEnough
			return
		}
	}

	var queueTxMsgUUIDList []string
	var optDCs []constant.OptInstanceReq
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		// 可能会占用闲时任务，此记录被强行占用的闲时任务
		stockCanBeUsed := append(freeGpuStock, idlerJobGpuStock...)

		sort.Slice(stockCanBeUsed, func(i, j int) bool {
			return stockCanBeUsed[i].Priority < stockCanBeUsed[j].Priority
		})

		for i := 0; i < request.GpuNum; i++ {
			if stockCanBeUsed[i].Priority != constant.FreeLevel && stockCanBeUsed[i].Priority < request.Priority {
				if stockCanBeUsed[i].ProductType == constant.ProductTypeDeployment {
					// 防止dc creating时，stop container失败，直接stop dc
					optDCs = append(optDCs, constant.OptInstanceReq{
						InstanceUUID: stockCanBeUsed[i].ProductUUID,
						OptType:      constant.DCStopByPriorityOpt,
						Caller:       constant.OptCallerDeployment,
						Payload:      "",
					})
				}
			}

			var affectRows int64
			affectRows, errDB = db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
				DBTransactionConnection: tx,
				ModelDefinition:         &model.GpuStock{},
				Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": stockCanBeUsed[i].ID, "product_uuid": stockCanBeUsed[i].ProductUUID}},
			}, map[string]interface{}{
				"priority":     request.Priority,
				"product_uuid": request.UUID,
				"runtime_uuid": request.RuntimeUUID,
				"tx_status":    constant.TxStatusPrepared,
				"updated_at":   time.Now(),
				"product_type": request.ProductType,
				"timestamp":    request.Timestamp,
			})
			if errDB.IsNotNil() {
				svc.log.WithField("err", err).WithField("machine_id ", request.MachineID).Error("update gpu stock failed")
				return
			}
			if affectRows == 0 {
				svc.log.Warn("Reserve gpu failed, current task is busy")
				errDB = db_helper.SetError(international.MMachine00005.GetByLang(international.ZHCNLanguage))
				return errDB
			}
		}

		// 停止被高优先级任务挤占的弹性部署
		if len(optDCs) > 0 {
			machineMsgUUID, errDB := message_model.TxPubMessage(tx, &queue_interface.NewQueueForInstanceOnMachine{
				MachineID:   request.MachineID,
				RuntimeType: constant.ContainerRuntimeOfDeployment,
				OptReqs:     optDCs,
			})
			if errDB.IsNotNil() {
				svc.log.WithFields(logger.Fields{"machine_id": request.MachineID, "opts": optDCs}).ErrorE(errDB.GetError(), "Pub stop dc by instance message failed.")
				return errDB
			}

			svc.log.Info("old low priority dc will be stop by new container [%s], old: %s", request.RuntimeUUID, libs.String(optDCs))
			queueTxMsgUUIDList = append(queueTxMsgUUIDList, machineMsgUUID)
		}

		msgUUID, errDB := message_model.TxPubMessage(tx, &queue_interface.NewQueueForMachineStockChanged{
			MachineID: request.MachineID,
			From:      "gpuStock.Reserve",
		})
		if errDB.IsNotNil() {
			svc.log.WithFields(logger.Fields{
				"machine_id": request.MachineID,
			}).ErrorE(errDB.GetError(), "Pub machine stock changed message failed.")
			return errDB
		}
		queueTxMsgUUIDList = append(queueTxMsgUUIDList, msgUUID)
		return
	}).GetError()
	if err != nil {
		if err.Error() == businesserror.ErrMachineGpuNumUpdateFailed.Error() {
			return false, nil
		}
		svc.log.WithError(err).Error("update machine gpu stock transaction failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	go func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	svc.log.Info("Finally reserve GPU in Gpu stock: %+v", request)
	return true, nil
}

func (svc *GpuStockService) ReserveCommit(request *coreapi.GpuReserveCommitReq) (err error) {
	runtimeUUIDs := buildRuntimeUUID(request.RuntimeUUID)

	// 先检测一下当前有没有对应记录，没有就返回
	gs := &model.GpuStock{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": request.MachineID},
			InFilters: []db_helper.In{
				{
					Key:   "runtime_uuid",
					InSet: runtimeUUIDs,
				},
			},
		}}, gs).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			svc.log.WithFields(logger.Fields{
				"runtime_uuid": request.RuntimeUUID,
				"machine_id":   request.MachineID,
			}).WithField("err", err).Info("gpu_stock reserve record not found")
			return businesserror.ErrRecordNotFoundError
		}
		svc.log.WithFields(logger.Fields{
			"runtime_uuid": request.RuntimeUUID,
			"machine_id":   request.MachineID,
		}).WithField("err", err).Info("gpu_stock get reserve record error")
		return businesserror.ErrDatabaseError
	}
	if gs.TxStatus == constant.TxStatusCommitted {
		return nil
	}

	// 可能会占用闲时任务，此记录被强行占用的闲时任务
	var affectRows int64
	affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": request.MachineID},
			//CompareFilters: []db_helper.Compare{
			//	{
			//		Key:          "timestamp",
			//		Sign:         db_helper.SmallerThan,
			//		CompareValue: request.Timestamp,
			//	},
			//},
			InFilters: []db_helper.In{
				{
					Key:   "runtime_uuid",
					InSet: runtimeUUIDs,
				},
			},
		},
	}, map[string]interface{}{
		"tx_status":  constant.TxStatusCommitted,
		"updated_at": time.Now(),
		"timestamp":  request.Timestamp,
	})

	if errDB.IsNotNil() {
		svc.log.WithField("err", err).WithField("machine_id ", request.MachineID).Error("update gpu stock failed")
		return
	}
	if affectRows == 0 {
		svc.log.Warn("Reserve gpu failed, current task is busy")
		errDB = db_helper.SetError(international.MMachine00005.GetByLang(international.ZHCNLanguage))
		return errDB.GetError()
	}
	svc.log.Info("Finally reserve GPU commit in Gpu stock: %+v", request)
	return nil
}

func (svc *GpuStockService) Release(request *model.GpuRelease) (err error) {
	svc.log.Info("Finally release GPU in Gpu stock: %+v", request)

	runtimeUUIDs := buildRuntimeUUID(request.RuntimeUUID)

	if request.Timestamp == 0 {
		request.Timestamp = tsc.Timestamp()
	}

	var gpuStock []*model.GpuStock
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": request.MachineID},
			CompareFilters: []db_helper.Compare{
				{
					Key:          "timestamp",
					Sign:         db_helper.SmallerThan,
					CompareValue: request.Timestamp,
				},
			},
			InFilters: []db_helper.In{
				{
					Key:   "runtime_uuid",
					InSet: runtimeUUIDs,
				},
			},
		},
	}, &gpuStock).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			svc.log.WithField("machine_id", request.MachineID).Info("gpu stock record not found")
			return nil
		}
		svc.log.WithField("machine_id", request.MachineID).Error("get machine gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}

	var queueTxMsgUUIDList []string
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.UpdateAllWithMap(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.GpuStock{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{"machine_id": request.MachineID},
				CompareFilters: []db_helper.Compare{
					{
						Key:          "timestamp",
						Sign:         db_helper.SmallerThan,
						CompareValue: request.Timestamp,
					},
				},
				InFilters: []db_helper.In{
					{
						Key:   "runtime_uuid",
						InSet: runtimeUUIDs,
					},
				},
			},
			UpdatePayload: map[string]interface{}{
				"priority":     constant.FreeLevel,
				"product_uuid": "",
				"runtime_uuid": "",
				"updated_at":   time.Now(),
				"tx_status":    "",
				"product_type": "",
				"timestamp":    request.Timestamp,
			}})
		if errDB.IsNotNil() {
			svc.log.WithField("err", errDB.GetError()).WithField("machine_id ", request.MachineID).Error("update machine gpu stock failed")
			err = businesserror.ErrDatabaseError
			return
		}

		msgUUID, errDB := message_model.TxPubMessage(tx, &queue_interface.NewQueueForMachineStockChanged{
			MachineID: request.MachineID,
			From:      "gpuStock.Release",
		})
		if errDB.IsNotNil() {
			svc.log.WithFields(logger.Fields{
				"machine_id": request.MachineID,
			}).ErrorE(errDB.GetError(), "Pub machine stock changed message failed.")
			return errDB
		}
		queueTxMsgUUIDList = append(queueTxMsgUUIDList, msgUUID)
		return
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("update machine gpu stock transaction failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	go func() {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s], Pub instance options request failed.", errMsg)
		}
	}()
	return
}

func (svc *GpuStockService) GetGpuStockUsed(machineID string, priorities constant.PriorityType) (gpuStocks []*model.GpuStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters:   map[string]interface{}{"machine_id": machineID},
			CompareFilters: []db_helper.Compare{{Key: "priority", Sign: db_helper.BiggerEqualThan, CompareValue: priorities}},
		},
	}, &gpuStocks).GetError()
	if err != nil {
		svc.log.WithField("machine_id", machineID).Error("get machine total used gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuStockService) CountMachineGpuStockUsed(machineID string, priorities constant.PriorityType) (count int64, err error) {
	g := &model.GpuStock{}
	count, err = g.GpuStockCount(&db_helper.QueryFilters{
		EqualFilters:   map[string]interface{}{"machine_id": machineID},
		CompareFilters: []db_helper.Compare{{Key: "priority", Sign: db_helper.BiggerEqualThan, CompareValue: priorities}},
		NullField:      []string{"deleted_at"},
	})
	if err != nil {
		svc.log.WithField("machine_id", machineID).Error("get machine total used gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

type CountGpuStockUsedRes struct {
	MachineID string `json:"machine_id"`
	Cn        int64  `json:"cn"`
}

func (svc *GpuStockService) CountAllMachineGpuStockUsed(priorities constant.PriorityType) (res map[string]int64, err error) {
	g := &model.GpuStock{}
	list := make([]CountGpuStockUsedRes, 0)
	res = make(map[string]int64)
	sql := fmt.Sprintf("select machine_id,count(*) as cn from %s where deleted_at is null and priority >= %d group by machine_id", model.TableNameGpuStock, priorities)

	err = g.GpuStockExecSqlRaw(sql, &list)
	if err != nil {
		svc.log.Error("CountGpuStockUsed  failed")
		err = businesserror.ErrDatabaseError
		return
	}

	for _, v := range list {
		res[v.MachineID] = v.Cn
	}

	return
}

func (svc *GpuStockService) CountAllMachineGpuStock(machineID string) (count int64, err error) {
	g := &model.GpuStock{}

	count, err = g.GpuStockCount(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"machine_id": machineID,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		svc.log.WithField("machine_id", machineID).ErrorE(err, "get machine total gpu stock num failed")
		err = businesserror.ErrDatabaseError
		return
	}

	return
}

// GetReserveDetail 获取 Reserve 的具体内容
func (svc *GpuStockService) GetReserveDetail(runtimeUUID, machineID string, priority constant.PriorityType) (gpuUUIDList, gpuCaps []string, err error) {
	gpuList := make([]*model.GpuStock, 0)
	gpuUUIDList = make([]string, 0)
	gpuCaps = make([]string, 0)
	runtimeUUIDs := buildRuntimeUUID(runtimeUUID)

	// 获取所有为这个 uuid 绑定的 gpu
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"machine_id": machineID,
				"priority":   priority,
			},
			InFilters: []db_helper.In{
				{
					Key:   "runtime_uuid",
					InSet: runtimeUUIDs,
				},
			},
		},
	}, &gpuList).GetError()
	if err != nil {
		svc.log.WithError(err).Error("Get all occupied gpu failed.")
		err = businesserror.ErrDatabaseError
		return
	}
	for _, gpu := range gpuList {
		gpuUUIDList = append(gpuUUIDList, gpu.GpuUUID)
	}
	// 默认
	gpuCaps = []string{"compute", "utility", "graphics", "video"}
	return
}

func (svc *GpuStockService) GetGpuStockAll(machineID string) (gpuStocks []*model.GpuStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": machineID},
		},
	}, &gpuStocks).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		svc.log.WithField("err", err).WithField("machine_id", machineID).Error("get machine total gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuStockService) GetAllFreeGpuStock() (gpuStocks []*model.GpuStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"priority": constant.FreeLevel},
		},
	}, &gpuStocks).GetError()
	if err != nil {
		svc.log.WithField("err", err).Error("get machine total gpu stock failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuStockService) GetGpuStockByRuntimeUUID(runtimeUUIDs []constant.ContainerRuntimeUUID) (gpuStocks []*model.GpuStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{{Key: "runtime_uuid", InSet: runtimeUUIDs}},
		},
	}, &gpuStocks).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("runtime_uuid", runtimeUUIDs).Error("get total gpu stock by runtime_uuid failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuStockService) GetGpuStockByUUID(uuids []string, priority constant.PriorityType) (gpuStocks []*model.GpuStock, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"priority": priority},
			InFilters:    []db_helper.In{{Key: "product_uuid", InSet: uuids}},
		},
	}, &gpuStocks).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("product_uuid", uuids).Error("get total gpu stock by uuid failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *GpuStockService) DeleteGpuStock(machineID string) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{ModelDefinition: &model.GpuStock{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"machine_id": machineID}}}, &model.GpuStock{}).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		svc.log.WithField("err", err).WithField("machine_id", machineID).Error("delete gpu_stock failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func buildRuntimeUUID(runtimeUUID string) []string {
	runtimeUUIDs := make([]string, 0, 2)
	if strings.Contains(runtimeUUID, constant.TenantAutoDL) {
		runtimeUUIDs = append(runtimeUUIDs, runtimeUUID)
		runtimeUUIDs = append(runtimeUUIDs, strings.TrimLeft(runtimeUUID, constant.TenantAutoDL+"-"))
	} else if strings.Contains(runtimeUUID, constant.TenantGPUHub) {
		runtimeUUIDs = append(runtimeUUIDs, runtimeUUID)
	} else {
		runtimeUUIDs = append(runtimeUUIDs, runtimeUUID)
		runtimeUUIDs = append(runtimeUUIDs, constant.TenantAutoDL+"-"+runtimeUUID)
	}
	return runtimeUUIDs
}

func (svc *GpuStockService) GpuStockListForAdmin(params *coreapi.GpuStockListForAdminReq) (paged *db_helper.PagedData, err error) {
	db := db_helper.GlobalDBConn().Table(model.TableNameGpuStock).
		Where("deleted_at is null")
	if params.MachineID != "" {
		db = db.Where("machine_id = ?", params.MachineID)
	}
	if params.GpuName != "" {
		db = db.Where("gpu_name = ?", params.GpuName)
	}
	if params.RegionSign != "" {
		subQueue := db_helper.GlobalDBConn().Table(coreMachineModel.TableNameMachine).
			Select("machine_id").Where("deleted_at is null").Where("region_sign = ?", params.RegionSign)
		db = db.Where("machine_id in (?)", subQueue)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	list := []model.GpuStock{}
	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.log.WithField("params", params).WithError(err).Warn("get gpu stock list failed.")
		err = businesserror.ErrDatabaseError
	}

	machineIDRegionSignMap := map[string]string{}
	if params.RegionSign == "" {
		machineIDSet := libs.NewStringSet()
		for _, v := range list {
			machineIDSet.Append(v.MachineID)
		}

		regionSignList := []map[string]interface{}{}
		err = db_helper.GlobalDBConn().Table(coreMachineModel.TableNameMachine).
			Select("machine_id, region_sign").
			Where("deleted_at is null").
			Where("machine_id in (?)", machineIDSet.ToSlice()).
			Find(&regionSignList).Error
		if err != nil {
			svc.log.ErrorE(err, "get region_sign from core_machine failed")
			return nil, err
		}
		for _, v := range regionSignList {
			machineIDRegionSignMap[cast.ToString(v["machine_id"])] = cast.ToString(v["region_sign"])
		}
	} else {
		for _, v := range list {
			machineIDRegionSignMap[v.MachineID] = params.RegionSign.String()
		}
	}

	listRes := make([]model.GpuStockListForAdminData, len(list))
	for k, v := range list {
		listRes[k] = model.GpuStockListForAdminData{
			GpuStock:   v,
			RegionSign: machineIDRegionSignMap[v.MachineID],
		}
	}

	paged.List = listRes

	return
}

func (svc *GpuStockService) GpuStockReleaseByAdmin(params *coreapi.GpuStockReleaseByAdminReq) (err error) {
	gpuStock := &model.GpuStock{}
	affectRows, err := gpuStock.GpuStockUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"gpu_uuid":     params.GpuUUID,
		"product_uuid": params.ProductID,
	}}, map[string]interface{}{
		"product_uuid": "",
		"runtime_uuid": "",
		"priority":     0,
		"tx_status":    "",
	})
	if err != nil {
		svc.log.ErrorE(err, "update gpu stock failed, params: %+v", params)
		return err
	}
	if affectRows == 0 {
		err = businesserror.ErrGpuStockReleaseByAdminFailed
		return err
	}

	return
}

func (svc *GpuStockService) GpuStockReserveByAdmin(params *coreapi.GpuStockReserveByAdminReq) (err error) {
	runtimeUUID := ""
	txStatus := ""
	if params.ProductID != "" {
		runtimeUUID = "autodl-container-" + params.ProductID
		txStatus = string(constant.TxStatusCommitted)
	}

	gpuStock := &model.GpuStock{}
	affectRows, err := gpuStock.GpuStockUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"gpu_uuid":     params.GpuUUID,
		"machine_id":   params.MachineID,
		"product_uuid": "",
	}}, map[string]interface{}{
		"product_uuid": params.ProductID,
		"runtime_uuid": runtimeUUID,
		"priority":     params.Priority,
		"tx_status":    txStatus,
	})
	if err != nil {
		svc.log.ErrorE(err, "update gpu stock failed, params: %+v", params)
		return err
	}
	if affectRows == 0 {
		err = businesserror.ErrGpuStockReserveByAdminFailed
		return err
	}
	return
}

func (svc *GpuStockService) GpuStockDeleteByAdmin(params *coreapi.GpuStockDeleteByAdminReq) (err error) {
	gpuStock := &model.GpuStock{}
	affectRows, err := gpuStock.GpuStockUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"machine_id": params.MachineID,
		"gpu_uuid":   params.GpuUUID,
	}}, map[string]interface{}{
		"deleted_at": time.Now(),
	})
	if err != nil {
		svc.log.ErrorE(err, "update gpu stock failed, params: %+v", params)
		return err
	}
	if affectRows == 0 {
		err = businesserror.ErrGpuStockDeleteByAdminFailed
		return err
	}

	return
}
