package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameGpuStock = "core_gpu_stock"

type GpuStock struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index;" json:"-"`

	Index        int                           `gorm:"type:int;column:index;" json:"index"`
	GpuName      string                        `gorm:"type:varchar(255);column:gpu_name;NOT NULL;" json:"gpu_name"`
	GpuUUID      string                        `gorm:"type:varchar(255);column:gpu_uuid;NOT NULL;" json:"gpu_uuid"`
	GpuMemory    int64                         `gorm:"type:bigint(20);column:gpu_memory;" json:"gpu_memory"` //单位为byte
	MachineID    string                        `gorm:"type:varchar(255);column:machine_id;NOT NULL;" json:"machine_id"`
	ProductUUID  string                        `gorm:"type:varchar(255);column:product_uuid;DEFAULT NULL;" json:"product_uuid"` // instanceUUID或JobInstanceUUID
	RuntimeUUUID constant.ContainerRuntimeUUID `gorm:"type:varchar(255);column:runtime_uuid;DEFAULT NULL;" json:"runtime_uuid"` // runtime_uuid
	//优先级用来判断此任务的优先级, 级别高的任务可以抢占级别低的任务的gpu资源
	Priority constant.PriorityType `gorm:"type:int;column:priority;" json:"priority"`
	// OccupationType constant.OrderType           `gorm:"type:varchar(255);column:occupation_type;NOT NULL;" json:"occupation_type"`
	ProductType constant.ProductType `gorm:"type:varchar(255);column:product_type;default ''" json:"product_type"`

	// 事务状态
	TxStatus  constant.TxStatus `gorm:"type:varchar(255);column:tx_status;NOT NULL;" json:"tx_status"`
	Timestamp int64             `gorm:"type:bigint(20);column:timestamp;NOT NULL;default 0" json:"timestamp"`
}

func (m *GpuStock) TableName() string {
	return TableNameGpuStock
}

// Init 实现 db_helper 接口.
func (m *GpuStock) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&GpuStock{})
}

type RegisterGpuStockParam struct {
	MachineID string `json:"machine_id"`
	GpuName   string `json:"gpu_name"`
	GpuMemory int64  `json:"gpu_memory"`
	GpuUUID   string `json:"gpu_uuid"`
	Index     int    `json:"index"`
}

type GpuReserve struct {
	UUID        string                `json:"uuid"`
	RuntimeUUID string                `json:"runtime_uuid"`
	MachineID   string                `json:"machine_id"`
	GpuNum      int                   `json:"gpu_num"`
	Priority    constant.PriorityType `json:"priority"`
	ProductType constant.ProductType  `json:"product_type"`
	Timestamp   int64                 `json:"timestamp"`
}

type GpuRelease struct {
	RuntimeUUID string `json:"runtime_uuid"`
	MachineID   string `json:"machine_id"`
	DebugMsg    string `json:"debug_msg,omitempty"`
	Timestamp   int64  `json:"timestamp"`
}

type GetGpuStockUsed struct {
	MachineID  string                `json:"machine_id"`
	Priorities constant.PriorityType `json:"priorities"`
}

type CountAllMachineGpuStockUsed struct {
	Priorities constant.PriorityType `json:"priorities"`
}

type ReserveOneGpu struct {
	UUID       string                `json:"uuid"`
	Priority   constant.PriorityType `json:"priority"`
	MachineIds []string              `json:"machine_ids"`
}

type GpuStockInfo struct {
	Index     int    `json:"index"`
	GpuName   string `json:"gpu_name"`
	GpuUUID   string `json:"gpu_uuid"`
	GpuMemory int64  `json:"gpu_memory"` //单位为byte
	MachineID string `json:"machine_id"`
	Uuid      string `json:"uuid"` // 实例或闲时任务uuid
	//优先级用来判断此任务的优先级, 级别高的任务可以抢占级别低的任务的gpu资源
	Priority constant.PriorityType `json:"priority"`
}

type GpuStockRank []*GpuStockInfo

func (g GpuStockRank) Len() int           { return len(g) }
func (g GpuStockRank) Swap(i, j int)      { g[i], g[j] = g[j], g[i] }
func (g GpuStockRank) Less(i, j int) bool { return g[i].Index < g[j].Index }

func (m *GpuStock) GpuStockGet(filters *db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: m,
		Filters:         *filters,
	}, &m).GetError()
}

func (m *GpuStock) GpuStockUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um interface{}) (int, error) {
	affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         m,
		Filters:                 *filter,
	}, um)
	if errDB.IsNotNil() {
		return 0, errDB.GetError()
	}
	return int(affectRows), nil
}

func (m *GpuStock) GpuStockGetAllWithSelect(filter *db_helper.QueryFilters, sel string, result interface{}) (err error) {
	return db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: m,
		Filters:         *filter,
		Select:          sel,
		NoLimit:         true,
	}, result).GetError()
}

func (m *GpuStock) GpuStockCount(filter *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: m,
		Filters:         *filter,
		NoLimit:         true,
	}, &count).GetError()
	return
}

func (m *GpuStock) GpuStockExecSqlRaw(sql string, result interface{}) (err error) {
	return db_helper.GlobalDBConnForRead().Raw(sql).Scan(result).Error
}
