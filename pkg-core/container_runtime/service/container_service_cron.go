package service

import (
	"context"
	"github.com/pkg/errors"
	"server/conf"
	containerModel "server/pkg-core/container_runtime/model"
	message_model "server/pkg-core/message/model"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/queue_interface"
	"time"

	"gorm.io/gorm"
)

func (c *CR) CronJobRegister(ctx context.Context, cron *libs.Cron) {
	//go c.cronFindAndSetNoRespondingOptStatus(ctx)

	// 12小时后，对于标记soft_removed的记录下发remove命令
	// _ = c.kv.Del(constant.JobCronPubRemoveCmdToAgentWithSoftRemoved)
	if conf.GetGlobalGsConfig().App.DebugApi {
		cron.CronAdd("0 0 *", c.cronPubRemoveCmdToAgent)
	} else {
		cron.CronAdd("0 0 11,16", c.cronPubRemoveCmdToAgent)
	}

	<-ctx.Done()
}

func (c *CR) cronPubRemoveCmdToAgent() {
	l := logger.NewLogger("cronPubRemoveCmdToAgent")

	result, err := c.kv.Get(constant.JobCronPubRemoveCmdToAgent)
	if err != nil {
		l.ErrorE(err, "get kv control failed")
		return
	}

	if result != "" {
		l.Warn("cron job has been banned by kv control")
		return
	}
	c.pubRemoveCmdToAgent(l)
}

func (c *CR) pubRemoveCmdToAgent(l *logger.Logger) {
	l.Info("job begin running...")

	con := containerModel.Container{}
	cronList := []containerModel.Container{}

	err := con.ContainerExec(nil).
		Where("assigned_remove_time < ?", time.Now()).
		Where("deleted_at is null").
		FindInBatches(&cronList, 500, func(tx *gorm.DB, batch int) error {
			for _, container := range cronList {
				if container.AssignedRemoveTime == nil || container.AssignedRemoveTime.After(time.Now()) {
					continue
				}

				_ = container.LoadAllStructByJson()

				ll := l.WithFields(logger.Fields{
					"runtime_uuid":         container.RuntimeUUID,
					"stopped_at":           container.StoppedAt,
					"assigned_remove_time": container.AssignedRemoveTime,
				})

				_, err := c.mod.GetMachine(container.MachineID)
				if err == nil {
					agentHandler := NewAgentHandler(container.MachineID, *container.ContainerParam, *container.RuntimeParam, c.t, c.q)

					// 2. pub cmd
					err = agentHandler.PubRemoveContainerCommand()
					if err != nil {
						ll.ErrorE(err, "Pub remove container cmd to agent failed.")
						continue
					}

					ll.Info("Pub remove container cmd to agent.")
					continue
				}

				if err != nil && !errors.Is(err, biz.ErrMachineNotFound) {
					ll.ErrorE(err, "get machine failed")
					continue
				}

				// machine已经删除,
				//  因为此处是先删除机器,再清除记录导致的操作, 所以 直接清除记录
				//  关于其他资源的释放, 在前面的remove, 已经释放过了
				// 删除instance

				// todo: zt 借用 NewQueueForCoreToBusinessUpdateContainerStatus 删除业务层的instance
				err = message_model.SimplePubMessage(c.l, c.q, &queue_interface.NewQueueForCoreToBusinessUpdateContainerStatus{
					Tenant: container.Tenant,
					Reqs: []constant.MQCoreToBusinessUpdateContainerStatusReq{
						{
							Tenant:    container.Tenant,
							MachineID: container.MachineID,
							OptType:   constant.MQCoreToBusinessDeleteInstanceRecordOpt,
							Payload:   container.ProductUUID,
							Caller:    constant.OptCallerCronRemoveInstanceCacheOpt,
						},
					},
				})
				if err != nil {
					ll.ErrorE(err, "pub MQCoreToBusinessDeleteInstanceRecordOpt failed")
					return err
				}

				// 删除 container
				err = c.crud.DeleteContainer(container.RuntimeUUID)
				if err != nil {
					ll.ErrorE(err, "delete container failed")
					continue
				}

				continue
			}

			return nil
		}).Error
	if err != nil {
		l.ErrorE(err, "get assigned remove time before now container list failed")
		return
	}
}

// 轮询检查所有在指定状态下停留太久的容器, 并对其超时进行处理.
func (c *CR) cronFindAndSetNoRespondingOptStatus(ctx context.Context) {
	ticker := time.NewTicker(constant.ContainerStatusCheckWaitingInterval) // 10s

	backupMapping := make(map[string]bool)
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
		}

		// 常规的对开机中， 关机中，释放中的超时检测
		c.handleRegularNoRespondingContainers(constant.ContainerStatusWaitingTimeout, backupMapping)
	}
}

func (c *CR) handleRegularNoRespondingContainers(timeout time.Duration, backupMapping map[string]bool) {
	var regularTimeoutCheckList = []constant.ContainerStatusType{
		constant.ContainerCreating,
		constant.ContainerReInitializing,
		constant.ContainerStarting,
		constant.ContainerStopping,
		constant.ContainerRemoving,
		constant.ContainerCloning,
	}

	timeoutContainers, err := c.crud.GetContainersInXXingStatusForTooLong(timeout, regularTimeoutCheckList)
	if err != nil {
		c.l.WarnE(err, "GetContainersInXXingStatusForTooLong() failed.")
		return
	}

	for _, container := range timeoutContainers {
		if _, ok := backupMapping[container.RuntimeUUID.String()]; !ok {
			backupMapping[container.RuntimeUUID.String()] = true
			c.l.WithFields(logger.Fields{
				"runtime_uuid": container.RuntimeUUID,
				"old_status":   container.ContainerStatus,
				"created at":   container.StatusCreatedAt,
			}).Info("Just a warn log. The container has been in this state for over %.2f seconds. Need to force this container to the next status.", timeout.Seconds())
		}
	}
	return
}
