package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"server/conf"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg-core/api/coreapi"
	containerModel "server/pkg-core/container_runtime/model"
	message_model "server/pkg-core/message/model"
	"server/pkg-core/module_definition"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/kv_plugin"
	"server/plugin/queue"
	"server/plugin/queue_interface"
	"server/plugin/redis_plugin"
	tsc "server/plugin/redis_plugin/time_service_center"
	"time"

	"gorm.io/gorm"
)

/**
 * 实现 ContainerRuntimeInterface 接口
 */

const secNameContainerRuntime = "container_runtime"

type CR struct {
	crud CRUDHandler
	mod  ModuleHandler
	t    redis_plugin.SyncTimer
	cu   *redis_plugin.ContainerUsagePlugin

	q     *queue.Q
	mutex *redis_plugin.MutexRedis
	l     *logger.Logger

	hooks    *containerModel.Hooker
	handlers map[constant.OptType]handler
	kv       *kv_plugin.KVPlugin
}

func NewContainerRuntime(
	self *CR,
	machineInterface module_definition.MachineInference,
	portInterface module_definition.PortInterface,
	gpuStockInterface module_definition.GpuStockInterface,
	gpuType module_definition.GpuTypeInference,
	cu *redis_plugin.ContainerUsagePlugin,
	region module_definition.RegionInterface,
	dds module_definition.DataDiskStockInterface,
	timer redis_plugin.SyncTimer,
	q *queue.Q,
	mutex *redis_plugin.MutexRedis,
	kv *kv_plugin.KVPlugin,
) {
	self.crud = NewCRUDHandler(timer, logger.NewLogger(secNameContainerRuntime), cu)
	self.mod = NewModuleHandler(machineInterface, portInterface, gpuStockInterface, gpuType, region, dds, timer, q)
	self.t = timer
	self.q = q
	self.cu = cu
	self.mutex = mutex
	self.l = logger.NewLogger(secNameContainerRuntime)
	self.hooks = containerModel.NewHooker(conf.GetGlobalGsConfig().App.DebugLog)
	self.kv = kv

	self.initHandlerMap()
	return
}

func InjectFakeModuleForTest(svc *CR, mod ModuleHandler, l *logger.Logger) {
	svc.l = l
	svc.mod = mod
}

func (c *CR) ExistUUID(runtimeUUID constant.ContainerRuntimeUUID) (exist bool, err error) {
	return c.crud.ExistContainer(runtimeUUID)
}

func (c *CR) GetContainerRecord(runtimeUUID constant.ContainerRuntimeUUID) (containerModel.Container, error) {
	container, err := c.crud.GetContainer(runtimeUUID)
	if err != nil {
		// TRACE
		// cl := logger.NewLogger("GetContainerFailed").WithField("runtime_uuid", runtimeUUID)
		// cl.WithError(err).Trace("GetContainerRecord() get container failed.")
		return containerModel.Container{}, err
	}
	return container, nil
}

func (c *CR) CountMachineBindingContainerNum(machineID string) (num int64, err error) {
	err = db_helper.GlobalDBConn().Model(&containerModel.Container{}).
		Where("deleted_at is null").
		Where("machine_id = ?", machineID).
		Where("runtime_type = ?", constant.ContainerRuntimeOfInstance).
		Count(&num).Error
	if err != nil {
		c.l.WithField("machine_id", machineID).ErrorE(err, "count container failed")
	}

	return
}

func (c *CR) GetContainerRecordUnScoped(runtimeUUID constant.ContainerRuntimeUUID) (containerModel.Container, error) {
	return c.crud.GetContainerUnscoped(runtimeUUID)
}

func (c *CR) GetContainerRecords(runtimeUUIDList []constant.ContainerRuntimeUUID) ([]containerModel.Container, error) {
	return c.crud.GetContainers(runtimeUUIDList)
}

func (c *CR) GetContainerSimplifiedInfoList(
	runtimeUUIDList []constant.ContainerRuntimeUUID,
	containerTaskType constant.ContainerRuntimeType,
) (infoList []*containerModel.ContainerSimplifiedInfo, err error) {

	containers, err := c.crud.GetContainers(runtimeUUIDList)
	if err != nil {
		return
	}

	var containerMap = make(map[constant.ContainerRuntimeUUID]containerModel.Container)
	for i := range containers {
		containerMap[containers[i].RuntimeUUID] = containers[i]
	}

	for _, runtimeUUID := range runtimeUUIDList {
		container, ok := containerMap[runtimeUUID]
		if !ok {
			continue
		}

		var usageInfo constant.ContainerUsageInfo
		usageInfo, err = c.GetContainerUsageThenSetInRedis(runtimeUUID)
		if err != nil {
			return
		}

		var info = &containerModel.ContainerSimplifiedInfo{
			RuntimeUUID: runtimeUUID,
			Status:      container.LatestStatus.ContainerStatus.SimplifiedToString(containerTaskType),
			SubStatus:   container.LatestStatus.ContainerSubStatus.SimplifiedToString(containerTaskType),
			StatusAt:    container.LatestStatus.ValidAt.Time,
			// 默认返回false, 这个指标目前没有实际意义
			OOMKilled:                false && container.LatestStatus.OOMKilled,
			RegionSign:               container.RegionSign,
			ContainerParam:           *container.ContainerParam,
			StartedAt:                container.StartedAt,
			StoppedAt:                container.StoppedAt,
			UsageInfo:                usageInfo,
			DiskHealthStatus:         usageInfo.DiskHealthStatus,
			RootFSUsedRate:           usageInfo.RootFSUsedRate,
			StartMode:                container.StartMode,
			ContainerSerializeStatus: containerModel.ContainerSerializeStatus{},
			StatusId:                 container.StatusId,
		}

		// try to fill region sign
		if len(info.RegionSign) == 0 {
			rs, uErr := c.mod.GetMachineRegionSign(container.MachineID)
			if uErr == nil {
				info.RegionSign = rs
				_ = c.crud.UpdateContainerRegionSign(runtimeUUID, rs)
			}
		}

		infoList = append(infoList, info)
	}

	return
}

func (c *CR) GetContainerSimplifiedInfo(
	runtimeUUID constant.ContainerRuntimeUUID,
	containerTaskType constant.ContainerRuntimeType,
) (info *containerModel.ContainerSimplifiedInfo, err error) {
	container, err := c.crud.GetContainer(runtimeUUID)
	if err != nil {
		return
	}

	var usageInfo constant.ContainerUsageInfo
	usageInfo, err = c.GetContainerUsageThenSetInRedis(runtimeUUID)
	if err != nil {
		return
	}

	info = &containerModel.ContainerSimplifiedInfo{
		RuntimeUUID: runtimeUUID,
		Status:      container.LatestStatus.ContainerStatus.SimplifiedToString(containerTaskType),
		SubStatus:   container.LatestStatus.ContainerSubStatus.SimplifiedToString(containerTaskType),
		StatusAt:    container.LatestStatus.ValidAt.Time,
		StatusId:    container.StatusId,
		// 默认返回false, 这个指标目前没有实际意义
		OOMKilled:                false && container.LatestStatus.OOMKilled,
		RegionSign:               container.RegionSign,
		ContainerParam:           *container.ContainerParam,
		StartedAt:                container.StartedAt,
		StoppedAt:                container.StoppedAt,
		UsageInfo:                usageInfo,
		DiskHealthStatus:         usageInfo.DiskHealthStatus,
		RootFSUsedRate:           usageInfo.RootFSUsedRate,
		StartMode:                container.StartMode,
		ContainerSerializeStatus: containerModel.ContainerSerializeStatus{},
	}

	// try to fill region sign
	if len(info.RegionSign) == 0 {
		rs, uErr := c.mod.GetMachineRegionSign(container.MachineID)
		if uErr == nil {
			info.RegionSign = rs
			_ = c.crud.UpdateContainerRegionSign(runtimeUUID, rs)
		}
	}

	return
}

func (c *CR) GetContainerSimplifiedInfoListWithUsage(
	runtimeUUIDList []constant.ContainerRuntimeUUID,
	containerTaskType constant.ContainerRuntimeType,
) (infoList []containerModel.ContainerSimplifiedInfoWithUsage, err error) {
	infoList = []containerModel.ContainerSimplifiedInfoWithUsage{}
	containers, err := c.crud.GetContainers(runtimeUUIDList)
	if err != nil {
		return
	}

	var containerMap = make(map[constant.ContainerRuntimeUUID]containerModel.Container)
	for i := range containers {
		containerMap[containers[i].RuntimeUUID] = containers[i]
	}

	usageMap := c.cu.GetContainerUsageInfoList(runtimeUUIDList)

	for _, runtimeUUID := range runtimeUUIDList {
		container, ok := containerMap[runtimeUUID]
		if !ok {
			continue
		}

		var usageInfo constant.ContainerUsageInfo
		usageInfo, err = c.GetContainerUsageThenSetInRedis(runtimeUUID)
		if err != nil {
			return
		}

		var info = containerModel.ContainerSimplifiedInfo{
			RuntimeUUID: runtimeUUID,
			Status:      container.LatestStatus.ContainerStatus.SimplifiedToString(containerTaskType),
			SubStatus:   container.LatestStatus.ContainerSubStatus.SimplifiedToString(containerTaskType),
			StatusAt:    container.LatestStatus.ValidAt.Time,
			// 默认返回false, 这个指标目前没有实际意义
			OOMKilled:                false && container.LatestStatus.OOMKilled,
			RegionSign:               container.RegionSign,
			ContainerParam:           *container.ContainerParam,
			StartedAt:                container.StartedAt,
			StoppedAt:                container.StoppedAt,
			UsageInfo:                usageInfo,
			DiskHealthStatus:         usageInfo.DiskHealthStatus,
			RootFSUsedRate:           usageInfo.RootFSUsedRate,
			StartMode:                container.StartMode,
			ContainerSerializeStatus: containerModel.ContainerSerializeStatus{},
			StatusId:                 container.StatusId,
		}

		// try to fill region sign
		if len(info.RegionSign) == 0 {
			rs, uErr := c.mod.GetMachineRegionSign(container.MachineID)
			if uErr == nil {
				info.RegionSign = rs
				_ = c.crud.UpdateContainerRegionSign(runtimeUUID, rs)
			}
		}

		infoList = append(infoList, containerModel.ContainerSimplifiedInfoWithUsage{
			Info:  info,
			Usage: usageMap[runtimeUUID],
		})
	}

	return
}

func (c *CR) OperateContainer(reqs ...constant.OptContainerReq) (err error) {
	for i := range reqs {
		err := c.operateContainer(reqs[i])
		if err != nil {
			return err
		}
	}

	return nil
}

func (c *CR) ForceOperateContainer(
	runtimeUUID constant.ContainerRuntimeUUID,
	containerTaskType constant.ContainerRuntimeType,
	isStop bool,
) (info *containerModel.ContainerSimplifiedInfo, err error) {
	// 获取 core_container 最后一条记录的 id
	container, err := c.GetContainerRecord(runtimeUUID)
	if err != nil {
		c.l.WithField("runtime_uuid", runtimeUUID).ErrorE(err, "get container info by runtime uuid failed.")
		return
	}

	// 调用 core 更改 core_container_status_history 的最后一条状态
	err = c.crud.UpdateContainerStatusHistory(nil, container.LatestStatusID, isStop)
	if err != nil {
		c.l.WithField("latest_status_id", container.LatestStatusID).ErrorE(err, "update container status failed")
		return
	}

	// 更新 core_container status_id
	//err = c.crud.UpdateContainerParam(runtimeUUID, containerModel.ContainerParam{
	//	StatusId: tsc.Timestamp(),
	//})

	container.ContainerParam.StatusId = tsc.Timestamp()
	err = c.crud.UpdateContainerParam(runtimeUUID, *container.ContainerParam)
	if err != nil {
		c.l.WithField("runtime_uuid", runtimeUUID).ErrorE(err, "update container status id failed")
		return
	}

	err = container.ContainerUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"runtime_uuid": container.RuntimeUUID,
	}}, map[string]interface{}{
		"status_id": tsc.Timestamp(),
	})
	if err != nil {
		c.l.WithField("runtime_uuid", runtimeUUID).ErrorE(err, "update container status id failed")
		return nil, err
	}

	info, err = c.GetContainerSimplifiedInfo(runtimeUUID, containerTaskType)
	if err != nil {
		c.l.WarnE(err, "Get container simplified info for instance failed. runtimeUUID=%v", runtimeUUID)
		return
	}

	return
}

func (c *CR) OperateContainerByPubMQ(req constant.OptContainerReq) error {
	container, err := c.crud.GetContainer(req.RuntimeUUID)
	if err != nil {
		cl := logger.NewLogger("GetContainerFailed").WithField("runtime_uuid", req.RuntimeUUID)
		cl.WarnE(err, "OperateContainerByPubMQ() get container failed. req: %+v", req)
		return err
	}

	// PUB
	err = message_model.SimplePubMessage(c.l, c.q, &queue_interface.NewQueueForContainerOnMachine{
		MachineID: container.MachineID,
		OptReqs: []constant.OptContainerReq{
			req,
		},
	})
	if err != nil {
		c.l.WithFields(logger.Fields{
			"machine_id": container.MachineID,
		}).ErrorE(err, "Pub message failed. req: %+v", req)
		return err
	}

	c.l.Info("Will operate container by pub: %+v", req)
	return nil
}

func (c *CR) GuardCronJobRegister(ctx context.Context, machineID string, writer chan<- messenger.Message) {
	go func() {
		// only check instance
		var getMsgPayload = func() string {
			containers, err := c.crud.GetContainersOfInstanceByMachineID(machineID)
			if err != nil {
				return ""
			}

			if len(containers) == 0 {
				return ""
			}

			var containerIDList []agent_constant.ContainerID
			for _, container := range containers {
				containerIDList = append(containerIDList, agent_constant.NewContainerID(container.RuntimeUUID.String()))
			}

			req := &agent_constant.ContainerCheckRequest{
				Containers: containerIDList,
			}

			payload, err := req.String()
			if err != nil {
				c.l.WarnE(err, "Marshal ContainerCheckRequest failed.")
				return ""
			}
			return payload
		}

		var msgToWrite *messenger.Message
		for {
			payload := getMsgPayload()
			if payload != "" {
				msgToWrite = &messenger.Message{
					MsgID:   c.t.Now().Format(time.RFC3339Nano),
					Type:    messenger.ContainerCronCheckType,
					Payload: payload,
				}
			}

			// 这样只能尽量让写入 writer 的操作和 ctx.Done 相邻，尽量及时发现连接断开
			ticker := time.NewTicker(constant.ContainerStatusCheckByServerInterval) // 5s
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				if msgToWrite != nil {
					writer <- *msgToWrite // 如果对方连接意外断开，容易出现 send on closed channel
				}
			}
			ticker.Stop()
		}
	}()
}

func (c *CR) MsgRegister() (info []queue.RegisterInfo) {
	return
}

func (c *CR) registerContainerStatusHook(
	runtimeType constant.ContainerRuntimeType,
	f containerModel.ContainerStatusHookFunc,
	hookName string,
) {
	/*
	 * 容器状态变化回调钩子
	 */

	/*
	 * registerContainerStatusHook
	 * container 模块的钩子只关心状态变化. 将 instance 和闲时任务的业务钩子各自保持在自己模块内部 (但这些附属模块的状态更新会晚于真实状态).
	 * 如果 bill 模块的钩子能自己区分实例还是闲时任务的话也可以直接注册在此处.
	 * 外部钩子内部应该仅执行启动停止等业务操作.
	 * 外部钩子内部如果执行状态更新, 必须是发消息, 否则会抢锁.
	 */
	c.hooks.RegHook(runtimeType, f, hookName)
	c.l.WithField("hook_name", hookName).Debug("[hook] Container status hook registered.")
	return
}

func (c *CR) InitOwnHooks() {
	c.registerContainerStatusHook(
		constant.ContainerRuntimeOfInstance,
		c.containerSelfHooks,
		"container_self_hooks_of_instance",
	)

	c.registerContainerStatusHook(
		constant.ContainerRuntimeOfDeployment,
		c.containerSelfHooks,
		"container_self_hooks_of_deployment",
	)

	// 下面增加三个勾子，将参数 pub 到 MQ 中

	c.registerContainerStatusHook(
		constant.ContainerRuntimeOfInstance,
		c.containerHookPubMQ,
		"container_hook_pub_mq_of_instance",
	)

	c.registerContainerStatusHook(
		constant.ContainerRuntimeOfDeployment,
		c.containerHookPubMQ,
		"container_hook_pub_mq_of_deployment",
	)
}

func (c *CR) CheckContainerStatusNum(runtimeUUID constant.ContainerRuntimeUUID, status []constant.ContainerStatusType) (count int64, err error) {
	return c.crud.GetContainerStatusHistoryCount(runtimeUUID, status)
}

// CheckUserRunningNonGPULimit 一个用户同时只能启动两个无 gpu 启动的容器
func (c *CR) CheckUserRunningNonGPULimit(uid, limit int) (can bool, err error) {
	can = true

	containers, err := c.crud.GetAllContainersByUID(uid)
	if err != nil {
		c.l.WarnE(err, "GetAllContainersByUID() for user_%d failed.", uid)
		return false, err
	}

	var runningNonGPU = 0
	for _, container := range containers {
		if container.StartMode.IsNonGPU() &&
			container.LatestStatus.ContainerStatus.IsRunningOrStarting() {

			runningNonGPU++
			if runningNonGPU >= limit {
				can = false
				break
			}
		}
	}
	return
}

//
//func (c *CR) UpdateContainerUploadImageInfo(progressInfo *agent_constant.UploadOssInfo) error {
//	return c.mod.UpdatePrivateImage(progressInfo)
//}

//func (c *CR) CancelUploadPrivateImage(cancelUploadImageInfo *agent_constant.CancelUploadImageInfo) error {
//	return c.mod.CancelUploadPrivateImage(cancelUploadImageInfo)
//}

// GetContainerOperateHistory 获取容器操作历史
func (c *CR) GetContainerOperateHistory(runtimeUUID []constant.ContainerRuntimeUUID, operateType []constant.OptType, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []containerModel.OperateHistory, err error) {
	if pageReq == nil {
		pageReq = &db_helper.GetPagedRangeRequest{}
	}

	list = make([]containerModel.OperateHistory, 0)
	db := db_helper.GlobalDBConn().Model(&containerModel.OperateHistory{})

	if len(runtimeUUID) != 0 {
		db = db.Where("runtime_uuid in (?)", runtimeUUID)
	}
	if len(operateType) != 0 {
		db = db.Where("operate in (?)", operateType)
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		c.l.WithError(err).Warn("Count failed.")
		err = biz.ErrDatabaseError
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).
		Error
	if err != nil {
		c.l.WithError(err).Warn("get container operate history failed.")
		err = biz.ErrDatabaseError
	}
	paged.List = &list

	return
}

func (c *CR) GetContainerUsageThenSetInRedis(runtimeUUID constant.ContainerRuntimeUUID) (usageInfo constant.ContainerUsageInfo, err error) {
	var exist bool
	usageInfo, exist = c.cu.GetContainerUsageInfo(runtimeUUID.String())
	if exist {
		err = nil
		return
	}
	return
}

func (c *CR) GetContainerUsageList(runtimeUUIDList []constant.ContainerRuntimeUUID) (usageInfoList map[constant.ContainerRuntimeUUID]constant.ContainerUsageInfo, err error) {
	usageInfoList = map[constant.ContainerRuntimeUUID]constant.ContainerUsageInfo{}
	usageInfoList = c.cu.GetContainerUsageInfoList(runtimeUUIDList)
	return
}

func (c *CR) CreateMigHistory(req coreapi.CreateContainerMigHistoryReq) (migID int, err error) {
	mig, exist, err := c.CheckMigHistoryInMigratingStatus(req.SourceRuntimeUUID)
	if err != nil {
		return 0, err
	}

	if exist {
		return mig.ID, nil
	}

	migrateHistory := &containerModel.MigrationHistory{
		UID:               req.UID,
		RuntimeType:       req.RuntimeType,
		SourceRuntimeUUID: req.SourceRuntimeUUID,
		SourceMachineID:   req.SourceMachineID,
		SourceRegionSign:  req.SourceRegionSign,
		SourceProductUUID: req.SourceProductUUID,
		TargetRuntimeUUID: req.TargetRuntimeUUID,
		TargetMachineID:   req.TargetMachineID,
		TargetRegionSign:  req.TargetRegionSign,
		TargetProductUUID: req.TargetProductUUID,
		StartedAt: sql.NullTime{
			Time:  c.t.Now(),
			Valid: true,
		},
		FinishedAt: sql.NullTime{},
		Status:     containerModel.Migrating,
		Msg:        "",
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &containerModel.MigrationHistory{},
		InsertPayload:   migrateHistory,
	}).GetError()
	if err != nil {
		c.l.WithField("instanceUUID", req.SourceRuntimeUUID).ErrorE(err, "insert into migrate history failed.")
		return
	}
	migID = migrateHistory.ID
	return
}

func (c *CR) CheckMigHistoryInMigratingStatus(myRuntimeUUID constant.ContainerRuntimeUUID) (mig *containerModel.MigrationHistory, exist bool, err error) {
	mig = new(containerModel.MigrationHistory)

	err = db_helper.GlobalDBConn().Model(&containerModel.MigrationHistory{}).Debug().
		Where("status = ?", containerModel.Migrating).
		Where("source_runtime_uuid = ?", myRuntimeUUID).
		Last(&mig).
		Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			err = db_helper.GlobalDBConn().Model(&containerModel.MigrationHistory{}).Debug().
				Where("status = ?", containerModel.Migrating).
				Where("target_runtime_uuid = ?", myRuntimeUUID).
				Last(&mig).
				Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					return mig, false, nil
				}
				c.l.WithField("runtimeUUID", myRuntimeUUID).WarnE(err, "get migrate history by runtime uuid failed.")
				return mig, false, err
			}
			return mig, true, nil
		}
		c.l.WithField("runtimeUUID", myRuntimeUUID).WarnE(err, "get migrate history by runtime uuid failed.")
		return mig, false, err
	}

	return mig, true, nil
}

func (c *CR) UpdateMigHistoryResult(migID int, status containerModel.MigrateHistoryStatusType, msg string) (err error) {
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &containerModel.MigrationHistory{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": migID,
			},
		},
	}, map[string]interface{}{
		"status": status,
		"msg":    msg,
		"finished_at": sql.NullTime{
			Time:  c.t.Now(),
			Valid: true,
		},
	}).GetError()
	if err != nil {
		c.l.WithFields(logger.Fields{
			"Id":     migID,
			"Status": status,
		}).ErrorE(err, "update migrate result failed.")
		return
	}
	return
}

// RetryMigHistory 为了以后添加迁移重试
func (c *CR) RetryMigHistory(migID int) error {
	return c.UpdateMigHistoryResult(migID, containerModel.Migrating, "")
}

func (c *CR) InsertMigHistory(mig containerModel.MigrationHistory) (migID int, err error) {
	migH, exist, err := c.CheckMigHistoryInMigratingStatus(mig.SourceRuntimeUUID)
	if err != nil {
		return 0, err
	}

	if exist {
		return migH.ID, nil
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &containerModel.MigrationHistory{},
		InsertPayload:   &mig,
	}).GetError()
	if err != nil {
		c.l.WarnE(err, "insert migrate history failed.")
		return
	}
	return
}

func (c *CR) GetMigrateByTargetRuntimeUUID(runtimeUUID constant.ContainerRuntimeUUID) (migrationHistory *containerModel.MigrationHistory, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &containerModel.MigrationHistory{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"target_runtime_uuid": runtimeUUID}},
	}, &migrationHistory).GetError()
	if err != nil {
		c.l.WarnE(err, "get migrate history failed.")
		return
	}
	return
}

func (c *CR) GetContainerMigrateList(uid int, pagedReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*containerModel.MigrationHistory, err error) {
	var count int64
	db := db_helper.GlobalDBConn().Model(&containerModel.MigrationHistory{}).
		Where("uid = ?", uid).
		Where("deleted_at is null")

	err = db.Count(&count).Error
	if err != nil {
		c.l.WithError(err).Warn("Count failed.")
		return
	}

	paged = db_helper.BuildPagedDataUtil(pagedReq.PageIndex, pagedReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		c.l.WithError(err).Warn("Get user instance migrate record list by uid failed.")
		return
	}
	paged.List = list
	return
}

func (c *CR) GetContainerRunningErrRuntimeUuidList(runtimeUuidList []constant.ContainerRuntimeUUID) (missStatusRuntimeUuidList []constant.ContainerRuntimeUUID, err error) {
	for _, v := range runtimeUuidList {
		var lastStatusInfo, secondLastStatusInfo containerModel.StatusHistory
		err = db_helper.GlobalDBConn().Model(&containerModel.StatusHistory{}).
			Where("deleted_at IS NULL").
			Where("runtime_uuid = ?", v).
			Order("id DESC").
			Limit(1).
			Find(&lastStatusInfo).Error
		if err != nil {
			c.l.ErrorE(err, "get last status info failed")
			return
		}
		err = db_helper.GlobalDBConn().Model(&containerModel.StatusHistory{}).
			Where("deleted_at IS NULL").
			Where("runtime_uuid = ?", v).
			Order("id DESC").
			Offset(1).
			Limit(1).
			Find(&secondLastStatusInfo).Error
		if err != nil {
			c.l.ErrorE(err, "get second last status info failed")
			return
		}
		if lastStatusInfo.ContainerStatus == constant.ContainerStopped && secondLastStatusInfo.ContainerStatus == constant.ContainerRunning {
			missStatusRuntimeUuidList = append(missStatusRuntimeUuidList, v)
		}
	}

	return
}

// containerHookPubMQ 是一个容器勾子，用于将参数发布到 MQ 中，供业务层处理
func (c *CR) containerHookPubMQ(params containerModel.DoHookParams) error {
	b, _ := json.Marshal(params)
	// QUEUE pkg/constant/core.go
	err := message_model.SimplePubMessage(c.l, c.q, &queue_interface.NewQueueForCoreToBusinessUpdateContainerStatus{
		Tenant: params.Tenant,
		Reqs: []constant.MQCoreToBusinessUpdateContainerStatusReq{
			{
				Tenant:    params.Tenant,
				MachineID: params.Container.MachineID,
				OptType:   constant.MQCoreToBusinessUpdateContainerStatusOpt,
				Payload:   string(b),
				Caller:    constant.OptCallerUpdateContainerStatus,
			},
		},
	})
	if err != nil {
		c.l.WithFields(logger.Fields{
			"container_id":     params.RuntimeUUID,
			"container_source": params.Tenant,
			"do_hook_params":   params,
		}).ErrorE(err, "Pub message failed.")
		return err
	}
	return nil
}

// ---------------------------------------------------------------------------------------------------------------------

func (c *CR) operateContainer(req constant.OptContainerReq) (optErr error) {
	l := logger.NewLogger("ContainerOperator").WithFields(logger.Fields{
		"runtime_uuid": req.RuntimeUUID,
		"payload":      req.Payload,
		"caller":       req.Caller,
		"opt_at":       req.OptAt,
		"opt":          req.Opt,
	})

	var saveHistory = true
	var useful = true
	var agent AgentHandler

	req.OptAt = c.t.Now()

	defer func() {
		// 存储操作记录.
		if req.Opt.NeedHistory() && saveHistory && useful {
			var errMsg string
			if optErr != nil {
				l.WarnE(optErr, "Operate Container failed.")
				errMsg = optErr.Error()
			}
			_ = c.afterOperate(req, c.t.Now(), errMsg)
		}
	}()

	// agent handler
	agent, optErr = c.getAgentHandler(req, l)
	if optErr != nil {
		l.WarnE(optErr, "Get Agent Handler before operate container failed.")
		return optErr
	}

	// check caller
	if !c.canCallerDoThisReq(req, l) {
		return nil
	}

	// ctrl opt hooks
	useful, optErr = c.beforeCtrlOperate(req)
	if optErr != nil {
		l.WarnE(optErr, "Check before ctrl operate container failed.")
		return optErr
	}

	if !useful { // maybe container not found
		l.Info("Omit useless opt.")
		return nil
	}

	// handling
	h, ok := c.getHandler(req.Opt)
	if !ok {
		l.Error("Operate Container failed! Not supported opt_type!")
		saveHistory = false
		return nil
	}

	saveHistory, optErr = h(agent, req, l)
	if optErr != nil {
		l.WarnE(optErr, "Handle opt failed.")
		saveHistory = true
		return optErr
	}

	return nil
}

func (c *CR) RemoveContainerCache(req coreapi.RemoveContainerCacheReq) (err error) {
	con := containerModel.Container{}
	cronList := []containerModel.Container{}

	err = con.ContainerExec(nil).
		Where("assigned_remove_time BETWEEN ? AND ?", time.Now(), time.Now().Add(time.Duration(req.AssignHourNum)*time.Hour)).
		Where("deleted_at is null").
		FindInBatches(&cronList, 500, func(tx *gorm.DB, batch int) error {
			for _, container := range cronList {
				_ = container.LoadAllStructByJson()

				agentHandler := NewAgentHandler(container.MachineID, *container.ContainerParam, *container.RuntimeParam, c.t, c.q)

				err := agentHandler.PubRemoveContainerCommand()
				if err != nil {
					c.l.ErrorE(err, "Pub remove container cmd to agent failed.")
					continue
				}

				c.l.Info("Pub remove container cmd to agent.")
			}

			return nil
		}).Error
	if err != nil {
		c.l.ErrorE(err, "assigned remove container cache failed")
		return
	}

	return
}

func (c *CR) ContainerCacheRecovery(req coreapi.ContainerCacheRecoveryReq) (err error) {
	con := containerModel.Container{}

	err = con.GetContainer(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"runtime_uuid": req.RuntimeUuid,
		},
	})
	if err != nil {
		c.l.ErrorE(err, "get container failed")
		return
	}

	if con.AssignedRemoveTime == nil {
		c.l.Error("container not assigned remove")
		return biz.ErrInstanceIsNotInRemoveCache
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		conHis := containerModel.StatusHistory{}
		err = conHis.StatusHistoryUpdate(tx, db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": con.LatestStatusID,
			},
		}, map[string]interface{}{
			"container_status": "stopped",
			"message":          "从实例暂存池中恢复实例",
		})
		if err != nil {
			c.l.ErrorE(err, "update container status history failed")
			return err
		}

		// assigned_remove_time null
		// latest_status_id container_status_history
		err = con.ContainerUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"runtime_uuid": req.RuntimeUuid,
			},
		}, map[string]interface{}{
			"assigned_remove_time": nil,
			"status_at":            time.Now(),
			"stopped_at":           time.Now().Add(-2 * 24 * time.Hour),
		})
		if err != nil {
			c.l.ErrorE(err, "update container failed")
			return err
		}

		return
	})
	if err != nil {
		return err
	}

	return
}
