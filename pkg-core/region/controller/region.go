package controller

import (
	"server/pkg-core/api/coreapi"
	"server/pkg-core/module_definition"
	"server/pkg-core/region/model"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/pkg/logger"

	"github.com/gin-gonic/gin"
)

const ModuleName = "region_controller"

type RegionController struct {
	logger        *logger.Logger
	regionService module_definition.RegionInterface
}

func NewRegionControllerProvider(regionService module_definition.RegionInterface) *RegionController {
	return &RegionController{
		logger:        logger.NewLogger(ModuleName),
		regionService: regionService,
	}
}

func (ctrl *RegionController) Create(c *gin.Context) {
	var req coreapi.CreateRegionReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	region := &model.Region{
		Sign:                   req.Sign,
		Name:                   req.Name,
		DataCenter:             req.DataCenter,
		PortRangeMin:           req.PortRangeMin,
		PortRangeMax:           req.PortRangeMax,
		ExportAddr1:            req.ExportAddr1,
		ExportAddr2:            req.ExportAddr2,
		ProxyToken:             req.ProxyToken,
		IsNFSAvailable:         req.IsNFSAvailable,
		NFSAddr:                req.NFSAddr,
		NFSPort:                req.NFSPort,
		NFSHealth:              req.NFSHealth,
		DefaultUserQuotaInByte: req.DefaultUserQuotaInByte,
		Rank:                   req.Rank,
		ADFSFilerAddr:          req.ADFSFilerAddr,
		ADFSPubAddr:            req.ADFSPubAddr,
		ADFSPubPort:            req.ADFSPubPort,
	}
	region, err := ctrl.regionService.CreateRegion(region)
	if err != nil {
		ctrl.logger.ErrorE(err, "create region failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, region)
}

func (ctrl *RegionController) GetRegionDetail(c *gin.Context) {
	var req coreapi.GetRegionDetailReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	region, err := ctrl.regionService.GetRegionDetail(req.RegionSignType)
	if err != nil {
		ctrl.logger.ErrorE(err, "get region detail failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, region)
}

func (ctrl *RegionController) GetRegionList(c *gin.Context) {
	regions, err := ctrl.regionService.GetRegionList()
	if err != nil {
		ctrl.logger.ErrorE(err, "get region list failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, regions)
}

func (ctrl *RegionController) GetRegionDetailWithStorageInfo(c *gin.Context) {
	var req coreapi.GetRegionDetailWithStorageInfoReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	region, storage, err := ctrl.regionService.GetRegionDetailWithStorageInfo(req.RegionSignType)
	if err != nil {
		ctrl.logger.ErrorE(err, "get region detail failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.GetRegionDetailWithStorageInfoData{
		Region:      region,
		StorageList: storage,
	}
	http.SendOK(c, data)
}

func (ctrl *RegionController) GetStorageDetail(c *gin.Context) {
	var req coreapi.GetStorageDetailReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	storage, err := ctrl.regionService.GetStorageDetail(req.SignType)
	if err != nil {
		ctrl.logger.ErrorE(err, "get storage detail failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, storage)
}

func (ctrl *RegionController) CheckRegionSignExist(c *gin.Context) {
	var req coreapi.CheckRegionSignExistReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	exist, err := ctrl.regionService.CheckRegionSignExist(req.RegionSignType)
	if err != nil {
		ctrl.logger.ErrorE(err, "get storage detail failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckRegionSignExistData{
		Exist: exist,
	}
	http.SendOK(c, data)
}

func (ctrl *RegionController) CheckRegionNameExist(c *gin.Context) {
	var req coreapi.CheckRegionNameExistReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	exist, err := ctrl.regionService.CheckRegionNameExist(req.Name)
	if err != nil {
		ctrl.logger.ErrorE(err, "get storage detail failed")
		http.SendError(c, err)
		return
	}
	data := coreapi.CheckRegionNameExistData{
		Exist: exist,
	}
	http.SendOK(c, data)
}
func (ctrl *RegionController) GetRegionListForIndex(c *gin.Context) {
	var req coreapi.GetRegionListForIndexReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.logger.Warn(err.Error())
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	res, err := ctrl.regionService.GetRegionListForIndex()
	if err != nil {
		ctrl.logger.ErrorE(err, "GetRegionListForIndex failed")
		http.SendError(c, err)
		return
	}
	http.SendOK(c, res)
}
