package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameFileStorage = "core_region_file_storage"

/**
 * 文件存储, 实际上是 region_file_storage 的关系表
 */

type FileStorage struct {
	ID        int       `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:datetime;column:updated_at;" json:"updated_at"` // 初始化日期

	// 绑定关系
	UID        int                            `gorm:"column:uid;index:uid_region;" json:"uid"`
	RegionSign constant.RegionSignType        `gorm:"column:sign;type:varchar(255);index:uid_region;" json:"sign"`
	Region     *Region                        `gorm:"-" json:"-"`
	OrderUUID  string                         `gorm:"type:varchar(255);column:order_uuid" json:"order_uuid"` // 所属订单
	FsType     constant.FsType                `gorm:"column:fs_type;type:varchar(255)" json:"fs_type"`
	Status     constant.FileStorageStatusType `gorm:"column:status;type:varchar(255)" json:"status"` // 创建状态
	Tenant     string                         `gorm:"type:varchar(100);default '';" json:"tenant"`   // 租户，例如 autodl、海外站等

	/* adfs autofs 公共项, 部分字段根据 FsType 而有不同含义
	// FsType 			此条记录代表的文件存储类型
	// QuotaTotal 		用量配额 单位:B
	// QuotaInodeTotal 	inode总配额 单位:个
	// ConcurrentLimit	并发量，限制读写速度，单位:adfs下100MB/s，autofs下Mb/s
	// QuotaUsage		使用量 单位:B
	// QuotaMaxUsage	每日峰值使用量,每日更新,用以计费 单位:B
	// QuotaUsageRate	使用率 97 = 97%（保留小数点后两位*100 后写入 DB）
	// QuotaInodeUsage	inode使用量 单位:个
	*/
	QuotaTotal      int64 `gorm:"column:quota_total" json:"quota_total"`
	QuotaInodeTotal int64 `gorm:"column:quota_inode_total" json:"quota_inode_total"`
	ConcurrentLimit int   `gorm:"column:concurrent_limit;" json:"concurrent_limit"`
	QuotaUsage      int64 `gorm:"column:quota_usage" json:"quota_usage"`
	QuotaMaxUsage   int64 `gorm:"column:quota_max_usage" json:"quota_max_usage"`
	QuotaUsageRate  int   `gorm:"column:quota_usage_rate" json:"quota_usage_rate"`
	QuotaInodeUsage int64 `gorm:"column:quota_inode_usage" json:"quota_inode_usage"`

	/* autofs单独设置项
	// MaxUploads 挂载程序最大上传并发数 单位:个
	// CacheSize  挂载程序可使用缓存大小 单位:GB
	// BufferSize 挂载程序缓冲区大小	 单位:MB
	// AutoFsPrefix 会存在两个地区使用同一套底层存储但是不同的meta引擎的情况, 此时需要对bucket名称做区分
		当此字段不为空时, 命令规则为{AutoFsPrefix}{uid}, 默认为fs{uid}
	// NoBGJob 	  disable autofs background jobs (clean-up, backup, etc.)
	*/
	MaxUploads   int    `gorm:"column:max_uploads" json:"max_uploads"`
	CacheSize    int    `gorm:"column:cache_size" json:"cache_size"`
	BufferSize   int    `gorm:"column:buffer_size" json:"buffer_size"`
	AutoFsPrefix string `gorm:"column:autofs_prefix;type:varchar(255)" json:"autofs_prefix"`
	NoBGJob      bool   `gorm:"column:no_bgjob;type:tinyint(1);default 0" json:"no_bgjob"`

	// FsConfigVersion 参考 Region 表中此字段。
	FsConfigVersion string `gorm:"column:fs_config_version;type:varchar(255);not null;default:'';" json:"fs_config_version"`
}

func (b *FileStorage) TableName() string {
	return TableNameFileStorage
}

func (b *FileStorage) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&FileStorage{})
}

type RegionFileStorageInfo struct {
	Sign        constant.RegionSignType        `json:"sign"`
	Name        string                         `json:"name"`
	ExportAddr1 string                         `json:"export_addr_1"`
	ExportAddr2 string                         `json:"export_addr_2"`
	Status      constant.FileStorageStatusType `json:"status"`
	Port        int                            `json:"port"`
	FsType      constant.FsType                `json:"fs_type"`

	FreeSize    int64 `json:"free_size"`    // 免费大小
	MaxSize     int64 `json:"max_size"`     // 最大容量
	DefaultSize int64 `json:"default_size"` // 默认容量

	InitializedAt *time.Time `json:"initialized_at"` // 初始化时间, 未初始化的为nil
}

func (b *FileStorage) FileStorageGet(filter *db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: b,
		Filters:         *filter,
	}, &b).GetError()
}

func (b *FileStorage) FileStorageUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, m interface{}) (err error) {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         b,
		Filters:                 *filter,
	}, m).GetError()
}

func (b *FileStorage) FileStorageGetAllWithSelect(filter *db_helper.QueryFilters, sel string, result interface{}) (err error) {
	return db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: b,
		Filters:         *filter,
		Select:          sel,
		NoLimit:         true,
	}, result).GetError()
}
