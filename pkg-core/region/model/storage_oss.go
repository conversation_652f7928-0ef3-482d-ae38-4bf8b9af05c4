package model

import (
	"server/pkg-agent/agent_constant"
	"time"

	"gorm.io/gorm"
)

const TableNameStorageOSS = "core_storage_oss"

type StorageOSS struct {
	ID        int       `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
	UpdatedAt time.Time `gorm:"type:datetime;column:updated_at;" json:"updated_at"`

	Sign agent_constant.StorageOSSSignType `gorm:"column:sign;type:varchar(255);index" json:"sign"` // 地区标识
	Name string                            `gorm:"column:name;type:varchar(255)" json:"name"`       // 地区名称

	// 公网地址
	PublicAddr string `gorm:"column:public_addr" json:"public_addr"`
	// 内网地址
	PrivateAddr string `gorm:"column:private_addr" json:"private_addr"`

	AccessKey string `gorm:"type:varchar(255);column:access_key;" json:"access_key"`
	SecretKey string `gorm:"type:varchar(255);column:secret_key;" json:"secret_key"`

	// 有创建bucket，删除object权限
	AdminAccessKey string `gorm:"type:varchar(255);column:admin_access_key;" json:"admin_access_key"`
	AdminSecretKey string `gorm:"type:varchar(255);column:admin_secret_key;" json:"admin_secret_key"`

	// 0-100 -> 0% - 100%
	UsedPercent int `gorm:"column:used_percent" json:"used_percent"`

	// 存储镜像的bucket name
	ImageBucketName string `gorm:"type:varchar(255);column:image_bucket_name;" json:"image_bucket_name"`
}

func (r *StorageOSS) TableName() string {
	return TableNameStorageOSS
}

func (r *StorageOSS) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&StorageOSS{})
}
