package service

import (
	"server/conf"
	"server/pkg-agent/agent_constant"
	"server/pkg-core/region/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/kv_plugin"
	"server/plugin/queue"
	redisPlugin "server/plugin/redis_plugin"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

const ModuleName = "region_service"

type RegionService struct {
	storagePlugin *redisPlugin.RegionPlugin
	log           *logger.Logger
	queue         *queue.Q
	kv            *kv_plugin.KVPlugin
	mutex         *redisPlugin.MutexRedis
}

func NewRegionServiceProvider(
	self *RegionService,
	q *queue.Q,
	redis *redisPlugin.RegionPlugin,
	kv *kv_plugin.KVPlugin,
	mutex *redisPlugin.MutexRedis,
) *RegionService {
	self = &RegionService{
		log:           logger.NewLogger(ModuleName),
		queue:         q,
		storagePlugin: redis,
		kv:            kv,
		mutex:         mutex,
	}
	return self
}

// crud

// CreateRegion 创建地区
func (svc *RegionService) CreateRegion(newRegion *model.Region) (res *model.Region, err error) {
	if newRegion == nil || !newRegion.InspectionPass() {
		err = businesserror.ErrInvalidRequestParams
		return
	}

	exist := false
	exist, err = svc.CheckRegionSignExist(newRegion.Sign)
	if err != nil {
		return
	}
	if exist {
		err = businesserror.ErrRegionSionAlreadyExist
		return
	}
	exist, err = svc.CheckRegionNameExist(newRegion.Name)
	if err != nil {
		return
	}
	if exist {
		err = businesserror.ErrRegionNameAlreadyExist
		return
	}

	var queueTxMsgUUIDList []string
	defer func() {
		if len(queueTxMsgUUIDList) > 0 {
			errMsg := svc.queue.Pub(queueTxMsgUUIDList...)
			if len(errMsg) != 0 {
				logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s]", errMsg)
			}
		}
	}()

	errDB := db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			ModelDefinition: &model.Region{},
			InsertPayload:   newRegion,
		})
		if errDB.IsNotNil() {
			svc.log.WithError(errDB.GetError()).Error("insert region failed")
			return
		}

		queueTxMsgUUIDList, errDB = svc.pubInsertRegion(tx, newRegion.Map())
		if errDB.IsNotNil() {
			svc.log.WithField("err", errDB.GetError()).Error("pub mq insert region failed.")
			return
		}

		return
	})
	if errDB.IsNotNil() {
		err = businesserror.ErrDatabaseError
		return
	}

	return newRegion, nil
}

func (svc *RegionService) updateRegionHealthStatus(rs constant.RegionSignType, status constant.NFSMachineStatus) (err error) {
	var queueTxMsgUUIDList []string
	defer func() {
		if len(queueTxMsgUUIDList) > 0 {
			errMsg := svc.queue.Pub(queueTxMsgUUIDList...)
			if len(errMsg) != 0 {
				logger.NewLogger("TxNowPubMessage").WithField("queueTxMsgUUIDList", queueTxMsgUUIDList).Warn("SimplePubMessage pub msg failed [%s]", errMsg)
			}
		}
	}()

	errDB := db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.UpdateOne(db_helper.QueryDefinition{
			ModelDefinition: &model.Region{},
			Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
		}, map[string]interface{}{"nfs_health": status})
		if errDB.IsNotNil() {
			svc.log.WithError(err).WithField("sign", rs).WithField("status", status).Error("update region health status failed.")
			return
		}

		updateMap := map[string]interface{}{
			"sign":       rs,
			"nfs_health": status,
		}
		queueTxMsgUUIDList, errDB = svc.pubUpdateRegion(tx, updateMap)
		if errDB.IsNotNil() {
			svc.log.WithField("err", errDB.GetError()).Error("pub mq insert region failed.")
			return
		}

		return
	})
	if errDB.IsNotNil() {
		return errDB.GetError()
	}

	return
}

func (svc *RegionService) GetRegionDetail(rs constant.RegionSignType) (region *model.Region, err error) {
	region = new(model.Region)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &region).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrRegionSionNotFound
			return
		}
		svc.log.WithError(err).WithField("sign", rs).Error("get region by sign failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *RegionService) GetRegionDetailWithStorageInfo(rs constant.RegionSignType) (region *model.Region, storage model.RegionStorageOSSDetailList, err error) {
	region = new(model.Region)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &region).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrRegionSionNotFound
			return
		}
		svc.log.WithError(err).WithField("sign", rs).Error("get region by sign failed")
		err = businesserror.ErrDatabaseError
		return
	}
	storage, err = model.GetRegionStorageOssList(rs)
	svc.log.WithFields(logger.Fields{
		"region":  region,
		"storage": storage,
		"err":     err,
	}).Info("-------------GetRegionStorageOssList")
	return
}

func (svc *RegionService) GetStorageDetail(rs agent_constant.StorageOSSSignType) (oss *model.StorageOSS, err error) {
	oss = new(model.StorageOSS)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.StorageOSS{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &oss).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrRegionSionNotFound
			return
		}
		svc.log.WithError(err).WithField("sign", rs).Error("get storage oss by sign failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *RegionService) GetRegionList() (regions []model.Region, err error) {
	// 不超过 100 个
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{Orders: []string{"rank desc"}},
	}, &regions).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get regions failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *RegionService) CheckRegionSignExist(rs constant.RegionSignType) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"sign": rs}},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Warn("count region by sign failed.")
		err = businesserror.ErrDatabaseError
		return
	}

	exist = count != 0
	return
}

func (svc *RegionService) CheckRegionNameExist(name string) (exist bool, err error) {
	var count int64
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.Region{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"name": name}},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("count region by name failed")
		err = businesserror.ErrDatabaseError
		return
	}

	exist = count != 0
	return
}

func (svc *RegionService) GetNetDiskListForUser(uid int) (res []model.RegionUsageInfo, err error) {
	var (
		regions          []model.Region
		usageMap         = make(map[string]string)
		netDisks         []model.NetDisk
		netDiskStatusMap = make(map[constant.RegionSignType]constant.NetDiskInitStatusType)
	)

	// region 列表
	regions, err = svc.GetRegionList()
	if err != nil {
		return
	}

	// redis usage信息
	usageMap, err = svc.storagePlugin.UsageGetAll(uid)
	if err != nil {
		svc.log.WithError(err).Error("get nfs storage usage info from storagePlugin failed")
		err = businesserror.ErrInternalError
		return
	}

	// 各地区网盘初始化情况
	netDisks, err = svc.getUserNetDiskInfo(uid)
	if err != nil {
		return
	}
	for _, v := range netDisks {
		netDiskStatusMap[v.RegionSign] = v.Status
	}

	for _, v := range regions {
		if !v.IsNFSAvailable || !v.NFSVisibleForFrontend {
			continue
		}

		// 整理初始化状态
		var nfsStatus constant.NetDiskInitStatusType
		if status, ok := netDiskStatusMap[v.Sign]; ok {
			nfsStatus = status
		} else {
			nfsStatus = constant.NetDiskUninitialized
		}

		res = append(res, model.RegionUsageInfo{
			Sign:                   v.Sign,
			Name:                   v.Name,
			ExportAddr1:            v.ExportAddr1,
			ExportAddr2:            v.ExportAddr2,
			DefaultUserQuotaInByte: v.DefaultUserQuotaInByte,
			NfsStatus:              nfsStatus,
			NFSPort:                v.NFSPort,
			Usage:                  usageMap[v.Sign.String()],
		})
	}

	return
}

func (svc *RegionService) GetRegionListForIndex() (res []model.GetRegionListForIndexRes, err error) {
	var (
		regionList []model.Region
	)
	regionList, err = svc.GetRegionList()
	if err != nil {
		return
	}

	for _, v := range regionList {
		res = append(res, model.GetRegionListForIndexRes{RegionSign: v.Sign, RegionName: v.Name, DataCenter: v.DataCenter})
	}
	return
}

func (svc *RegionService) getUserNetDiskInfo(uid int) (netDisk []model.NetDisk, err error) {
	// 不超过 100 个
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.NetDisk{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &netDisk).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get regions failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// agent

func (svc *RegionService) AuthAgent(token string) (err error) {
	// FIXME: 此处临时套用 frps 的 token
	if token != conf.GetGlobalGsConfig().Frps.Token {
		svc.log.WithField("token", token).Info("Invalid storage agent token.")
		return businesserror.ErrAuthorizeFailed
	}
	return
}

func (svc *RegionService) RegisterAgent(rs constant.RegionSignType, token string) error {
	if err := svc.AuthAgent(token); err != nil {
		return err
	}

	// NOTE: 目前尚无其他特殊操作.

	return nil
}

func (svc *RegionService) CheckAgentRealHealth(rs constant.RegionSignType) bool {
	// TODO:
	return true
}

func (svc *RegionService) SetStorageAgentOnlineOnce(rs constant.RegionSignType) {
	status, err := svc.storagePlugin.StatusGet(rs)
	if err != nil {
		if err == redis.Nil {
			status = &constant.NFSMachineStatusInfo{
				Status:   constant.NFSMachineHealth,
				UpdateAt: time.Now(),
			}
			err = nil
		} else {
			svc.log.WithError(err).Warn("storagePlugin get nfs machine health status failed")
			return
		}

	}
	svc.UpdateNfsStatus(rs, status, constant.NFSMachineHealth, false, "")
	return
}

func (svc *RegionService) DistributeFrpcProxy(rs constant.RegionSignType) (proxyHost string, proxyPort, proxyApiPort int, proxyToken string, region *model.Region, err error) {
	if len(rs) == 0 {
		rs = constant.RegionDefault
	}

	region, err = svc.GetRegionDetail(rs)
	if err != nil {
		svc.log.ErrorE(err, "Get region failed. region_sign: %s", rs)
		return
	}

	proxyHost = svc.selectExportAddr(rs, region.ExportAddr1, region.ExportAddr2) // select one addr
	if region.ProxyPort != 0 {
		proxyPort = region.ProxyPort
	} else {
		proxyPort = constant.FrpcDefaultProxyPort // default 7000
	}
	proxyApiPort = region.NFSPort  // storage agent api
	proxyToken = region.ProxyToken // default seetatech666
	return
}

func (svc *RegionService) selectExportAddr(rs constant.RegionSignType, addr1, addr2 string) string {
	// 用于 jupyter 等, 封装选择出口地址的方法, 目前默认用第一个, 以后更改策略时修改此处.
	// 前端在网盘列表中可以获得所有出口地址, 由前端自行判断.
	return addr1
}
