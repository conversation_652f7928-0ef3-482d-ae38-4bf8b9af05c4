package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg/businesserror"
	"server/pkg/http"
	"server/plugin/tencent_oss"
	"time"
)

func (ctrl *FileController) Upload(c *gin.Context) {
	formList, err := c.MultipartForm()
	if err != nil {
		ctrl.log.Info("multipart Form failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetUserInfo(c)
	files := formList.File["file"]
	widthL := formList.Value["width"]
	heightL := formList.Value["height"]

	width := ""
	height := ""
	if widthL != nil && len(widthL) > 0 {
		width = widthL[0]
	}
	if heightL != nil && len(heightL) > 0 {
		height = heightL[0]
	}

	uuidMap, err := ctrl.file.Upload(u.UID, files, width, height)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, uuidMap)
}

func (ctrl *FileController) Download(c *gin.Context) {
	fileName := c.Param("file")
	if fileName == "" {
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	obj, err := tencent_oss.Download(fileName)
	if err != nil {
		http.SendError(c, businesserror.ErrFileNotExist)
		return
	}
	defer obj.Body.Close()
	c.DataFromReader(200,
		obj.ContentLength,
		obj.Header.Get("Content-Type"),
		obj.Body, map[string]string{"Expires": time.Now().Add(time.Hour * 24 * 30).Format(time.RFC1123)})
}
