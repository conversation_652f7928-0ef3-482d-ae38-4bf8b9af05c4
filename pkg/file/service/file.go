package service

import (
	"mime/multipart"
	"path/filepath"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/libs"
	"server/plugin/tencent_oss"
)

func (svc *FileService) Upload(uid int, files []*multipart.FileHeader, width, height string) (urlMap map[string]string, err error) {
	urlMap = make(map[string]string)
	err = svc.uploadCheck(files)
	if err != nil {
		return
	}

	for _, fileHeader := range files {
		urlMap[fileHeader.Filename] = ""

		// 构造uuid
		name := libs.GenUserFileName(uid) + filepath.Ext(fileHeader.Filename)
		err = svc.doUpload(name, fileHeader)
		if err != nil {
			svc.log.ErrorE(err, "upload failed")
			continue
		}
		//if width != "" && height != "" {
		//	name = fmt.Sprintf("%s?width=%s&height=%s", name, width, height)
		//}
		urlMap[fileHeader.Filename] = name
	}
	return
}

func (svc *FileService) uploadCheck(files []*multipart.FileHeader) (err error) {
	for _, fileHeader := range files {
		// 校验后缀
		fileExt := filepath.Ext(fileHeader.Filename)
		fileType := ""
		if len(fileHeader.Header["Content-Type"]) != 0 {
			fileType = fileHeader.Header["Content-Type"][0]
		}
		if !libs.AllowExt(fileExt) || !libs.AllowFileType(fileType) {
			svc.log.WithField("ext", fileExt).WithField("type", fileType).Info("ext now allow")
			err = businesserror.ErrFileTypeError
			return
		}
		if fileHeader.Size > constant.MaxImageSize {
			err = businesserror.ErrFileMax10M
			return
		}
	}
	return
}

func (svc *FileService) doUpload(name string, file *multipart.FileHeader) (err error) {
	reader, err := file.Open()
	if err != nil {
		svc.log.ErrorE(err, "open file failed")
		return
	}
	defer reader.Close()

	uploadParams := &constant.UploadParams{
		ObjName:     name,
		ContentType: file.Header["Content-Type"][0],
		File:        reader,
		Size:        file.Size,
	}
	err = tencent_oss.Upload(uploadParams)

	if err != nil {
		svc.log.WithField("name", name).ErrorE(err, "upload file to seaweed failed")
		err = businesserror.ErrFileUploadFailed
		return

	}
	return
}
