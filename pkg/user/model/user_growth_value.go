package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableUserGrowthValue string = "user_growth_value"

type UserGrowthValue struct {
	Id        int        `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time  `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time  `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	ExpireAt  *time.Time `gorm:"type:datetime;column:expire_at" json:"expire_at"`

	GrowthValue int64                        `gorm:"column:growth_value" json:"growth_value"` // 获取成长值
	ValueWay    constant.GrowthValueOptTypes `gorm:"column:value_way" json:"value_way"`       // 获取成长值的方式

	UserId int `gorm:"column:user_id" json:"user_id"` // 操作对应的用户id
}

func (u *UserGrowthValue) TableName() string {
	return TableUserGrowthValue
}

// Init 实现 db_helper 接口.
func (u *UserGrowthValue) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserGrowthValue{})
}
