package model

import (
	"gorm.io/gorm"
	"server/pkg/db_helper"
	"time"
)

const TableUserInvite = "user_invite"

type UserInvite struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	InviteUserId  int `gorm:"column:invite_user_id" json:"invite_user_id"`   // 邀请者id
	InvitedUserId int `gorm:"column:invited_user_id" json:"invited_user_id"` // 被邀请用户id
}

func (u *UserInvite) TableName() string {
	return TableUserInvite
}

// Init 实现 db_helper 接口.
func (u *UserInvite) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserInvite{})
}

func (u *UserInvite) UserInviteCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
	}).GetError()
}
