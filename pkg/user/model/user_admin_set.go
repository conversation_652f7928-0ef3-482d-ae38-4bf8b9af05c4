package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

const TableLevelAdminSet string = "level_admin_set"

type LevelAdminSet struct {
	Id        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	UserId         int                      `gorm:"column:user_id" json:"user_id"`                               // 操作对应的用户id
	MemberLevel    constant.MemberLevelName `gorm:"type:varchar(255);column:member_level" json:"member_level"`   // 用户等级
	MemberDateline *time.Time               `gorm:"type:datetime;column:member_dateline" json:"member_dateline"` // 会员截止日期(针对于管理员赋予的)
}

func (u *LevelAdminSet) TableName() string {
	return TableLevelAdminSet
}

// Init 实现 db_helper 接口.
func (u *LevelAdminSet) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&LevelAdminSet{})
}
