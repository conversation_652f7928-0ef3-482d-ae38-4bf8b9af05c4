package model

import (
	"encoding/json"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	redisPlugin "server/plugin/redis_plugin"
	"strings"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameUser string = "user"

type User struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	// 用户基本信息
	UUID                       string                            `gorm:"column:uuid;type:varchar(255);" json:"uuid"` // 第三方用户登录的凭证(多个三方登录绑定同一个uuid)
	Username                   string                            `gorm:"column:username;type:varchar(255);" json:"username"`
	Password                   string                            `gorm:"column:password;type:varchar(255);" json:"-"`
	Nickname                   string                            `gorm:"column:nickname;type:varchar(255);" json:"nickname"` // 获取到的微信用户昵称进行覆盖
	Status                     UserStatusType                    `gorm:"column:status;type:varchar(255);" json:"status"`     // 用户当前状态
	LoggedInAt                 *time.Time                        `gorm:"column:logged_in_at;" json:"logged_in_at"`
	Email                      string                            `gorm:"column:email;type:varchar(255);" json:"email"`
	PhoneArea                  string                            `gorm:"column:phone_area;type:varchar(255)" json:"phone_area"`
	Phone                      string                            `gorm:"column:phone;type:varchar(255)" json:"phone"`
	IsAdmin                    bool                              `gorm:"column:is_admin;type:tinyint(1)" json:"is_admin"`             // 普通管理员
	IsSuperAdmin               bool                              `gorm:"column:is_super_admin;type:tinyint(1)" json:"is_super_admin"` // 是否是超级管理员
	BackstageRole              constant.BackstageRole            `gorm:"column:backstage_role;type:varchar(255)" json:"backstage_role"`
	Sha1Password               string                            `gorm:"column:sha1_password;type:varchar(255);" json:"-"`                                // 加密后的密码
	EmailConfirmed             bool                              `gorm:"column:email_confirmed;type:tinyint(1)" json:"email_confirmed"`                   // 邮箱是否绑定过
	IdleJobAuthority           bool                              `gorm:"column:idle_job_authority;type:tinyint(1)" json:"idle_job_authority"`             // 用户是否有开启闲时作业的权限
	MountNetDiskAuthority      bool                              `gorm:"column:mount_net_disk_authority;type:tinyint(1)" json:"mount_net_disk_authority"` // 用户是否挂载网盘
	InvitationCode             string                            `gorm:"type:varchar(255);column:invitation_code" json:"invitation_code"`                 // 用户邀请码
	OpenId                     string                            `gorm:"column:open_id;type:varchar(255);" json:"open_id"`                                // 微信openID
	OpenIdBindAt               *time.Time                        `gorm:"column:open_id_bind_at" json:"open_id_bind_at"`                                   // 微信openID绑定时间
	UnionId                    string                            `gorm:"column:union_id;type:varchar(255);" json:"union_id"`                              // 多个应用及程序下的公共id
	AppletOpenId               string                            `gorm:"column:applet_open_id;type:varchar(255);" json:"applet_open_id"`                  // 用户在小程序中的openId
	AvatarId                   int                               `gorm:"column:avatar_id" json:"avatar_id"`                                               // 用户头像id
	RegisterIp                 string                            `gorm:"column:register_ip;type:varchar(255);" json:"register_ip"`                        // 用户注册时的ip
	MaxInstanceNum             int64                             `gorm:"type:int;column:max_instance_num;default 0;" json:"max_instance_num"`
	MaxRegionInstanceNumJson   datatypes.JSON                    `gorm:"column:max_region_instance_num;type:json" json:"-"`
	MaxRegionInstanceNumEntity map[constant.RegionSignType]int64 `gorm:"-" json:"max_region_instance_num"`
	SubUserNum                 int                               `gorm:"column:sub_user_num;default 0" json:"sub_user_num"`
	PrivateImageNum            int                               `gorm:"column:private_image_num;default 3" json:"private_image_num"`
	StartNoGpuNum              int                               `gorm:"column:start_no_gpu_num;default 1" json:"start_no_gpu_num"`
	MaxFileTransferPerDay      int                               `gorm:"type:int;column:max_file_transfer_per_day;default 0;" json:"max_file_transfer_per_day"`

	VisibleADFS            bool   `gorm:"column:visible_adfs;type:tinyint(1)" json:"visible_adfs"`                           // 用户看见文件存储功能
	MountADFSDiskAuthority bool   `gorm:"column:mount_adfs_disk_authority;type:tinyint(1)" json:"mount_adfs_disk_authority"` // 用户是否挂载网盘
	ADFSQuotaSize          string `gorm:"type:varchar(255);column:adfs_quota_size" json:"adfs_quota_size"`                   // 用户size上限
	ADFSQuotaInode         int    `gorm:"type:int;column:adfs_quota_inode" json:"adfs_quota_inode"`                          // 用户size上限

	RealNameAuth bool   `gorm:"column:real_name_auth;type:tinyint(1)" json:"real_name_auth"`
	RealName     string `gorm:"column:real_name;type:varchar(255)" json:"-"`
	IDNumber     string `gorm:"column:id_number;type:varchar(255)" json:"-"`

	GrowthValue    int64                    `gorm:"column:growth_value" json:"growth_value"`                       // 用户计算的成长值
	MemberLevel    constant.MemberLevelName `gorm:"type:varchar(255);column:member_level" json:"member_level"`     // 用户等级
	LevelAcqMode   constant.MemberGainWays  `gorm:"type:varchar(255);column:level_acq_mode" json:"level_acq_mode"` // 用户等级获取方式
	MemberDateline *time.Time               `gorm:"type:datetime;column:member_dateline" json:"member_dateline"`   // 会员截止日期(针对于管理员赋予的)
	PasswordSet    bool                     `gorm:"-" json:"password_set"`

	UpdatePasswordKey string `gorm:"column:update_password_key;type:varchar(255);not null;default:'';" json:"update_password_key"` // 用户修改密码的标识

	SettingJson datatypes.JSON  `gorm:"column:setting;type:json" json:"-"`
	Setting     UserSettingInfo `gorm:"-" json:"setting"`

	LimitRecharge bool `gorm:"column:limit_recharge;type:tinyint(1)" json:"limit_recharge"`
	CloneToOther  bool `gorm:"column:clone_to_other" json:"clone_to_other"` // 设置是否展示克隆给他人
}

type UserSettingInfo struct {
	MaxClonePerDay int `json:"max_clone_per_day"`
}

func (u UserSettingInfo) GetMaxClonePerDay() int {
	if u.MaxClonePerDay == 0 {
		return constant.UserMaxFileTransferPerDay
	}
	return u.MaxClonePerDay
}

func (u *User) TableName() string {
	return TableNameUser
}

// Init 实现 db_helper 接口.
func (u *User) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&User{})
}

func (u *User) GetMaxInstanceNum(region constant.RegionSignType) (totalLimit, regionLimit int64) {
	totalLimit = u.MaxInstanceNum
	if totalLimit == 0 {
		totalLimit = constant.UserMaxInstanceNum
	}

	regionLimit = u.MaxRegionInstanceNumEntity[region]
	return
}

func (u *User) GetMaxFileTransferPerDay() int {
	if u.MaxFileTransferPerDay == 0 {
		return constant.UserMaxFileTransferPerDay
	}

	return u.MaxFileTransferPerDay
}

func (u *User) GetStartNoGpuNum() int {
	if u.StartNoGpuNum == 0 {
		return constant.ContainerRunningNonGPULimit
	}
	return u.StartNoGpuNum
}

func (u *User) GetOpenIDBindAt() time.Time {
	if u.OpenIdBindAt == nil {
		return u.CreatedAt
	}
	return *u.OpenIdBindAt
}

type UserRedisCache struct {
	ID       int    `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	Phone    string `gorm:"column:phone" json:"phone"`
	Username string `gorm:"column:username" json:"username"`
}

func (u *User) BuildSubUserName(name string) string {
	s := strings.Split(u.UUID, "-")
	return name + "@" + s[len(s)-1]
}

func (u *User) BeforeCreate(db *gorm.DB) error {
	u.MaxInstanceNum = constant.UserMaxInstanceNum
	u.PrivateImageNum = constant.UserMaxImageNum
	if u.MaxRegionInstanceNumEntity == nil {
		u.MaxRegionInstanceNumEntity = map[constant.RegionSignType]int64{}
	}
	u.MaxRegionInstanceNumJson, _ = json.Marshal(u.MaxRegionInstanceNumEntity)
	if u.Username == "" && u.Phone != "" {
		u.Username = constant.GetUserName(u.Phone)
	}

	u.SettingJson, _ = json.Marshal(u.Setting)
	return nil
}

func (u *User) AfterFind(db *gorm.DB) error {
	if u.Sha1Password != "" {
		u.PasswordSet = true
	}
	u.MaxRegionInstanceNumEntity = map[constant.RegionSignType]int64{}
	if len(u.MaxRegionInstanceNumJson) != 0 {
		_ = json.Unmarshal(u.MaxRegionInstanceNumJson, &u.MaxRegionInstanceNumEntity)
	}

	if len(u.SettingJson) != 0 {
		_ = json.Unmarshal(u.SettingJson, &u.Setting)
	}
	return nil
}

func (u *User) UserCreate(tx *gorm.DB) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
	}).GetError()
	return
}

func (u *User) UserUpdate(tx *gorm.DB, filter db_helper.QueryFilters, up interface{}) (err error) {
	// 清除缓存
	if u.ID == 0 {
		if ui, ok := filter.EqualFilters["id"]; ok {
			switch ui.(type) {
			case int:
				u.ID = ui.(int)
			}
		}
	}

	if u.Phone == "" {
		if pi, ok := filter.EqualFilters["phone"]; ok {
			switch pi.(type) {
			case string:
				u.Phone = pi.(string)
			}
		}
	}

	redisPlugin.ClearUserCache(u.ID, u.Phone)

	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 filter,
	}, up).GetError()

	redisPlugin.ClearUserCache(u.ID, u.Phone)
	return
}

func (u *User) UserCount(filter db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         filter,
	}, &count).GetError()
	return
}

func (u *User) UserCountFromRO(filter db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         u,
		Filters:                 filter,
	}, &count).GetError()
	return
}

func (u *User) UserGet(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &u).GetError()
}

const (
	SubAccountNumber int = 10
)

func (u *User) Mask() {
	u.Phone = libs.Mask(u.Phone)
	u.RegisterIp = libs.Mask(u.RegisterIp)
}

func (u *User) MarshalToCache() (string, error) {
	b, err := json.Marshal(u)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

func (u *User) UnMarshal(us string) error {
	return json.Unmarshal([]byte(us), &u)
}
