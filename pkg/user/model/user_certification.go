package model

import (
	"encoding/json"
	"reflect"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableUserCertification = "user_certification"

type UserCertification struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	ExpiredAt *time.Time     `gorm:"column:expired_at;type:datetime" json:"expired_at"`

	UID      int    `gorm:"column:uid" json:"uid"`
	UUID     string `gorm:"column:uuid;type:varchar(255)" json:"uuid"`
	Username string `gorm:"column:username;type:varchar(255);default ''" json:"username"`
	Phone    string `gorm:"column:phone;type:varchar(255);default ''" json:"phone"`
	Name     string `gorm:"column:name;type:varchar(255);default ''" json:"name"`
	IDNumber string `gorm:"column:id_number;type:varchar(255);default ''" json:"id_number"`
	// 认证方式 个人认证/银行对公账户认证/企业证件认证
	AuthType   constant.UserCertificationType `gorm:"column:auth_type;type:varchar(255);default ''" json:"auth_type"`
	AuthJson   datatypes.JSON                 `gorm:"column:auth_info;type:json" json:"-"`
	AuthEntity UserCertificationAuthEntity    `gorm:"-" json:"auth_info"`

	Status       constant.UserCertificationStatus `gorm:"column:status;type:varchar(255);default ''" json:"status"`
	Auditor      string                           `gorm:"column:auditor;type:varchar(255);default ''" json:"auditor"`
	AuditorID    int                              `gorm:"column:auditor_id;default 0" json:"auditor_id"`
	RejectReason string                           `gorm:"column:reject_reason;type:varchar(255);default ''" json:"reject_reason"`

	OuterOrderNo string `gorm:"column:outer_order_no;type:varchar(255)" json:"outer_order_no"`
	CertifyId    string `gorm:"column:certify_id;type:varchar(255)" json:"certify_id"`
}

type UserCertificationAuthEntity struct {
	// 认证的一些细节, 全都放在这里身份证证件信息,企业信息,学生证信息等

	// 个人认证
	OuterOrderNo string `json:"outer_order_no"`
	CertifyId    string `json:"certify_id"`

	// 学生认证 - 教育邮箱
	EducationEmail string `json:"education_email,omitempty"`

	// 企业证件认证

	// 企业证件类型
	// 企业法人营业执照(enterprise_legal_person_business_license)
	CertificationType string `json:"certification_type,omitempty"`
	// 企业证件附件
	Attachment     string `json:"attachment,omitempty"`
	AttachmentUUID string `json:"attachment_uuid,omitempty"`
	// 法人/被授权人信息
	// 身份 法定代表人(legal_representative)/被授权人(authorized_person)
	Authentication string `json:"authentication,omitempty"`
	FrontOfIDCard  string `json:"front_of_id_card,omitempty"`
	BackOfIDCard   string `json:"back_of_id_card,omitempty"`
	Name           string `json:"name,omitempty"`
	IDNumber       string `json:"id_number,omitempty"`
	// 授权书 被授权人额外附加附件
	AuthorisationForm     string `json:"authorisation_form,omitempty"`
	AuthorisationFormUUID string `json:"authorisation_form_uuid,omitempty"`

	// 银行对公账户认证

}

func (u *UserCertification) TableName() string {
	return TableUserCertification
}

// Init 实现 db_helper 接口.
func (u *UserCertification) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserCertification{})
}

func (u *UserCertification) BeforeCreate(db *gorm.DB) error {
	u.AuthJson, _ = json.Marshal(u.AuthEntity)
	return nil
}

func (u *UserCertification) AfterFind(db *gorm.DB) error {
	if len(u.AuthJson) != 0 {
		return json.Unmarshal(u.AuthJson, &u.AuthEntity)
	}
	return nil
}

func (u *UserCertification) UCCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
	}).GetError()
}

func (u *UserCertification) UCGet(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &u).GetError()
}

func (u *UserCertification) UCGetFromRO(filter *db_helper.QueryFilters) error {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         u,
		Filters:                 *filter,
	}, &u).GetError()
}

func (u *UserCertification) UCGetLast(filter *db_helper.QueryFilters) error {
	return db_helper.GetLast(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &u).GetError()
}

func (u *UserCertification) UCUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um interface{}) error {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 *filter,
	}, um).GetError()
}

func (u *UserCertification) UCCount(filter *db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &count).GetError()
	return
}

func MaskString(s string) string {
	r := []rune(s)
	switch {
	case len(r) == 0:
		return ""
	case len(r) == 1:
		return "*"
	case len(r) == 2:
		return string(r[0]) + "*"
	case len(r) <= 7:
		return string(r[0]) + "*" + string(r[len(r)-1])
	default:
		prefix := r[:3]
		suffix := r[len(r)-4:]
		middle := make([]rune, len(r)-len(prefix)-len(suffix))
		for i := range middle {
			middle[i] = '*'
		}
		return string(prefix) + string(middle) + string(suffix)
	}
}

func MaskEntity(entity UserCertificationAuthEntity) UserCertificationAuthEntity {
	maskedEntity := entity
	v := reflect.ValueOf(&maskedEntity).Elem()
	typeOfEntity := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := typeOfEntity.Field(i).Name

		// Skip specific fields from masking
		if fieldName == "Attachment" || fieldName == "FrontOfIDCard" || fieldName == "BackOfIDCard" || fieldName == "Authentication" || fieldName == "CertificationType" {
			continue
		}

		if field.Kind() == reflect.String && field.CanSet() {
			field.SetString(MaskString(field.String()))
		}
	}
	return maskedEntity
}
