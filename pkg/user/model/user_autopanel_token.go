package model

import (
	"gorm.io/gorm"
	"time"
)

const TableUserAutopanelToken = "user_autopanel_token"

type UserAutopanelToken struct {
	ID    int    `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID   int    `gorm:"column:uid" json:"uid"`
	Token string `gorm:"column:token" json:"token"`

	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
}

func (u *UserAutopanelToken) TableName() string {
	return TableUserAutopanelToken
}

// Init 实现 db_helper 接口.
func (u *UserAutopanelToken) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserAutopanelToken{})
}
