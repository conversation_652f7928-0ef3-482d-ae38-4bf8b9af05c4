package model

import (
	"encoding/json"
	"time"

	"server/pkg/db_helper"

	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableAdminOperateLog = "admin_operate_log"

// 审计日志
type AdminOperateLog struct {
	ID        int       `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time `gorm:"type:datetime;column:created_at;" json:"created_at"`

	UID        int    `gorm:"column:uid;index" json:"uid"`
	EntityUUID string `gorm:"column:entity_uuid;type:varchar(255);index;default ''" json:"entity_uuid"`

	IP        string `gorm:"column:ip;type:varchar(255);default ''" json:"ip"`
	Method    string `gorm:"column:method;type:varchar(255);default ''" json:"method"`
	Url       string `gorm:"column:url;type:varchar(255);default ''" json:"url"`
	UserAgent string `gorm:"column:user_agent;type:varchar(255);default ''" json:"user_agent"`
	Referer   string `gorm:"column:referer;type:varchar(255);default ''" json:"referer"`

	PayloadJson   datatypes.JSON `gorm:"column:payload;type:json" json:"-"`
	PayloadEntity interface{}    `gorm:"-" json:"-"`
}

func (ca *AdminOperateLog) TableName() string {
	return TableAdminOperateLog
}
func (ca *AdminOperateLog) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&AdminOperateLog{})
}
func (ca *AdminOperateLog) AdminOperateLogCreate(c *gin.Context) (err error) {
	if c != nil {
		// ip
		ca.IP = c.ClientIP()
		// user-agent
		ca.UserAgent = c.GetHeader("user-agent")
		if ca.UserAgent == "" {
			ca.UserAgent = c.GetHeader("User-Agent")
		}

		// referer
		ca.Referer = c.GetHeader("referer")
	}
	// payload
	if ca.PayloadEntity != nil {
		ca.PayloadJson, _ = json.Marshal(ca.PayloadEntity)
	}
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: ca,
		InsertPayload:   ca,
	}).GetError()
}
