package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameUserFollowMachine string = "user_follow_machine"

// UserFollowMachine 用户关注 machine（订阅空闲 GPU，以后可以扩展）
type UserFollowMachine struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"-"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	UID              int    `gorm:"column:uid;index:uid" json:"uid"`
	MachineID        string `gorm:"type:varchar(255);column:machine_id;not null;" json:"machine_id"`
	NotifyIdleGPUNum uint   `gorm:"type:int unsigned;column:notify_idle_gpu_num;not null;default:0;" json:"notify_idle_gpu_num"`

	// status 不需要自定义类型，只会有0和1，不会有其它状态
	Status uint8 `gorm:"type:tinyint unsigned;column:status;not null;default:0;" json:"status"` // 0unfollow 1follow
}

func (u *UserFollowMachine) TableName() string {
	return TableNameUserFollowMachine
}

// Init 实现 db_helper 接口.
func (u *UserFollowMachine) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserFollowMachine{})
}
