package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameUserMember = "user_member"

type UserMember struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID       int            `gorm:"column:uid;index:uid" json:"uid"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	GrowthValue  int64                    `gorm:"column:growth_value" json:"growth_value"`                       // 用户计算的成长值
	MemberLevel  constant.MemberLevelName `gorm:"type:varchar(255);column:member_level" json:"member_level"`     // 用户等级
	LevelAcqMode constant.MemberGainWays  `gorm:"type:varchar(255);column:level_acq_mode" json:"level_acq_mode"` // 用户等级获取方式

	// todo: delete
	IdentifyDeadline *time.Time              `gorm:"type:datetime;column:identify_deadline" json:"identify_deadline"` // 身份截止日期
	Identify         constant.MemberIdentify `gorm:"type:varchar(255);column:identify" json:"identify"`               // 用户身份信息,student、normal、bus

	RealName           constant.MemberIdentifyStatus `gorm:"column:real_name;type:tinyint" json:"real_name"`
	Student            constant.MemberIdentifyStatus `gorm:"column:student;type:tinyint" json:"student"`
	Enterprise         constant.MemberIdentifyStatus `gorm:"column:enterprise;type:tinyint" json:"enterprise"`
	RealNameDeadline   *time.Time                    `gorm:"column:real_name_deadline;type:datetime" json:"real_name_deadline"`
	StudentDeadline    *time.Time                    `gorm:"column:student_deadline;type:datetime" json:"student_deadline"`
	EnterpriseDeadline *time.Time                    `gorm:"column:enterprise_deadline;type:datetime" json:"enterprise_deadline"`

	// 管理员指定截止日期
	MemberDeadline *time.Time `gorm:"type:datetime;column:member_deadline" json:"member_deadline"` // 会员截止日期(针对于管理员赋予的)
	EducationEmail string     `gorm:"type:varchar(255);column:education_email" json:"education_email"`
}

func (u *UserMember) TableName() string {
	return TableNameUserMember
}

// Init 实现 db_helper 接口.
func (u *UserMember) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserMember{})
}

func (u *UserMember) IsRealName() bool {
	return u.RealName > 0
}

func (u *UserMember) IsEnterprise() bool {
	return u.Enterprise > 0
}

func (u *UserMember) IsStudent() bool {
	return u.Student > 0
}

func (u *UserMember) UserMemberCreate() (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: u,
		InsertPayload:   u,
	}).GetError()
}

func (u *UserMember) UserMemberUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um interface{}) (err error) {
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 *filter,
	}, um).GetError()
}
func (u *UserMember) UserMemberGet(filter *db_helper.QueryFilters) (err error) {
	return db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &u).GetError()
}

func (u *UserMember) UserMemberCount(filter *db_helper.QueryFilters) (count int64, err error) {

	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
	}, &count).GetError()
	return
}

func (u *UserMember) UserMemberGetAll(filter *db_helper.QueryFilters) (list []UserMember, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         *filter,
		NoLimit:         true,
	}, &list).GetError()
	return
}
