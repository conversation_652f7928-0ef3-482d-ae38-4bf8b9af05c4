package model

import (
	"gorm.io/gorm"
	"server/pkg/db_helper"
	"time"
)

const TableNameUserPrizeWhitelist string = "user_prize_whitelist"

type UserPrizeWhitelist struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID       int            `gorm:"column:uid" json:"uid"`
	Phone     string         `gorm:"column:phone" json:"phone"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
}

func (u *UserPrizeWhitelist) TableName() string {
	return TableNameUserPrizeWhitelist
}

// Init 实现 db_helper 接口.
func (u *UserPrizeWhitelist) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserPrizeWhitelist{})
}

func (u *UserPrizeWhitelist) UserWhitelistCount(filter db_helper.QueryFilters) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: u,
		Filters:         filter,
	}, &count).GetError()
	return
}

type AddUserPrizeWhitelistRequest struct {
	Phone string `form:"phone" json:"phone"` // 手机号
}

func (u *UserPrizeWhitelist) UserPrizeWhitelistInsertOrUpdateOne(tx *gorm.DB) error {
	return db_helper.InsertOrUpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		InsertPayload:           u,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid": u.UID,
			},
		},
	}, u).GetError()
}
