package model

import (
	"time"

	"gorm.io/gorm"
)

const TableUserSSHPublicKey = "user_ssh_public_key"

type UserSSHPublicKey struct {
	ID        int    `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	UID       int    `gorm:"column:uid" json:"uid"`
	PublicKey string `gorm:"column:public_key" json:"public_key"`
	Note      string `gorm:"type:varchar(255);column:note" json:"note"`

	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
}

func (u *UserSSHPublicKey) TableName() string {
	return TableUserSSHPublicKey
}

// Init 实现 db_helper 接口.
func (u *UserSSHPublicKey) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserSSHPublicKey{})
}
