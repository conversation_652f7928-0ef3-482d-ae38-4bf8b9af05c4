package model

import (
	"database/sql"
	"encoding/json"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const TableNameUserSetting string = "user_setting"

// UserSetting 用户设置：有一些是用户设置的，也有一些字段是用来记录用户级别全局的状态用来实现一些业务逻辑
type UserSetting struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"-"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	UID int `gorm:"column:uid;index:uid" json:"uid"`

	/******************************* 下面用户设置的 *******************************/

	// 通知实例预警状态，例如 0关闭，1开启
	NotifyInstanceWarningStatus constant.NotifyInstanceWarningStatus `gorm:"type:tinyint unsigned;column:notify_instance_warning_status;not null;default:0;" json:"notify_instance_warning_status"`

	// 通知余额预警状态，例如 0关闭，1开启
	NotifyBalanceWarningStatus constant.NotifyBalanceWarningStatus `gorm:"type:tinyint unsigned;column:notify_balance_warning_status;not null;default:0;" json:"notify_balance_warning_status"`

	// 通知余额预警触发金额（元 整数 用户的余额记录的是 100.10*100 = 10010，所以比较的时候要注意这个字段需要*100）
	NotifyBalanceWarningMoney int64 `gorm:"type:bigint unsigned;column:notify_balance_warning_money;not null;default:0;" json:"notify_balance_warning_money"`

	/******************************* 下面不是用户设置的 *******************************/

	// 上一次通知余额预警时间
	LastNotifyBalanceWarningTime  sql.NullTime `gorm:"type:datetime;column:last_notify_balance_warning_time;" json:"-"`
	LastNotifyBalanceNegativeTime sql.NullTime `gorm:"type:datetime;column:last_notify_balance_negative_time;" json:"-"`

	// 通知余额预警次数
	NotifyBalanceWarningCount uint8 `gorm:"type:tinyint unsigned;column:notify_balance_warning_count;not null;default:0;" json:"-"`

	AdditionalInfo   datatypes.JSON `gorm:"type:json;column:additional_info;" json:"-"`
	AdditionalEntity AdditionalInfo `gorm:"-" json:"-"`
}

func (u *UserSetting) TableName() string {
	return TableNameUserSetting
}

// Init 实现 db_helper 接口.
func (u *UserSetting) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&UserSetting{})
}

func (u *UserSetting) BeforeCreate(tx *gorm.DB) (err error) {
	u.AdditionalInfo, _ = json.Marshal(u.AdditionalEntity)

	return nil
}

func (u *UserSetting) AfterFind(tx *gorm.DB) (err error) {
	if len(u.AdditionalInfo) != 0 {
		_ = json.Unmarshal(u.AdditionalInfo, &u.AdditionalEntity)
	}
	if u.AdditionalEntity.SubUserBalanceAlert == nil {
		u.AdditionalEntity.SubUserBalanceAlert = map[string]bool{}
	}
	return nil
}

func (u *UserSetting) UserSettingUpdate(tx *gorm.DB, f *db_helper.QueryFilters, um map[string]interface{}) (int64, error) {
	count, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         u,
		Filters:                 *f,
	}, um)
	return count, errDB.GetError()
}

type AdditionalInfo struct {
	SubUserBalanceAlert map[string]bool `json:"subuser_balance_alert"`
}
