package service

import (
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	redis "server/plugin/redis_plugin"
	"strconv"
)

// [lock] update user member
func (svc *UserService) setLockForUpdateUserMember(uid int) (lock *redis.RedisMutex, err error) {
	lock = svc.mutex.NewRedisMutex(redis.MutexUpdateUserMember, "update_"+strconv.Itoa(uid))
	var locked bool
	locked, err = lock.LockWithNoRetry(constant.LockTimeout)
	if err != nil || !locked {
		svc.l.WithError(err).WithField("is locked", locked).WithField("uid", uid).Warn("get redis lock failed.")
		err = biz.ErrServerBusy
		return
	}
	return
}

// [lock] update sub user quota
func (svc *UserService) setLockForUpdateSubUserQuota(subName string) (lock *redis.RedisMutex, err error) {
	lock = svc.mutex.NewRedisMutex(redis.MutexUpdateSubUserQuota, "update_"+subName)
	var locked bool
	locked, err = lock.LockWithNoRetry(constant.LockTimeout)
	if err != nil || !locked {
		svc.l.WithError(err).WithField("is locked", locked).WithField("subName", subName).Warn("get redis lock failed.")
		err = biz.ErrServerBusy
		return
	}
	return
}
