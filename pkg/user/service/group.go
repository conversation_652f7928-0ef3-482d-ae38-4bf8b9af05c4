package service

import (
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/pkg/module_definition"
	"server/pkg/user/model"

	"gorm.io/gorm"
)

const (
	GroupModuleName = "group"
)

type GroupService struct {
	l         *logger.Logger
	userGroup module_definition.UserGroupInterface
}

func NewGroupServiceProvider(userGroup module_definition.UserGroupInterface) *GroupService {
	return &GroupService{
		userGroup: userGroup,
		l:         logger.NewLogger(GroupModuleName),
	}
}

// CreateUserGroup 创建用户组
func (svc *GroupService) CreateGroup(params *model.CreateGroupRequest) (err error) {
	if params == nil {
		svc.l.WithField("params", params).Info("Create group invalid params.")
		err = biz.ErrInvalidRequestParams
		return
	}
	group := &model.Group{
		Name:        params.GroupName,
		MaxCapacity: constant.MaxUserCapacity,
		Description: params.Description,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Group{},
		InsertPayload:   group,
	}).GetError()
	if err != nil {
		svc.l.WithField("params", params).WithError(err).Info("Create group failed.")
		err = biz.ErrCreateUserGroupFailed
	}
	return
}

// UpdateGroup 编辑用户组
func (svc *GroupService) UpdateGroup(params *model.UpdateGroupRequest) (err error) {
	if params == nil {
		err = biz.ErrInvalidRequestParams
		svc.l.WithField("params", params).Info("Update group invalid params.")
		return
	}
	data := map[string]interface{}{
		"name":        params.GroupName,
		"description": params.Description,
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.Group{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.GroupId}},
	}, data).GetError()
	if err != nil {
		err = biz.ErrUpdateUserGroupFailed
		svc.l.WithField("params", params).WithError(err).Error("Update group failed.")
		return
	}
	return
}

// GetGroup 获取组
func (svc *GroupService) GetGroupFromRO(groupID int) (group *model.Group, err error) {
	if groupID < 1 {
		err = biz.ErrInvalidRequestParams
		svc.l.WithField("group_id", groupID).Info("get group invalid params.")
		return
	}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         &model.Group{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": groupID}},
	}, &group).GetError()
	if err != nil {
		err = biz.ErrUpdateUserGroupFailed
		svc.l.WithField("group_id", groupID).WithError(err).Error("get group failed.")
		return
	}
	return
}

// DeleteUserGroup 删除用户组 盱眙删除user_group和machine_user_group
func (svc *GroupService) DeleteGroup(groupId int) (err error) {
	if groupId == 0 {
		err = biz.ErrInvalidRequestParams
		return
	}

	// 在一个事务中实现删除两个表中的记录
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (txError db_helper.ModelError) {
		txError = db_helper.Delete(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.Group{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": groupId,
				},
			},
		}, &model.Group{})
		if txError.IsNotNil() {
			svc.l.WithField("id", groupId).WithError(txError.GetError()).Error("delete group failed")
			return
		}

		txError = db_helper.Delete(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.UserGroup{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"group_id": groupId,
				},
			},
		}, &model.UserGroup{})
		if txError.IsNotNil() {
			svc.l.WithField("groupId", groupId).WithError(txError.GetError()).Error("Delete user group relation failed.")
			return
		}

		txError = db_helper.Delete(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &machineModel.MachineUserGroup{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"group_id": groupId,
				},
			},
		}, &model.UserGroup{})
		if txError.IsNotNil() {
			svc.l.WithField("groupId", groupId).WithError(txError.GetError()).Error("Delete machine user_group relation failed.")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.l.WithError(err).Error("Delete group in db failed.")
		err = biz.ErrDatabaseError
		return
	}
	return
}

// GroupList 获取用户组列表
func (svc *GroupService) GroupList(db *gorm.DB, name string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.Group, err error) {
	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableGroup).Where("deleted_at is null")
	if name != "" {
		name := "%" + name + "%"
		db = db.Where("name like ?", name)
	}
	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Warn("Count failed.")
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	// 按照创建时间进行倒序排序
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.l.WithError(err).Warn("Get group list failed.")
		return
	}
	paged.List = &list
	return
}

// GroupList 获取所有用户组
func (svc *GroupService) GetAll(db *gorm.DB) (groups []*model.Group, err error) {
	groups = make([]*model.Group, 0)

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	err = db.Table(model.TableGroup).Where("deleted_at is null").Find(&groups).Error
	return
}

// UserGroupExist 校验用户组是否存在
func (svc *GroupService) CheckGroupExistFromRO(name string, groupID int) (exist bool, err error) {
	group := &model.Group{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         &model.Group{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"name": name}}}, group).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		svc.l.WithField("err", err).Error("get group failed")
		err = biz.ErrDatabaseError
		return
	}
	if groupID > 0 {
		if group.ID == groupID {
			return false, nil
		}
	}
	return true, nil
}

// 检查组的最大用户容量是否满足修改
func (svc *GroupService) CheckGroupMaxUserCapacityFromRO(groupID int, maxCapacity int) (ok bool, err error) {
	group, err := svc.getGroupFromRO(groupID)
	if err != nil {
		svc.l.WithField("err", err).WithField("group_id", groupID).Error("get group failed")
		return
	}
	if maxCapacity >= group.MaxCapacity {
		return true, nil
	}
	count, err := svc.userGroup.CountUserInGroupFromRO(groupID)
	if err != nil {
		svc.l.WithField("err", err).WithField("group_id", groupID).Error("get user_group failed")
		return
	}
	ok = maxCapacity >= int(count)
	return ok, nil
}

// 检查组添加用户是否达到最大容量
func (svc *GroupService) CheckGroupIsMaxCapacityFromRO(groupID int, userNum int) (ok bool, err error) {
	group, err := svc.getGroupFromRO(groupID)
	if err != nil {
		svc.l.WithField("err", err).WithField("group_id", groupID).Error("get group failed")
		return
	}
	count, err := svc.userGroup.CountUserInGroupFromRO(groupID)
	if err != nil {
		svc.l.WithField("err", err).WithField("group_id", groupID).Error("get user_group failed")
		return
	}
	ok = (userNum + int(count)) <= group.MaxCapacity // 新添加的人数+已经存入的人数<=组的最大容量
	return ok, nil
}

func (svc *GroupService) getGroupFromRO(groupID int) (group *model.Group, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		DBTransactionConnection: db_helper.GlobalDBConnForRead(),
		ModelDefinition:         &model.Group{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": groupID}}},
		&group).GetError()
	if err != nil {
		svc.l.WithField("err", err).WithField("group_id", groupID).Error("get group failed")
		err = biz.ErrDatabaseError
		return
	}
	return
}

// GroupList 获取所有用户组
func (svc *GroupService) GetGroupsByID(groupIDs []int) (groups []*model.Group, err error) {
	groups = make([]*model.Group, 0)
	err = db_helper.GlobalDBConn().Table(model.TableGroup).Where("deleted_at is null").Where("id in (?)", groupIDs).Find(&groups).Error
	if err != nil {
		svc.l.WithField("err", err).WithField("group_ids", groupIDs).Error("get group failed")
		err = biz.ErrDatabaseError
	}
	return
}
