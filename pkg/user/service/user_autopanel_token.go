package service

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	biz "server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/user/model"
)

// CreateAutopanelToken 创建token
func (svc *UserService) CreateAutopanelToken(uid int, token string) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAutopanelToken{},
		InsertPayload: &model.UserAutopanelToken{
			UID:   uid,
			Token: token,
		},
	}).GetError()
	if err != nil {
		svc.l.WithError(err).Error("create autopanel token failed")
		err = biz.ErrDatabaseError
	}
	return
}

// GetAutopanelToken 获取token
func (svc *UserService) GetAutopanelToken(uid int) (tokenStr string, err error) {
	var token *model.UserAutopanelToken
	token, err = svc.GetAutopanelTokenFrontend(uid)
	if err != nil {
		return
	}

	if token != nil {
		tokenStr = token.Token
	}
	return
}

func (svc *UserService) GetAutopanelTokenFrontend(uid int) (token *model.UserAutopanelToken, err error) {
	token = &model.UserAutopanelToken{}
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAutopanelToken{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &token).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		svc.l.WithError(err).WithField("uid", uid).Error("get user autopanel token failed")
		err = biz.ErrDatabaseError
	}
	return
}

// DeleteAutopanelToken 删除token
func (svc *UserService) DeleteAutopanelToken(uid int) (err error) {
	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.UserAutopanelToken{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, nil).GetError()
	if err != nil {
		svc.l.WithError(err).Error("delete user autopanel token failed")
		err = biz.ErrDatabaseError
	}
	return
}
