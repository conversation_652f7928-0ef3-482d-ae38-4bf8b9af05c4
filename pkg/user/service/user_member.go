package service

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/user/model"
	"time"
)

func (svc *UserService) GetUserMemberInfoByUid(uid int) (member *model.UserMember, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.UserMember{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &member).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			var memberExpiredTime *time.Time
			timeAdd := libs.MemberShipDeadline()
			memberExpiredTime = &timeAdd
			member = &model.UserMember{
				UID:            uid,
				MemberLevel:    constant.MemberUser,
				LevelAcqMode:   constant.VIPAdminAppointed,
				MemberDeadline: memberExpiredTime,
			}

			err = member.UserMemberCreate()
			if err != nil {
				svc.l.WithField("member", member).ErrorE(err, "create user member failed")
				return
			}
		}
		svc.l.WithField("uid", uid).WarnE(err, "get user member info by uid in table user member failed.")
		err = biz.ErrGetUserMemberInfoFailed
		return
	}

	// 防止上线到刷库期间用户调用，旧数据导致问题
	if member.MemberLevel != constant.NormalUser {
		member.MemberLevel = constant.MemberUser
	}

	return
}

func (svc *UserService) UserMemberGetAll(uid ...int) (members []model.UserMember, err error) {
	filter := &db_helper.QueryFilters{}
	if len(uid) > 0 {
		filter.InFilters = []db_helper.In{{Key: "uid", InSet: uid}}
	}

	um := &model.UserMember{}
	members, err = um.UserMemberGetAll(filter)
	if err != nil {
		svc.l.WithField("machineIDs", uid).ErrorE(err, "get all userMember failed")
		return
	}
	return
}

// GetUserGrowthValue 历史累积获得成长值总额
func (svc *UserService) GetUserGrowthValue(userid int) (count int, err error) {
	return svc.getUserGrowthValue(db_helper.GlobalDBConn(), userid)
}

func (svc *UserService) GetUserGrowthValueFromRO(userid int) (count int, err error) {
	return svc.getUserGrowthValue(db_helper.GlobalDBConnForRead(), userid)
}

func (svc *UserService) getUserGrowthValue(db *gorm.DB, userid int) (count int, err error) {
	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	numberList, countErr := db_helper.CountColumn(
		db_helper.QueryDefinition{
			DBTransactionConnection: db,
			ModelDefinition:         &model.UserGrowthValue{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"user_id": userid}}},
		"growth_value",
	)
	if countErr.GetError() != nil {
		err = biz.ErrCountColumnFailed
		svc.l.WithField("userId", userid).WithError(err).Error("Count user growth value by user id failed.")
		return
	}

	for i := 0; i < len(numberList); i++ {
		count += numberList[i]
	}
	return
}

func (svc *UserService) GetMemberList(db *gorm.DB, filter model.UserMemberListFilter, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []model.UserMemberListReply, err error) {
	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Model(&model.UserMember{}).Where("deleted_at is null").Debug()

	if filter.UID != 0 {
		db = db.Where("uid = ?", filter.UID)
	}

	if filter.Level != "" {
		if filter.Level == string(constant.NormalUser) {
			db = db.Where("member_level = ?", constant.NormalUser)
		} else {
			db = db.Where("member_level != ?", constant.NormalUser)
		}
	}

	switch filter.Identify {
	case constant.UserIdentifyNormal:
		db = db.Where("student <= 0 and real_name <= 0 and enterprise <= 0")
	case constant.UserIdentifyRealName:
		db = db.Where("real_name > 0")
	case constant.UserIdentifyStudent:
		db = db.Where("student > 0")
	case constant.UserIdentifyEnterprise:
		db = db.Where("enterprise > 0")

	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Warn("Count failed.")
		return
	}
	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	// 按照创建时间进行排序
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.l.WithError(err).Warn("Get user member list failed.")
		return
	}

	// fill more...
	uidList := libs.NewIntSet()
	for i := range list {
		uidList.Append(list[i].UID)
	}
	phoneMap, err := svc.CacheIDToPhoneMGet(uidList.ToSlice())
	if err != nil {
		svc.l.WithField("ul", uidList.ToSlice()).ErrorE(err, "cache get failed")
		err = nil
	}
	nameMap, err := svc.CacheIDToNameMGet(uidList.ToSlice())
	if err != nil {
		svc.l.WithField("ul", uidList.ToSlice()).ErrorE(err, "cache get failed")
		err = nil
	}

	for i := range list {
		list[i].Phone = phoneMap[list[i].UID]
		list[i].UserName = nameMap[list[i].UID]
	}
	paged.List = list
	return
}

// UpdateUserMemberLevel 管理员设置用户会员等级
func (svc *UserService) UpdateUserMemberLevel(params model.UpdateUserLevelRequest) (err error) {
	var (
		adminSetDeadlineParse *time.Time
		userMember            *model.UserMember
	)

	// find user member record
	userMember, err = svc.GetUserMemberInfoByUid(params.UID)
	if err != nil {
		return
	}

	if params.AdminSetDeadline != "" {
		adminSetDeadlineParse, err = libs.AddTimeInfoToTheTransTime(params.AdminSetDeadline)
		if err != nil {
			return
		}
	}

	um := map[string]interface{}{}
	if adminSetDeadlineParse == nil {
		um = map[string]interface{}{
			"member_level":    constant.NormalUser,
			"level_acq_mode":  constant.VIPUserAcquire,
			"member_deadline": nil,
		}
	} else {
		um = map[string]interface{}{
			"member_level":    constant.MemberUser,
			"level_acq_mode":  constant.VIPAdminAppointed,
			"member_deadline": adminSetDeadlineParse,
		}
	}

	err = userMember.UserMemberUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": params.UID}}, um)
	if err != nil {
		svc.l.WithField("params", params).WarnE(err, "update in table user member failed.")
		return
	}
	return

}

// GetUserGrowthValueList 用户获取自己的成长值记录
func (svc *UserService) GetUserGrowthValueList(id int, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.UserGrowthValue, err error) {
	db := db_helper.GlobalDBConn().Model(&model.UserGrowthValue{}).Where("user_id = ?", id)
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.l.WithError(err).Warn("Count failed.")
		return
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 0)

	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).
		Error
	if err != nil {
		svc.l.WithError(err).Warn("Get user growth value list failed.")
		return
	}
	paged.List = list
	return
}

// AddUserGrowthValueCmd add user growth value summary
func (svc *UserService) AddUserGrowthValueCmd(userId int, assets int64, updateWays constant.GrowthValueOptTypes) (err error) {
	userMember, err := svc.GetUserMemberInfoByUid(userId)
	if err != nil {
		return
	}
	return svc.userCmdAddGrowthValue(userMember, assets, updateWays)
}

func (svc *UserService) CheckEducationEmailExist(email string, uid int) (exist bool, err error) {
	um := &model.UserMember{}
	count, err := um.UserMemberCount(&db_helper.QueryFilters{
		NotEqualFilters: map[string]interface{}{
			"uid": uid,
		},
		LowerEqualFilters: map[string]interface{}{
			"education_email": email,
		},
		CompareFilters: []db_helper.Compare{{Key: "student_deadline", Sign: db_helper.BiggerThan, CompareValue: time.Now()}},
	})
	if err != nil {
		svc.l.WithField("education_email", email).ErrorE(err, "Check education email exist failed.")
		return
	}

	return count != 0, nil
}

// useless--------------------------------------
// useless--------------------------------------

// InsertIntoTableGrowthValue 测试用例专用
func (svc *UserService) InsertIntoTableGrowthValue(growth *model.UserGrowthValue) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserGrowthValue{},
		InsertPayload:   growth,
	}).GetError()
	if err != nil {
		svc.l.WithField("growth", growth).WithError(err).Info("Insert a record into table user growth value failed.")
		return
	}
	return
}
