package service

import (
	biz "server/pkg/businesserror"
	"server/pkg/db_helper"
	"server/pkg/user/model"
	"strings"
)

// CreatePubKey 创建用户公钥
func (svc *UserService) CreatePubKey(uid int, pubKey, note string) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		InsertPayload: &model.UserSSHPublicKey{
			UID:       uid,
			PublicKey: pubKey,
			Note:      note,
		},
	}).GetError()
	if err != nil {
		svc.l.WithError(err).Error("create pub key failed")
		err = biz.ErrDatabaseError
	}
	return
}

// UpdatePubKey 更新用户公钥
func (svc *UserService) UpdatePubKey(id int, pubKey, note string) (err error) {
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": id}},
	}, map[string]interface{}{
		"public_key": pubKey,
		"note":       note,
	}).GetError()
	if err != nil {
		svc.l.WithError(err).Error("update pub key failed")
		err = biz.ErrDatabaseError
	}
	return
}

// GetPubKey 获取公钥
func (svc *UserService) GetPubKey(id int) (pubKey model.UserSSHPublicKey, err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": id}},
	}, &pubKey).GetError()
	if err != nil {
		svc.l.WithError(err).WithField("id", id).Error("get user ssh pub key by id failed")
		err = biz.ErrDatabaseError
	}
	return
}

// GetPubKeyList 获取公钥刘表
func (svc *UserService) GetPubKeyList(uid int) (list []model.UserSSHPublicKey, err error) {
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &list).GetError()
	if err != nil {
		svc.l.WithError(err).WithField("uid", uid).Error("get user ssh pub key by uid failed")
		err = biz.ErrDatabaseError
	}
	return
}

// DeletePubKey 删除公钥
func (svc *UserService) DeletePubKey(uid, id int) (err error) {
	var (
		pubKey model.UserSSHPublicKey
	)
	pubKey, err = svc.GetPubKey(id)
	if err != nil {
		return
	}

	if pubKey.UID != uid {
		err = biz.ErrAuthorizeFailed
		return
	}

	err = db_helper.Delete(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": id}},
	}, nil).GetError()
	if err != nil {
		svc.l.WithError(err).Error("delete user ssh pub key by id failed")
		err = biz.ErrDatabaseError
	}
	return
}

// CountPubKey 获取用户已存在公钥数量
func (svc *UserService) CountPubKey(uid int) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.UserSSHPublicKey{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, &count).GetError()
	if err != nil {
		svc.l.WithError(err).Error("count user pub key bu uid failed")
		err = biz.ErrDatabaseError
	}
	return
}

// GeneratePubKeyForContainer 生成 authorized_keys 内容字符串
func (svc *UserService) GeneratePubKeyForContainer(uid int) (key string, err error) {
	var (
		keyList []model.UserSSHPublicKey
		strList = []string{"# 此文件由系统创建，请勿修改"}
	)

	keyList, err = svc.GetPubKeyList(uid)
	if err != nil {
		return
	}

	for _, v := range keyList {
		strList = append(strList, v.PublicKey)
	}

	key = strings.Join(strList, "\n")
	key = key + "\n"
	return
}
