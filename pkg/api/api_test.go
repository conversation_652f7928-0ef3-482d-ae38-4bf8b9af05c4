package server_api

import (
	"encoding/json"
	agentConstant "server/pkg-agent/agent_constant"
	"testing"
	"time"
)

func testNew() *Api {
	//url := agentConstant.ParseUrl("http://127.0.0.1:9002")
	url := agentConstant.ParseUrl("https://test.autodl.com:33443")
	return NewApi(url)
}

func TestGetSubUserSPAC(t *testing.T) {
	api := testNew()
	res, _, err := api.GetSubUserSPAC(GetSubUserSPACReq{
		UID:     1,
		SubName: "zhoutao@ad5e00916d11",
	})

	if err != nil {
		t.Error(err)
		return
	}

	t.Log(res)
}

func TestGetBody(t *testing.T) {
	api := testNew()
	_, _, err := api.get("/test/123", GetSubUserSPACReq{
		UID:     1,
		SubName: "zhoutao@ad5e00916d11",
	})
	if err != nil {
		return
	}

}
func TestGet1(t *testing.T) {
	api := testNew()
	_, _, err := api.get("/test/123", nil)
	if err != nil {
		return
	}
}

type testStruct struct {
	Name string    `json:"name"`
	Age  int       `json:"age"`
	Born time.Time `json:"born"`
}

func TestToMap(t *testing.T) {
	s := testStruct{
		Name: "hhhh",
		Age:  12,
		Born: time.Now(),
	}
	b, _ := json.Marshal(structToStringMap(s))
	t.Log(string(b))

}
