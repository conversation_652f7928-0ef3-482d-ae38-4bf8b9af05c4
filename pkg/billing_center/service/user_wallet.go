package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	ddsModel "server/pkg/data_disk_stock/model"
	"server/pkg/db_helper"
	deploymentModel "server/pkg/deployment/model"
	instanceModel "server/pkg/instance/model"
	"server/pkg/international"
	"server/pkg/libs"
	"server/pkg/logger"
	message_model "server/pkg/message/model"
	"server/pkg/notify"
	notifyModel "server/pkg/notify/model"
	userModel "server/pkg/user/model"
	"server/plugin/payment"
	"server/plugin/queue_interface"
	"server/plugin/redis_plugin"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/smartwalle/alipay/v3"

	adlredis "server/plugin/redis_plugin"

	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"gorm.io/gorm"
)

// ---------------- API --------------------

func (svc *BCService) CreateWallet(uid int) (err error) {
	return svc.createWallet(uid)
}

func (svc *BCService) GetBalance(uid int) (balance int64, err error) {
	return svc.getBalance(uid)
}
func (svc *BCService) GetBalances(uidList []int) (balance map[int]int64, err error) {
	return svc.getBalances(uidList)
}

func (svc *BCService) GetWallet(uid int, productType constant.ProductType, chargeType constant.ChargeType, machineId string) (wallet *model.UserWallet, err error) {
	return svc.getWalletWithVoucher(uid, productType, chargeType, machineId)
}

func (svc *BCService) GetWalletAndAllVoucher(uid int) (wallet *model.UserWallet, err error) {
	return svc.getWalletWithAllVoucher(uid)
}

// RechargeCreate 创建充值，共用
func (svc *BCService) RechargeCreate(uid int, userPhone string, asset int64, pathway constant.RechargePathway) (codeUrl, billUUID string, err error) {
	return svc.rechargeCreate(uid, userPhone, asset, pathway)
}

func (svc *BCService) AppletRechargeCreate(uid int, userPhone, openId string, asset int64) (resp *payment.PrepayWithRequestPaymentResponse, billUUID string, err error) {
	return svc.appletRechargeCreate(uid, userPhone, openId, asset)
}

// RechargeCheckSuccess 本地检查是否充值成功，共用
func (svc *BCService) RechargeCheckSuccess(billUUID string) (map[string]interface{}, error) {
	return svc.rechargeCheckSuccess(billUUID)
}

func (svc *BCService) FirstRechargeUpdateSendSms(uid int, assets int64) (err error) {
	return svc.firstRechargeUpdateSendSms(uid, assets)
}

// RechargeWxClose 微信 关闭订单
func (svc *BCService) RechargeWxClose(billUUID string) error {
	return svc.rechargeWxClose(billUUID)
}

// RechargeWxQuery 微信 查询
func (svc *BCService) RechargeWxQuery(billUUID string) (string, error) {
	return svc.rechargeWxQuery(billUUID)
}

func (svc *BCService) RechargeWxAppletQuery(billUUID string) (string, error) {
	return svc.appletRechargeWxQuery(billUUID)
}

// RechargeWxNotify 微信 通知回调
func (svc *BCService) RechargeWxNotify(request *http.Request) (err error) {
	return svc.rechargeWxNotify(request)
}

// RechargeAliQuery 支付宝 查询
func (svc *BCService) RechargeAliQuery(billUUID string) (status string, err error) {
	return svc.rechargeAliQuery(billUUID)
}

// RechargeAliNotify 支付宝 通知回调
func (svc *BCService) RechargeAliNotify(request *http.Request) (err error) {
	return svc.rechargeAliNotify(request)
}

// RechargeAliUpdateQrCode 支付宝 定时更新二维码
func (svc *BCService) RechargeAliUpdateQrCode(billUUID string) (qrUrl string, err error) {
	return svc.rechargeAliUpdateQrCode(billUUID)
}

func (svc *BCService) RechargeWechatAppletQuery(billUUID string) (status string, err error) {
	return svc.rechargeAliQuery(billUUID)
}

// GetWalletForCreateOrder 获取钱包，创建订单时，根据订单计费方式不同返回不同的代金券余额
func (svc *BCService) GetWalletForCreateOrder(uid int, productType constant.ProductType, chargeType constant.ChargeType, machineId string, regionSign []constant.RegionSignType, gpuName string) (wallet *model.UserWallet, err error) {
	return svc.getWalletForCreateOrder(uid, productType, chargeType, machineId, regionSign, gpuName)
}

// ------------------ private ------------------

// [钱包] 创建钱包,在创建用户的时候调用
func (svc *BCService) createWallet(uid int) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserWallet{},
		InsertPayload:   &model.UserWallet{UID: uid},
	}).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("uid", uid).Error("create userWallet service failed.")
		err = businesserror.ErrDatabaseError
	}
	return
}

// [钱包] 更新钱包
func (svc *BCService) updateWallet(tx *gorm.DB, bill *model.Bill) (err error) {
	if bill == nil {
		return businesserror.ErrInternalError
	}

	wallet := &model.UserWallet{}
	wallet, err = svc.getWallet(tx, bill.UID)
	if err != nil {
		svc.log.WithError(err).Warn("get user wallet failed")
		return
	}

	if bill.Asset == 0 {
		bill.Balance = wallet.Assets + wallet.BlockedAsset
		return
	}
	// 更新subUserWallet
	err = svc.subUserWalletUpdate(tx, bill)
	if err != nil {
		return
	}

	if bill.PayByBalance == 0 {
		bill.Balance = wallet.Assets + wallet.BlockedAsset
		return
	}

	if bill.SubType == constant.BillSubTypeCreateContainer ||
		bill.SubType == constant.BillSubTypeRenewalContainer {
		if wallet.Assets < bill.PayByBalance {
			return businesserror.ErrWalletInsufficientBalance
		}
	}

	// 做金额计算
	switch bill.Type {
	case constant.BillTypeRecharge:
		wallet.Assets += bill.PayByBalance

		if bill.SubType != constant.BillSubTypeRechargeCommunityExchange {
			wallet.TotalRechargeAsset += bill.PayByBalance
		}

	case constant.BillTypeRefund:
		wallet.Assets += bill.PayByBalance
		wallet.Accumulate -= bill.PayByBalance
	case constant.BillTypeCharge:
		wallet.Assets -= bill.PayByBalance
		wallet.Accumulate += bill.PayByBalance
	case constant.BillTypeWithdraw:
		wallet.Accumulate -= bill.PayByBalance

		// 管理员体提现，直接扣除余额，没有其他操作
		if bill.SubType == constant.BillSubTypeWithdrawAdmin {
			wallet.Assets -= bill.PayByBalance
		}

		// 支付宝，微信提现，此类型用于触发冻结余额操作
		if bill.SubType == constant.BillSubTypeWithdrawRefundProcessing {
			wallet.Assets -= bill.PayByBalance
			wallet.BlockedAsset += bill.PayByBalance
		}

		// 支付宝微信提现失败，解冻余额
		if bill.SubType == constant.BillSubTypeWithdrawRefundFailed {
			wallet.Assets += bill.PayByBalance
			wallet.BlockedAsset -= bill.PayByBalance
		}

		// 支付宝微信提现成功，扣除冻结的余额
		if bill.SubType == constant.BillSubTypeWithdrawRefund {
			wallet.BlockedAsset -= bill.PayByBalance
		}
	default:
		svc.log.WithField("billType", bill.Type).Error("update wallet billType error.")
		err = businesserror.ErrInternalError
		return
	}

	bill.Balance = wallet.Assets + wallet.BlockedAsset

	// 更新余额
	affectRows, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.UserWallet{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": wallet.ID, "assets_cc": wallet.AssetsCC}},
	}, map[string]interface{}{
		"assets":               wallet.Assets,
		"assets_cc":            wallet.AssetsCC + 1,
		"accumulate":           wallet.Accumulate,
		"updated_at":           time.Now(),
		"total_recharge_asset": wallet.TotalRechargeAsset,
		"blocked_asset":        wallet.BlockedAsset,
	})
	if errDB.IsNotNil() {
		svc.log.WithError(errDB.GetError()).Error("update wallet failed")
		return businesserror.ErrDatabaseError
	}

	if affectRows == 0 {
		svc.log.WithField("uid", bill.UID).Warn(international.MWallet00002.Message)
		return businesserror.ErrServerBusy
	}

	return
}

func (svc *BCService) sendMsgOfBalanceNegative(bill *model.Bill) (err error) {
	user, err := svc.user.CacheGetUser(bill.UID)
	if err != nil {
		svc.log.WithField("uid", bill.UID).ErrorE(err, "cache get user by uid failed")
		return
	}

	if user.Status == userModel.Disable {
		svc.log.Info("user disabled, so ignore notify")
		return
	}

	us, err := svc.user.GetSetting(bill.UID)
	if err != nil {
		svc.log.WithField("uid", bill.UID).ErrorE(err, "get user setting by uid failed")
		return
	}

	uw, err := svc.getWallet(nil, bill.UID)
	if err != nil {
		svc.log.WithField("uid", bill.UID).Info("get user wallet failed")
		return
	}

	if uw.Assets < 0 {
		if us.LastNotifyBalanceNegativeTime.Valid {
			return
		}

		userInfoList := []userModel.UserBalanceNegativeInfo{}
		userInfoList = append(userInfoList, userModel.UserBalanceNegativeInfo{
			UID:                           user.ID,
			PhoneArea:                     user.PhoneArea,
			Phone:                         user.Phone,
			LastNotifyBalanceNegativeTime: us.LastNotifyBalanceNegativeTime,
		})

		subUserList := []userModel.UserBalanceNegativeInfo{}
		err = db_helper.GlobalDBConn().Model(userModel.SubUser{}).
			Select("sub_name, phone_area, phone").
			Where("deleted_at is null").
			Where("uid = ?", user.ID).
			Where("phone_area is not null").
			Where("phone is not null").
			Where("json_extract(roles, '$.billing_center') = ?", constant.SubUserAllData).
			Find(&subUserList).Error
		if err != nil {
			svc.log.WithField("uid", bill.UID).ErrorE(err, "get sub user list failed")
			return err
		}

		for i, _ := range subUserList {
			subUserList[i].LastNotifyBalanceNegativeTime = us.LastNotifyBalanceNegativeTime
		}
		userInfoList = append(userInfoList, subUserList...)

		for _, v := range userInfoList {
			if v.Phone == "" || v.PhoneArea == "" {
				continue
			}

			if v.LastNotifyBalanceNegativeTime.Valid {
				return
			}

			currentSmsCount, err := svc.smsCount.SmsCountGet(v.Phone, constant.BalanceNegative)
			if err != nil {
				svc.log.WithField("phone", v.Phone).ErrorE(err, "get user phone balance negative sms count failed")
				continue
			}
			if currentSmsCount != 0 {
				continue
			}

			input := notify.SmsNotifyInput{
				PhoneArea:        v.PhoneArea,
				Phone:            libs.PhoneBuildWithArea(v.PhoneArea, v.Phone),
				SmsTemplateParam: libs.BuildSMSTemplateParamBalanceWarning(fmt.Sprintf("%d", 0)),
				SMSType:          constant.BalanceNegative,
			}

			_, notifyErr := svc.smsNotifyChannel.Notify(input)
			if notifyErr != nil {
				svc.log.WithField("notify_input", input).ErrorE(notifyErr, "notify failed")
				continue
			}

			_, err = us.UserSettingUpdate(nil, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uid": bill.UID,
				},
			}, map[string]interface{}{
				"last_notify_balance_negative_time": time.Now(),
			})
			if err != nil {
				svc.log.WithField("uid", bill.UID).ErrorE(err, "update user setting failed")
				continue
			}

			notifyRecord := &notifyModel.NotifyRecord{
				SubName: v.SubName,
				UID:     v.UID,
				Type:    notifyModel.NotifyTypeBalanceNegative,
				Channel: notify.ChannelSMS,
				Content: input.String(),
				Phone:   v.Phone,
			}
			// 保存通知记录
			err = svc.notify.SaveNotifyRecord(nil, notifyRecord)
			if err != nil {
				svc.log.WithField("notify_record", notifyRecord).ErrorE(err, "save notify failed")
				continue
			}

			err = svc.smsCount.SmsCountSet(v.Phone, constant.BalanceNegative)
			if err != nil {
				svc.log.WithField("phone", v.Phone).ErrorE(err, "increment sms count failed")
				continue
			}
		}

	} else {
		if us.LastNotifyBalanceNegativeTime.Valid {
			_, err = us.UserSettingUpdate(nil, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uid": us.UID,
				},
				NotNullField: []string{"last_notify_balance_negative_time"},
			}, map[string]interface{}{
				"last_notify_balance_negative_time": nil,
			})
			if err != nil {
				svc.log.WithField("uid", bill.UID).ErrorE(err, "update user setting negative time failed")
				return err
			}
		}
	}
	return nil
}

func (svc *BCService) sendMsgOfBalanceNegativeForSubUser(bill *model.Bill) (err error) {
	if bill == nil || bill.DetailsEntity == nil {
		return
	}

	if bill.DetailsEntity.SubName == "" {
		return
	}

	suw, err := svc.subUserWalletGet(nil, bill.DetailsEntity.SubName)
	if err != nil {
		svc.log.WithField("sub_name", bill.DetailsEntity.SubName).ErrorE(err, "get sub user wallet failed")
		return
	}

	if suw.PaymentMethod != constant.SUPaymentMethodQuota {
		svc.log.WithField("sub_name", bill.DetailsEntity.SubName).WithField("subuser.payment_method", suw.PaymentMethod).Info("sub user payment method is not quota")
		return
	}

	us, err := svc.user.GetSetting(bill.UID)
	if err != nil {
		svc.log.WithField("uid", bill.UID).ErrorE(err, "get user setting by uid failed")
		return
	}

	if suw.Quota < 0 {
		if us.AdditionalEntity.SubUserBalanceAlert[suw.SubName] {
			return
		}

		subUser := userModel.UserBalanceNegativeInfo{}
		err = db_helper.GlobalDBConn().Model(userModel.SubUser{}).
			Select("sub_name, phone_area, phone").
			Where("deleted_at is null").
			Where("uid = ?", bill.UID).
			Where("sub_name =?", bill.DetailsEntity.SubName).
			Where("phone_area is not null").
			Where("phone is not null").
			Find(&subUser).Error
		if err != nil {
			svc.log.WithField("uid", bill.UID).WithField("sub_name", bill.DetailsEntity.SubName).ErrorE(err, "get sub user info failed")
			return err
		}

		if subUser.Phone == "" || subUser.PhoneArea == "" {
			return
		}

		currentSmsCount, err := svc.smsCount.SmsCountGet(subUser.Phone, constant.BalanceNegative)
		if err != nil {
			svc.log.WithField("subuser_phone", subUser.Phone).ErrorE(err, "get sub user phone balance negative sms count failed")
			return err
		}
		if currentSmsCount != 0 {
			return nil
		}

		us.AdditionalEntity.SubUserBalanceAlert[suw.SubName] = true
		additionalInfoJSON, err := json.Marshal(us.AdditionalEntity)
		if err != nil {
			svc.log.ErrorE(err, "marshal additional info failed")
			return err
		}
		_, err = us.UserSettingUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid": bill.UID,
			},
		}, map[string]interface{}{
			"additional_info": additionalInfoJSON,
		})
		if err != nil {
			svc.log.WithField("uid", bill.UID).
				WithField("sub_name", suw.SubName).
				WithField("additional_info", us.AdditionalEntity).
				ErrorE(err, "update user setting sub_user_balance_alert_time failed")
			return err
		}

		input := notify.SmsNotifyInput{
			PhoneArea:        subUser.PhoneArea,
			Phone:            libs.PhoneBuildWithArea(subUser.PhoneArea, subUser.Phone),
			SmsTemplateParam: libs.BuildSMSTemplateParamBalanceWarning(fmt.Sprintf("%d", 0)),
			SMSType:          constant.BalanceNegative,
		}

		_, err = svc.smsNotifyChannel.Notify(input)
		if err != nil {
			svc.log.WithField("sms_input", input).ErrorE(err, "notify failed")
			return err
		}

		notifyRecord := &notifyModel.NotifyRecord{
			SubName: subUser.SubName,
			UID:     subUser.UID,
			Type:    notifyModel.NotifyTypeBalanceNegative,
			Channel: notify.ChannelSMS,
			Content: input.String(),
			Phone:   subUser.Phone,
		}
		err = svc.notify.SaveNotifyRecord(nil, notifyRecord)
		if err != nil {
			svc.log.WithField("notify_record", notifyRecord).ErrorE(err, "save notify failed")
			return err
		}

		err = svc.smsCount.SmsCountSet(subUser.Phone, constant.BalanceNegative)
		if err != nil {
			svc.log.WithField("subuser_phone", subUser.Phone).ErrorE(err, "set subuser phone sms count failed")
			return err
		}
	} else {
		if us.AdditionalEntity.SubUserBalanceAlert[suw.SubName] {
			us.AdditionalEntity.SubUserBalanceAlert[suw.SubName] = false
			additionInfoJSON, err := json.Marshal(us.AdditionalEntity)
			if err != nil {
				svc.log.WithField("subname", suw.SubName).WithField("addition_info", us.AdditionalEntity).ErrorE(err, "marshal additional info failed")
				return err
			}

			_, err = us.UserSettingUpdate(nil, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uid": bill.UID,
				},
			}, map[string]interface{}{
				"additional_info": additionInfoJSON,
			})
			if err != nil {
				svc.log.WithField("uid", bill.UID).WithField("sub_name", suw.SubName).WithField("additional_info", us.AdditionalEntity).ErrorE(err, "update user setting additional info failed")
				return err
			}
		}
	}

	return nil
}

func (svc *BCService) afterUpdateWallet(tx *gorm.DB, params *model.AfterUpdateWalletParams) (queueTxMsgUUIDList []string, err error) {
	if params == nil || params.Bill == nil {
		return nil, businesserror.ErrInternalError
	}

	l := svc.log.WithField("typ", params.AfterOperateType)
	switch params.AfterOperateType {
	case constant.UpdateWalletSkip: // 无处理内容
	// skip
	case constant.UpdateWalletOperateByOrderType: // 根据orderType进行处理
		err = svc.afterUpdateWalletOperateByOrderType(tx, params)
		if err != nil {
			l.ErrorE(err, "operate by order failed.")
			return
		}

	case constant.UpdateWalletCharge: // 扣款
		err = svc.updateOrderStatus(tx, params.Bill.OrderUUID, constant.OrderStatusSuccess, false)
		if err != nil {
			l.WithField("billUUID", params.Bill.UUID).
				WithField("OrderUUID", params.Bill.OrderUUID).
				ErrorE(err, "updateOrderStatus failed.")
			return
		}

	case constant.UpdateWalletPaygSettle: // payg定时结算
		err = svc.chargingUpdateSettleTime(tx, params.Bill.RuntimeUUID, params.Bill.OrderUUID, params.Bill.CreatedAt, params.Bill.DetailsEntity.ChargeFrom)
		if err != nil {
			l.ErrorE(err, "update instance last settle time failed, last_settle_at:%+v", params.Bill.DetailsEntity.ChargeFrom)
			return
		}

	case constant.UpdateWalletPaygShutdown: // payg关机结算
		ro := userModel.OperateRecord{
			UID:        params.Bill.UID,
			Operate:    constant.OperateInstancePowerOffByCharge,
			EntityUUID: params.Bill.ProductUUID,
			SubName:    params.Bill.DetailsEntity.SubName,
		}
		_ = ro.OperateRecordCreate(nil)
		fallthrough

	case constant.UpdateWalletChargeTypeToPrepayStopCharging:
		err = svc.chargingStop(tx, params.Bill.RuntimeUUID, params.Bill.OrderUUID, &params.Bill.CreatedAt, params.Bill.DetailsEntity.ChargeFrom)
		if err != nil {
			l.ErrorE(err, "update instance last settle time failed")
			return
		}

	case constant.UpdateWalletDataDiskPaygSettle:
		err = svc.dataDiskChargingUpdateByMap(tx, params.Bill.ProductUUID, params.DataDiskUpdateMap)
		if err != nil {
			l.WithField("params", params).ErrorE(err, "update ddc by map failed")
			return
		}

	case constant.UpdateWalletInstanceCallShutdown:
		err = svc.chargingStopForAsyncJob(tx, params.Bill.OrderUUID, params.UpdateWalletInstanceCallShutdownChargingStoppedAt, params.Bill.DetailsEntity.ChargeFrom)
		if err != nil {
			l.ErrorE(err, "update instance last settle time failed")
			return
		}
	case constant.UpdateWalletSpecialRechargeInsertRecord:
		err = svc.rechargeRecordCreateByBill(tx, params.Bill)
		if err != nil {
			return
		}
	default:
		l.WithField("bill_uuid", params.Bill.UUID).
			WithField("afterHook", libs.IndentString(params.AfterHook)).
			Warn("unknown after update wallet optType.")
		return nil, businesserror.ErrInternalError
	}

	if params.AfterHook != nil {
		msgUUID, mErr := message_model.TxPubMessage(tx, params.AfterHook)
		if mErr.GetError() != nil {
			err = mErr.GetError()
			l.ErrorE(err, "TxPubMessage failed")
			return nil, businesserror.ErrInternalError
		}
		queueTxMsgUUIDList = append(queueTxMsgUUIDList, msgUUID)
	}

	if params.AfterFunc != nil {
		err = params.AfterFunc(tx)
		if err != nil {
			l.ErrorE(err, "afterFunc call failed")
			return
		}
	}

	now := time.Now()
	params.Bill.ConfirmAt = &now

	return
}

func (svc *BCService) afterUpdateWalletOperateByOrderType(tx *gorm.DB, params *model.AfterUpdateWalletParams) (err error) {
	var (
		order *model.Order
	)
	// get order
	if params.Order != nil {
		order = params.Order
	} else {
		order, err = svc.getOrder(params.Bill.OrderUUID)
		if err != nil {
			svc.log.WithError(err).Error("get order failed")
			return
		}
	}

	switch order.OrderType {
	case constant.OrderTypeCreateInstance, constant.OrderTypeCloneInstance:
		ddc := order.ToDataDiskCharging()
		if ddc != nil {
			err = svc.dds.UpdateDDA(tx, &ddsModel.UpdateDDAParams{
				MachineID:   order.MachineID,
				ProductUUID: order.ProductUUID,
				FinallySize: order.PriceEntity.ExpandDataDisk,
			})
			if err != nil {
				svc.log.WithField("order", params.Bill.OrderUUID).ErrorE(err, "OrderTypeCreateInstance: expand data disk create dda failed")
				return
			}

			err = svc.dataDiskChargingCreate(tx, ddc)
			if err != nil {
				svc.log.WithError(err).Error("create ddc record failed")
				return
			}
		}

		var runtimeUUID constant.ContainerRuntimeUUID
		var (
			taskRequest  = order.RuntimeEntity
			priceRequest = order.PriceEntity
		)
		if taskRequest == nil || priceRequest == nil {
			svc.log.WithField("order_uuid", order.UUID).Error("RuntimeEntity or PriceEntity is null")
			return businesserror.ErrInternalError
		}
		if ok := taskRequest.Validate(); !ok {
			return businesserror.ErrInstanceCreateExceptionOccurred
		}

		runtimeUUID = taskRequest.RuntimeUUID

		// insert instance record
		instance := &instanceModel.Instance{
			InstanceUUID: constant.InstanceUUIDType(order.ProductUUID),
			UID:          taskRequest.UID,
			SubName:      order.SubName,
			Creator:      order.SubName,
			MachineID:    taskRequest.MachineID,
			RuntimeUUID:  runtimeUUID,
			RegionSign:   order.MachineEntity.RegionSign,
			ReqGPUAmount: taskRequest.ReqGPUAmount,
			Status:       constant.InstanceRecordInserted,
			StatusAt:     svc.t.Now(),
			OOMKilled:    false,
			ChargeType:   taskRequest.ChargeType,
			OrderUUID:    taskRequest.OrderUUID,
			StartedAt: sql.NullTime{
				Time:  time.Time{},
				Valid: false,
			},
			StoppedAt: sql.NullTime{
				Time:  time.Time{},
				Valid: false,
			},
			HaveGpuResources: true,
			Name:             taskRequest.Name,
			Description:      taskRequest.Description,
			ImageName:        taskRequest.Image,
			PrivateImageUUID: taskRequest.PrivateImageUUID,
			ReproductionUUID: taskRequest.ReproductionUUID,
			ReproductionID:   taskRequest.ReproductionID,
		}

		if taskRequest.ChargeType.IsPayg() {
			instance.PaygPrice = taskRequest.PaygPrice
			instance.OriginPaygPrice = priceRequest.OriginPrice
		}
		if taskRequest.ExpiredAt != nil {
			instance.ExpiredAt = sql.NullTime{
				Time:  *taskRequest.ExpiredAt,
				Valid: !taskRequest.ChargeType.IsPayg(),
			}
		} else {
			instance.ExpiredAt = sql.NullTime{
				Time:  time.Time{},
				Valid: false,
			}
		}

		// 关于迁移的特殊处理
		if order.OrderType == constant.OrderTypeCloneInstance || order.MigrateInstanceUUID != "" {
			var srcInstance instanceModel.Instance
			srcInstance, err = svc.instance.GetInstance(order.MigrateInstanceUUID)
			if err != nil {
				return err
			}

			instance.AdditionalEntity.SrcInstanceUUID = srcInstance.InstanceUUID.String()
			instance.AdditionalEntity.SrcRuntimeUUID = srcInstance.RuntimeUUID.String()
			instance.AdditionalEntity.SrcMachineID = srcInstance.MachineID
			instance.AdditionalEntity.CopyDataDiskAfterClone = order.RuntimeEntity.CopyDataDiskAfterClone
			instance.AdditionalEntity.CopyDataDiskAfterCloneAdminOperate = order.RuntimeEntity.CopyDataDiskAfterCloneAdminOperate
		}

		svc.log.Info("container_runtime record [%s] inserted. Then insert instance record.", order.RuntimeUUID)
		err = svc.instance.InsertOrUpdateInstance(instance)
		if err != nil {
			svc.log.WarnE(err, "Insert instance [%s] record failed.", constant.InstanceUUIDType(order.ProductUUID))
			return businesserror.ErrInternalError
		}
	case constant.OrderTypeRenewalInstance:
		ddChange := order.ToDataDiskChargingChangeParams()
		if ddChange != nil {
			err = svc.dataDiskChargingChange(tx, ddChange)
			if err != nil {
				svc.log.WithError(err).Error("")
			}
		}
	case constant.OrderTypeDataDiskExpandOfPrepay, constant.OrderTypeDataDiskExpand, constant.OrderTypeDataDiskReduce:
		var exist bool
		exist, err = svc.dataDiskChargingExist(order.ProductUUID)
		if err != nil {
			svc.log.WithField("productUUID", order.ProductUUID).Info("create ddc: get record failed.")
			return err
		}

		// 更新计费表
		if !exist {
			err = svc.dataDiskChargingCreate(tx, order.ToDataDiskCharging())
		} else {
			err = svc.dataDiskChargingChange(tx, order.ToDataDiskChargingChangeParams())
		}
		if err != nil {
			log.WithFields(map[string]interface{}{"existDDC": exist, "err": err, "newOrder": order}).Error("ddc change size failed")
			return err
		}

		// 更新dda
		err = svc.dds.UpdateDDA(tx, &ddsModel.UpdateDDAParams{
			MachineID:   order.MachineID,
			ProductUUID: order.ProductUUID,
			FinallySize: order.PriceEntity.ExpandDataDisk,
		})
		if err != nil {
			svc.log.WithField("orderUUID", order.UUID).ErrorE(err, "update dda failed")
			return err
		}
	// 发送消息在外部进行

	case constant.OrderTypeCreateDeploymentDurationPkg:
		bill := params.Bill

		subName := ""
		dcListStr := ""
		si, ok := order.DetailsEntity["ddp_subname"]
		if ok {
			subName = si.(string)
		}

		rsStr, ok := order.DetailsEntity["ddp_data_center_list_str"]
		if ok {
			dcListStr = rsStr.(string)
		}

		ddp := &deploymentModel.DeploymentDurationPkg{
			UID:            order.UID,
			DeploymentUUID: order.ProductUUID,
			OrderUUID:      order.UUID,
			GpuType:        order.MachineEntity.GpuType.Name,
			//RegionSignList:   regionSignListStr,
			DCList:           dcListStr,
			PaygPrice:        libs.AssetRound((bill.PayByBalance/bill.Asset)*order.PriceEntity.DDPPaygPrice, false),
			PaygPriceVoucher: libs.AssetRound((bill.PayByVoucher/bill.Asset)*order.PriceEntity.DDPPaygPrice, false),
			Total:            order.ChargeType.ToSecond() * int64(order.PriceEntity.Duration),
			Balance:          order.ChargeType.ToSecond() * int64(order.PriceEntity.Duration),
			Status:           constant.DDPInEffect,
			SubName:          subName,
		}

		err = ddp.DDPCreate(tx)
		if err != nil {
			svc.log.ErrorE(err, "create ddp failed")
			return
		}
	case constant.OrderTypeDeploymentDurationPkgRefund:
		ddp := &deploymentModel.DeploymentDurationPkg{}
		err = ddp.DDPUpdate(tx, db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"order_uuid": order.RefundEntity.OrderUUID,
		}}, map[string]interface{}{
			"status": constant.DDPRefunded,
		})
		if err != nil {
			svc.log.ErrorE(err, "OrderTypeDeploymentDurationPkgRefund update ddp failed")
			return
		}
	}

	if params.Order == nil {
		err = svc.updateOrderStatus(tx, params.Bill.OrderUUID, constant.OrderStatusSuccess, false)
		if err != nil {
			svc.log.WithField("billUUID", params.Bill.UUID).
				WithField("OrderUUID", params.Bill.OrderUUID).
				WithError(err).Error("updateOrderStatus failed.")
			return
		}
	}

	return
}

func (svc *BCService) getBalance(uid int) (balance int64, err error) {
	wallet, err := svc.getWallet(nil, uid)
	if err != nil {
		return
	}
	return wallet.Assets, nil
}

// [钱包] 获取资产详情
func (svc *BCService) getBalances(uidList []int) (balance map[int]int64, err error) {
	balance = make(map[int]int64)
	if len(uidList) == 0 {
		return
	}

	walletList := make([]model.UserWallet, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserWallet{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "uid", InSet: uidList}}},
		NoLimit:         true,
	}, &walletList).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get wallet list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	//if len(uidList) != len(walletList) {
	//	svc.log.WithFields(map[string]interface{}{"require_num": len(uidList), "get_num": len(walletList)}).
	//		Warn("len(uidList) != len(walletList)")
	//	err = businesserror.ErrInternalError
	//	return
	//}

	for _, v := range walletList {
		balance[v.UID] = v.Assets
	}
	return
}

func (svc *BCService) getSubUserWalletBalance(uidSubUserList map[int][]string) (balance map[int]map[string]*model.SubUserWallet, err error) {
	balance = make(map[int]map[string]*model.SubUserWallet)
	for uid, subUsers := range uidSubUserList {
		balance[uid] = make(map[string]*model.SubUserWallet)
		subUserWallets, err := svc.subUserWalletGetList(subUsers)
		if err != nil {
			svc.log.WithError(err).Error("get subUserWallet list failed")
			err = businesserror.ErrDatabaseError
			return nil, err
		}
		for _, subUserWallet := range subUserWallets {
			subName := subUserWallet.SubName
			balance[uid][subName] = &subUserWallet
		}

	}
	return
}

func (svc *BCService) getSubUserPaygSettle(subUserList []string) (balance map[string]userModel.SubUserListForPaygSettle, err error) {
	balance = make(map[string]userModel.SubUserListForPaygSettle)
	subUserWallets, err := svc.subUserWalletGetList(subUserList)
	if err != nil {
		svc.log.ErrorE(err, "get subUserWallet list failed")
		err = businesserror.ErrDatabaseError
		return nil, err
	}

	for _, subUserWallet := range subUserWallets {
		su, err := svc.user.SubUserGet(subUserWallet.SubName)
		if err != nil {
			svc.log.WithError(err).Error("get subUserWallet failed")
			err = businesserror.ErrDatabaseError
			return nil, err
		}
		balance[subUserWallet.SubName] = userModel.SubUserListForPaygSettle{
			SubName:                       subUserWallet.SubName,
			Quota:                         subUserWallet.Quota,
			PaymentMethod:                 subUserWallet.PaymentMethod,
			InSufficientIsBalanceShutdown: su.InSufficientIsBalanceShutdown,
		}
	}

	return balance, nil
}

func (svc *BCService) getAllBalance() (balance map[int]int64, err error) {
	balance = make(map[int]int64)

	walletList := make([]model.UserWallet, 0)
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.UserWallet{},
		NoLimit:         true,
	}, &walletList).GetError()
	if err != nil {
		svc.log.WithError(err).Error("get wallet list failed")
		err = businesserror.ErrDatabaseError
		return
	}

	for _, v := range walletList {
		balance[v.UID] = v.Assets
	}
	return
}

// 为了防止创建用户时创建钱包失败, 单独写一个getWallet方法
func (svc *BCService) getWallet(tx *gorm.DB, uid int) (wallet *model.UserWallet, err error) {
	wallet = new(model.UserWallet)
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.UserWallet{},
		Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": uid}},
	}, wallet).GetError()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没找到, 创建
			wallet.UID = uid
			err = db_helper.InsertOne(db_helper.QueryDefinition{
				ModelDefinition: &model.UserWallet{},
				InsertPayload:   wallet,
			}).GetError()
			if err != nil {
				svc.log.WithError(err).Error("create wallet failed.")
				err = businesserror.ErrDatabaseError
			}
			return
		}

		svc.log.WithError(err).Error("get user wallet failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

// 获取钱包，增加了代金券余额
func (svc *BCService) getWalletWithVoucher(uid int, productType constant.ProductType, chargeType constant.ChargeType, machineId string) (wallet *model.UserWallet, err error) {
	wallet = new(model.UserWallet)
	wallet, err = svc.getWallet(nil, uid)
	if err != nil {
		return nil, err
	}

	var voucherList []model.UserVoucher

	voucherList, wallet.VoucherBalance, err = svc.voucherGetUserBalance(nil, uid, productType, chargeType, machineId, nil, "")
	if err != nil {
		svc.log.WithError(err).Error("get voucher balance failed")
		return
	}

	// 代金券即将过期时间<=24小时的
	for _, voucher := range voucherList {
		if voucher.InvalidAt.Sub(time.Now()).Hours() <= 24 {
			wallet.ToExpireVoucherNum++
		}
	}

	wallet.CreditWallet, err = svc.creditWalletGet(uid)
	if err != nil {
		svc.log.WithError(err).Error("get credit wallet failed")
		return
	}

	// 可用优惠券张数
	wallet.AvailableCouponNum, err = svc.getAvailableCouponNumByUID(nil, uid)
	if err != nil {
		svc.log.WithError(err).Error("get user coupon num failed")
		return
	}

	return
}
func (svc *BCService) getWalletWithAllVoucher(uid int) (wallet *model.UserWallet, err error) {
	wallet = new(model.UserWallet)
	wallet, err = svc.getWallet(nil, uid)
	if err != nil {
		return nil, err
	}

	var voucherList []model.UserVoucher

	voucherList, wallet.VoucherBalance, err = svc.AllVoucherGetUserBalance(nil, uid)
	if err != nil {
		svc.log.WithError(err).Error("get voucher balance failed")
		return
	}

	var certainConditionsVoucherBalance int64
	// 代金券即将过期时间<=24小时的
	for _, voucher := range voucherList {
		if voucher.InvalidAt.Sub(time.Now()).Hours() <= 24 {
			wallet.ToExpireVoucherNum++
		}
		if voucher.UseProduct != constant.ProductTypeAllFake {
			certainConditionsVoucherBalance += voucher.Balance
		}
	}
	wallet.CertainConditionsVoucherBalance = certainConditionsVoucherBalance

	wallet.CreditWallet, err = svc.creditWalletGet(uid)
	if err != nil {
		svc.log.WithError(err).Error("get credit wallet failed")
		return
	}

	// 可用优惠券张数
	wallet.AvailableCouponNum, err = svc.getAvailableCouponNumByUID(nil, uid)
	if err != nil {
		svc.log.WithError(err).Error("get user coupon num failed")
		return
	}

	return
}

// 获取钱包，创建订单时，根据订单计费方式不同返回不同的代金券余额
func (svc *BCService) getWalletForCreateOrder(uid int, productType constant.ProductType, chargeType constant.ChargeType, machineId string, regionSign []constant.RegionSignType, gpuName string) (wallet *model.UserWallet, err error) {
	wallet = new(model.UserWallet)
	wallet, err = svc.getWallet(nil, uid)
	if err != nil {
		return nil, err
	}

	_, wallet.VoucherBalance, err = svc.voucherGetUserBalance(nil, uid, productType, chargeType, machineId, regionSign, gpuName)
	if err != nil {
		svc.log.WithError(err).Error("get voucher balance failed")
		return
	}
	return
}

// 获取用户充值总额
func (svc *BCService) countUserTotalRecharge(uid int) (assets int64, err error) {
	var list []model.RechargeRecord
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).
		Where("uid = ?", uid).
		Where("pathway in (?)", []int{1, 2, 3, 4, 5}).
		Where("callback_at is not null").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).WithField("uid", uid).Warn("get rr failed")
		err = businesserror.ErrDatabaseError
		return
	}

	for _, v := range list {
		assets += v.Assets
	}
	return
}

// 获取用户近xx天的充值金额
func (svc *BCService) countUserRechargeAssetRecentDays(uid, days int) (asset int64, err error) {
	var res []int64
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).
		Where("uid = ?", uid).
		Where("callback_at is not null").
		Where("pathway in (1,2)").
		Where("created_at > ?", time.Now().AddDate(0, 0, -1*days)).
		Pluck("payg_price", &res).
		Error
	if err != nil {
		svc.log.WithError(err).Error("count recharge asset failed")
		err = businesserror.ErrDatabaseError
		return
	}

	for _, v := range res {
		asset += v
	}

	return
}

// -------------- wxpay ----------------

// 生成充值二维码
func (svc *BCService) rechargeCreate(uid int, userPhone string, asset int64, pathway constant.RechargePathway) (codeUrl, billUUID string, err error) {
	ctx := context.Background()
	billUUID = libs.RandNumberString()
	expireAt := time.Now().Add(constant.RechargeExpireDuration)
	rr := &model.RechargeRecord{
		UID:       uid,
		UserPhone: userPhone,
		BillUUID:  billUUID,
		Pathway:   pathway,
		Assets:    asset,
		ExpireAt:  &expireAt,
	}

	if pathway == constant.Alipay {
		rr.Note.AliAppID = svc.aliPay.GetDefault().AppID
	}

	isPreRecharge, err := svc.beforeRechargeCheck(uid)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "beforeRechargeCheck failed")
		return
	}

	if isPreRecharge {
		rr.PreRecharge = true
	}

	// 创建recharge_record记录
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.RechargeRecord{},
		InsertPayload:   rr,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("uid", uid).Error("wxPayCreate: create rechargeRecord failed")
		return
	}

	if pathway == constant.WeChatPay {
		codeUrl, err = svc.wxPay.Create(ctx, &payment.CreateOrderParams{
			BillUUID: rr.BillUUID,
			Amount:   asset,
		})
		if err != nil {
			svc.log.WithError(err).Error("wxPayCreate: create wxpay codeUrl failed")
			err = businesserror.ErrWalletWxPayError
			return
		}
	} else if pathway == constant.Alipay {
		aliUrl := new(url.URL)
		aliUrl, err = svc.aliPay.Create(rr.Note.AliAppID, billUUID, asset)
		if err != nil {
			svc.log.WithError(err).Error("alipay create failed")
			err = businesserror.ErrWalletAliPayError
		}
		aliUrlStr := fmt.Sprint(aliUrl)
		codeUrl, err = svc.rechargeParseAliUrl(aliUrlStr)
		if err != nil {
			svc.log.WithError(err).Error("rechargeParseAliUrl parse failed")
			return
		}

		err = svc.aliRechargeTimeout.Set(billUUID, aliUrlStr, constant.RechargeExpireDuration)
		if err != nil {
			svc.log.WithError(err).Error("set redis_plugin failed")
			err = businesserror.ErrInternalError
			return
		}
	} else {
		err = businesserror.ErrInternalError
		return
	}
	return

}

func (svc *BCService) appletRechargeCreate(uid int, userPhone, openId string, asset int64) (resp *payment.PrepayWithRequestPaymentResponse, billUUID string, err error) {
	ctx := context.Background()
	billUUID = libs.RandNumberString()
	expireAt := time.Now().Add(constant.RechargeExpireDuration)
	rr := &model.RechargeRecord{
		UID:       uid,
		UserPhone: userPhone,
		BillUUID:  billUUID,
		Pathway:   constant.WechatAppletPay,
		Assets:    asset,
		ExpireAt:  &expireAt,
	}

	isPreRecharge, err := svc.beforeRechargeCheck(uid)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "beforeRechargeCheck failed")
		return
	}

	if isPreRecharge {
		rr.PreRecharge = true
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.RechargeRecord{},
			InsertPayload:           rr,
		}).GetError()
		if err != nil {
			svc.log.WithError(err).WithField("uid", uid).Error("wxPayCreate: create rechargeRecord failed")
			return
		}
		resp, err = svc.wxPay.AppletCreate(ctx, &payment.CreateOrderParams{
			BillUUID: rr.BillUUID,
			Amount:   asset,
		}, openId)
		if err != nil {
			svc.log.WithError(err).Error("wxPayCreate: create wx applet pay prepay id failed")
			err = businesserror.ErrWalletWxPayError
			return
		}
		return
	})
	if err != nil {
		svc.log.WithError(err).Error("recharge: create bill or create codeUrl failed")
		return
	}
	return
}

// 检查是否充值成功(本地)
func (svc *BCService) rechargeCheckSuccess(billUUID string) (res map[string]interface{}, err error) {
	res = map[string]interface{}{"status": false}
	rr := new(model.RechargeRecord)
	rr, err = svc.rechargeGetRecord(billUUID)
	if err != nil {
		return
	}
	res["recharge_asset"] = rr.Assets

	if rr.CallbackAt == nil {
		return
	}
	res["status"] = true

	wallet := new(model.UserWallet)
	wallet, err = svc.getWallet(nil, rr.UID)
	if err != nil {
		return
	}
	res["balance"] = wallet.Assets

	return
}

// FirstRechargeUpdateSendSms 用户是第一次充值发送短信功能调整
// 开启发送短信功能，判断recharge_asset达到哪个阈值，recharge_asset<=100，阈值为10，recharge_asset<=500，阈值为20，recharge_asset>100，阈值为50
func (svc *BCService) firstRechargeUpdateSendSms(uid int, assets int64) (err error) {
	count, err := svc.GetRechargeRecordCountByUserId(uid)
	if err != nil {
		return err
	}

	if count == 1 {
		userSetting, err := svc.user.GetSetting(uid)
		if err != nil {
			return err
		}

		userSettingUpdate := make(map[string]interface{})
		if userSetting.NotifyBalanceWarningStatus == constant.NotifyBalanceWarningStatusClosed {
			userSettingUpdate["notify_balance_warning_status"] = constant.NotifyBalanceWarningStatusNormalOpened
			if assets <= 100000 {
				userSettingUpdate["notify_balance_warning_money"] = 10
			} else if assets > 100000 && assets <= 500000 {
				userSettingUpdate["notify_balance_warning_money"] = 20
			} else {
				userSettingUpdate["notify_balance_warning_money"] = 50
			}
		}

		err = db_helper.UpdateOne(db_helper.QueryDefinition{
			ModelDefinition: &userModel.UserSetting{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
				"uid": uid,
			}},
		}, userSettingUpdate).GetError()
		if err != nil {
			return err
		}
	}
	return nil
}

// 取消微信订单
func (svc *BCService) rechargeWxClose(billUUID string) (err error) {
	ctx := context.Background()
	err = svc.wxPay.Close(ctx, billUUID)
	if err != nil {
		svc.log.WithField("billUUID", billUUID).WithError(err).Error("wxPay: cancel failed")
		return err
	}

	err = svc.bcRecharge.Remove(billUUID)
	if err != nil {
		svc.log.WithError(err).Error("redis_plugin: remove recharge query record failed")
		err = nil
	}
	return
}

// 主动查询是否充值成功(调微信接口)
func (svc *BCService) rechargeWxQuery(billUUID string) (string, error) {
	// 先查一遍本地
	_, err := svc.rechargeGetRecord(billUUID)
	if err != nil {
		svc.log.WithError(err).Info("get recharge record failed")
		return "", err
	}

	ctx := context.Background()
	resp := new(payments.Transaction)
	resp, err = svc.wxPay.Query(ctx, billUUID)
	if err != nil {
		svc.log.WithField("billUUID", billUUID).WithError(err).Error("wxPay: query failed")
		return "", err
	}

	if *resp.OutTradeNo != billUUID {
		svc.log.WithField("out_trade_no", resp.OutTradeNo).WithField("billUUID", billUUID).Error("wxPay: query failed")
		return "", businesserror.ErrWalletParseWxNotifyError
	}

	status := ""
	status, err = svc.rechargeWxDealTradeResult(resp)
	if err != nil {
		svc.log.WithError(err).Error("deal recharge confirm info failed")
		return "", err
	}

	return status, nil
}

func (svc *BCService) appletRechargeWxQuery(billUUID string) (string, error) {
	_, err := svc.rechargeGetRecord(billUUID)
	if err != nil {
		svc.log.WithError(err).Info("get recharge record failed")
		return "", err
	}

	ctx := context.Background()
	resp := new(payments.Transaction)
	resp, err = svc.wxPay.AppletQuery(ctx, billUUID)
	if err != nil {
		svc.log.WithField("billUUID", billUUID).WithError(err).Error("wechat applet pay: query failed")
		return "", err
	}

	if *resp.OutTradeNo != billUUID {
		svc.log.WithField("out_trade_no", resp.OutTradeNo).WithField("billUUID", billUUID).Error("wechat applet pay: query failed")
		return "", businesserror.ErrWalletParseWxNotifyError
	}

	status := ""
	status, err = svc.rechargeWxDealTradeResult(resp)
	if err != nil {
		svc.log.WithError(err).Error("deal recharge confirm info failed")
		return "", err
	}

	return status, nil
}

// 微信回调
func (svc *BCService) rechargeWxNotify(request *http.Request) error {
	if request == nil {
		return businesserror.ErrInternalError
	}
	ctx := context.Background()

	// 解析消息
	resp, err := svc.wxPay.ParseNotify(ctx, request)
	if err != nil {
		svc.log.WithError(err).Error("parse wxpay notify failed")
		return businesserror.ErrWalletParseWxNotifyError
	}
	if resp == nil {
		svc.log.WithError(err).Error("parse wxpay notify failed")
		return businesserror.ErrWalletParseWxNotifyError
	}

	_, err = svc.rechargeWxDealTradeResult(resp)
	if err != nil {
		svc.log.WithError(err).Error("deal recharge confirm info failed")
		return err
	}

	return nil
}

// 对微信返回内容解析
func (svc *BCService) rechargeWxDealTradeResult(trade *payments.Transaction) (status string, err error) {
	if trade == nil {
		svc.log.Info("rechargeWxDealTradeResult: trade is nil")
		return "", businesserror.ErrInternalError
	}

	if trade.OutTradeNo == nil {
		svc.log.Info("rechargeWxDealTradeResult: trade.OutTradeNo")
		return "", businesserror.ErrInternalError
	}
	billUUID := *trade.OutTradeNo

	svc.log.WithField("wx response", libs.IndentString(trade)).Warn("rechargeWxDealTradeResult!!!")

	// 查找账单, 状态确认
	rr := new(model.RechargeRecord)
	rr, err = svc.rechargeGetRecord(billUUID)
	if err != nil {
		svc.log.WithField("billUUID", billUUID).WithError(err).Error("get recharge record failed.")
		return
	}

	// 加锁, 每条记录一个锁
	lock := new(redis_plugin.RedisMutex)
	lock, err = svc.setLockForUpdateRechargeRecord(billUUID)
	if err != nil {
		return "", err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	status = *trade.TradeState
	switch status {
	case "SUCCESS": // 支付成功
		if rr.CallbackAt != nil {
			return
		}

		// 确认金额
		if *trade.Amount.Total != rr.Assets {
			svc.log.WithFields(map[string]interface{}{
				"bill_uuid":     billUUID,
				"bill_asset":    rr.Assets,
				"wx_pay_amount": *trade.Amount.PayerTotal,
			}).Error("notify payer amount != local asset recorded")
			err = businesserror.ErrWalletParseWxNotifyError
			return
		}

		rr.PayAccount = *trade.Payer.Openid
		rr.Balance = rr.Assets

		err = svc.rechargeSuccessConfirmRechargeRecord(rr)
		if err != nil {
			return
		}

	case "REFUND": // 转入退款
	case "NOTPAY": // 未支付
	case "CLOSED": // 已关闭
	case "REVOKED": // 已撤销（付款码支付）
	case "USERPAYING": // 用户支付中（付款码支付）
	case "PAYERROR": // 支付失败(其他原因，如银行返回失败)
	case "ACCEPT": // 已接收，等待扣款
	default:

	}
	return
}

//

func (svc *BCService) rechargeSuccessConfirmRechargeRecord(rr *model.RechargeRecord) (err error) {
	var (
		now                = time.Now()
		invitationRecord   *model.VoucherInvitationRecord
		queueTxMsgUUIDList []string
		totalRecharge      int64
		issueLimit         int64 = constant.InviterGetVoucherLimit
	)

	if conf.GetGlobalGsConfig().App.DebugApi {
		issueLimit = constant.InviterGetVoucherLimitForTest
	}

	totalRecharge, err = svc.countUserTotalRecharge(rr.UID)
	if err != nil {
		svc.log.WithError(err).Warn("count failed")
		return err
	}

	// 充值成功后，异步清除用户发票金额缓存
	defer func() {
		if err == nil {
			svc.invalidateInvoiceAmountCache(rr.UID)
		}
	}()

	newBill := &model.Bill{
		UID:           rr.UID,
		UUID:          rr.BillUUID,
		Type:          constant.BillTypeRecharge,
		SubType:       rr.Pathway.ToBillSubType(),
		Asset:         rr.Assets,
		PayByBalance:  rr.Assets,
		UserPhone:     rr.UserPhone,
		DetailsEntity: &model.BillDetail{RechargePathway: rr.Pathway},
	}

	// 签收充值记录, 创建bill, 更新钱包
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		affectRaws, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.RechargeRecord{},
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"bill_uuid": rr.BillUUID, "callback_at": nil}},
		}, map[string]interface{}{
			"callback_at": now,
			"pay_account": rr.PayAccount,
			"balance":     rr.Balance,
		})
		if errDB.IsNotNil() {
			svc.log.WithField("billUUID", rr.BillUUID).WithError(errDB.GetError()).Error("update recharge record failed")
			err = businesserror.ErrDatabaseError
			return
		}
		if affectRaws == 0 {
			return nil
		}

		if rr.PreRecharge {
			return
		}

		// 1.查看是否是被邀请的; 2.被邀请的是否已经发过券; 3.没发过券是否满足100元了
		invitationRecord, err = svc.voucherGetInvitationRecordInfo(rr.UID)
		if err != nil {
			svc.log.WithError(err).WithField("uid", rr.UID).Warn("get invitation record failed")
			return err
		}
		if invitationRecord != nil && totalRecharge+rr.Assets >= issueLimit {
			err = svc.voucherIssueInvitationTicket(tx, invitationRecord.InviterUID, invitationRecord.InviterPhone)
			if err != nil {
				svc.log.WithError(err).Warn("issue invitation voucher failed")
				return err
			}

			err = svc.voucherConfirmInvitationRecord(tx, invitationRecord.ID)
			if err != nil {
				svc.log.WithError(err).WithField("id", invitationRecord.ID).Warn("delete failed")
				return err
			}
		}

		err = svc.updateWallet(tx, newBill)
		if err != nil {
			svc.log.WithError(err).Error("update wallet failed")
			return err
		}

		afterHook := &queue_interface.NewQueueForUserRecharge{
			UserId:    rr.UID,
			UserPhone: rr.UserPhone,
			Asset:     rr.Assets,
			SubType:   queue_interface.AfterUserRecharge,
		}

		queueTxMsgUUIDList, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Bill:             newBill,
			AfterOperateType: constant.UpdateWalletSkip,
			AfterHook:        afterHook,
		})
		if err != nil {
			svc.log.WithError(err).Error("after update wallet failed")
			return err
		}

		err = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.Bill{}, // bill create
			InsertPayload:           newBill,
		}).GetError()
		if err != nil {
			svc.log.WithError(err).Error("insert into bill failed")
			return err
		}
		return
	})
	if err != nil && err != businesserror.ErrOptimisticLockingRollbackButNotReturnError {
		svc.log.WithError(err).Error("confirm recharge failed")
		return
	}

	if len(queueTxMsgUUIDList) > 0 {
		errMsg := svc.q.Pub(queueTxMsgUUIDList...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, queueTxMsgUUIDList)
			err = businesserror.ErrInternalError
			return err
		}
	}

	err = svc.firstRechargeUpdateSendSms(rr.UID, rr.Assets)
	if err != nil {
		svc.log.WithError(err).Error("the first time to recharge the user judgment failed")
		return err
	}

	return
}

// 获取充值记录
func (svc *BCService) rechargeGetRecord(billUUID string) (rr *model.RechargeRecord, err error) {
	rr = new(model.RechargeRecord)
	errDB := db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: &model.RechargeRecord{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"bill_uuid": billUUID}},
	}, rr)
	if errDB.IsNotNil() {
		if errDB.IsRecordNotFoundError() {
			err = businesserror.ErrWalletRechargeRecordNotFound
			return
		}
		svc.log.WithError(errDB.GetError()).Error("get recharge record failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

// 获取多个充值记录
func (svc *BCService) rechargeGetRecordList(billUUIDs []string) (records []model.RechargeRecord, err error) {
	records = make([]model.RechargeRecord, 0)
	errDB := db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: &model.RechargeRecord{},
		Filters: db_helper.QueryFilters{
			InFilters: []db_helper.In{
				{
					Key:   "bill_uuid",
					InSet: billUUIDs,
				},
			},
		},
	}, &records)
	if errDB.IsNotNil() {
		svc.log.WithError(errDB.GetError()).Error("get recharge records failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

// 查询ali账单
func (svc *BCService) rechargeAliQuery(billUUID string) (status string, err error) {
	rechargeRecord, err := svc.rechargeGetRecord(billUUID)
	if err != nil {
		svc.log.WithError(err).Error("get recharge record failed")
		return
	}

	res := new(alipay.TradeQueryRsp)
	amount := payment.QueryAmount{}
	res, amount, err = svc.aliPay.Query(rechargeRecord.Note.AliAppID, billUUID)
	if err != nil {
		if err == payment.AliTradeNotExistErr {
			status = "NOPAY"
			return status, nil
		} else {
			svc.log.WithError(err).Error("alipay query failed")
			err = businesserror.ErrInternalError
			return
		}
	}

	if res == nil {
		svc.log.Info("rechargeAliDealTradeResult: trade is nil")
		return "", businesserror.ErrInternalError
	}

	if res.OutTradeNo == "" {
		svc.log.Info("rechargeAliDealTradeResult: trade.OutTradeNo is empty")
		return "", businesserror.ErrInternalError
	}

	status, err = svc.rechargeAliDealTradeResult(res.TradeStatus, res.OutTradeNo, res.BuyerLogonId, amount)
	return
}

// ali回调
func (svc *BCService) rechargeAliNotify(request *http.Request) (err error) {
	res := new(alipay.Notification)
	amount := payment.QueryAmount{}
	res, amount, err = svc.aliPay.ParseNotify(request)
	if err != nil {
		svc.log.WithError(err).WithField("request", request).Error("parse ali notify failed")
		return businesserror.ErrInternalError
	}

	if res == nil {
		svc.log.WithField("request", request).Info("rechargeAliDealTradeResult: trade is nil")
		return businesserror.ErrInternalError
	}

	if amount.ReceiptAmount != 0 {
		_, err = svc.rechargeAliDealTradeResult(res.TradeStatus, res.OutTradeNo, res.BuyerLogonId, amount)
	}

	if amount.RefundFee != 0 {
		var refundStatus string
		if res.GmtRefund != "" {
			refundStatus = "REFUND_SUCCESS"
		}

		_, err = svc.refundAliPayDealTradeResult(&alipay.TradeFastPayRefundQueryRsp{
			RefundStatus: refundStatus,
			OutTradeNo:   res.OutTradeNo,
		}, payment.RefundQueryAmount{
			TotalAmount:  amount.TotalAmount,
			RefundAmount: amount.RefundFee,
		}, res.OutBizNo)
	}

	return
}

// 处理ali返回信息
func (svc *BCService) rechargeAliDealTradeResult(tradeStatus alipay.TradeStatus, billUUID, aliPayAccount string, amount payment.QueryAmount) (status string, err error) {
	// 查找账单, 状态确认
	rr := new(model.RechargeRecord)
	rr, err = svc.rechargeGetRecord(billUUID)
	if err != nil {
		svc.log.WithField("billUUID", billUUID).WithError(err).Error("get recharge record failed.")
		return
	}

	// 加锁, 每条记录一个锁
	lock := new(redis_plugin.RedisMutex)
	lock, err = svc.setLockForUpdateRechargeRecord(billUUID)
	if err != nil {
		return "", err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	status = string(tradeStatus)
	switch tradeStatus {
	case alipay.TradeStatusSuccess:
		if rr.CallbackAt != nil {
			return
		}

		// 确认金额
		if amount.TotalAmount != rr.Assets {
			svc.log.WithFields(map[string]interface{}{
				"bill_uuid":     billUUID,
				"bill_asset":    rr.Assets,
				"wx_pay_amount": amount.TotalAmount,
			}).Error("notify payer amount != local asset recorded")
			err = businesserror.ErrWalletParseAliNotifyError
			return
		}

		rr.PayAccount = aliPayAccount
		rr.Balance = rr.Assets

		err = svc.rechargeSuccessConfirmRechargeRecord(rr)
		if err != nil {
			return
		}

	case alipay.TradeStatusWaitBuyerPay:
	case alipay.TradeStatusClosed:
	case alipay.TradeStatusFinished:
	}
	return
}

// 定时更新二维码
func (svc *BCService) rechargeAliUpdateQrCode(billUUID string) (qrUrl string, err error) {
	aliUrl := ""
	aliUrl, err = svc.aliRechargeTimeout.Get(billUUID)
	if err != nil {
		if err == redis.Nil {
			err = businesserror.ErrWalletWaitPayTimeout
			return
		}
		svc.log.WithError(err).Error("redis: get ali recharge record failed")
		err = businesserror.ErrInternalError
		return
	}

	qrUrl, err = svc.rechargeParseAliUrl(aliUrl)
	if err != nil {
		svc.log.WithError(err).Error("rechargeParseAliUrl parse failed")
		return
	}
	return
}

func (svc *BCService) GetUserRechargeRecordSum(uid int, dateFrom time.Time, dateTo time.Time) (amount int64, err error) {
	var userAmount int64
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).Where("uid = ?", uid).Where("pathway in (?)", []constant.RechargePathway{constant.Alipay, constant.WeChatPay, constant.WechatAppletPay}).
		Select("IFNULL(abs(sum(recharge_record.payg_price)), 0)").Where("callback_at >= ?", dateFrom).
		Where("callback_at < ?", dateTo).Find(&userAmount).Error
	if err != nil {
		svc.log.WithFields(logger.Fields{
			"uid":       uid,
			"date_from": dateFrom,
			"date_to":   dateTo,
		}).Error("get user recharge_record failed")
		return
	}
	var adminAmount int64
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).Where("uid = ?", uid).Where("pathway in (?)", []constant.RechargePathway{constant.AdminPay, constant.CreditWalletPay}).
		Select("IFNULL(abs(sum(recharge_record.payg_price)), 0)").Where("created_at >= ?", dateFrom).
		Where("created_at < ?", dateTo).Find(&adminAmount).Error
	if err != nil {
		svc.log.WithFields(logger.Fields{
			"uid":       uid,
			"date_from": dateFrom,
			"date_to":   dateTo,
		}).Error("get user recharge_record failed")
		return
	}
	amount = userAmount + adminAmount
	return
}

func (svc *BCService) rechargeRecordCreate(tx *gorm.DB, rr *model.RechargeRecord) (err error) {
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.RechargeRecord{},
		InsertPayload:           rr,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("rr", rr).Error("create rechargeRecord failed")
		return
	}
	return
}

func (svc *BCService) GetUserRechargeAmount(uid int) (amount int64, err error) {
	var userAmount int64
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).
		Select("IFNULL(abs(sum(recharge_record.payg_price)), 0)").
		Where("uid = ?", uid).
		Where("callback_at is not null").
		Where("pathway in (?)", []constant.RechargePathway{constant.Alipay, constant.WeChatPay, constant.WechatAppletPay, constant.BankTransfer}).
		Where("pre_recharge != ?", true).
		Find(&userAmount).Error
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharge_record failed")
		return
	}
	var otherAmount int64
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).Where("uid = ?", uid).Where("pathway in (?)", []constant.RechargePathway{constant.AdminPay, constant.CreditWalletPay}).
		Select("IFNULL(abs(sum(recharge_record.payg_price)), 0)").Find(&otherAmount).Error
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharge_record failed")
		return
	}
	amount = userAmount + otherAmount
	return
}

func (svc *BCService) GetRechargeRecordCountByUserId(uid int) (count int64, err error) {
	err = db_helper.GlobalDBConn().Table(model.TableNameRechargeRecord).
		Where("uid = ?", uid).
		Where("callback_at is not null").
		Count(&count).Error
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharge_record failed")
		return count, err
	}
	return count, nil

}

func (svc *BCService) rechargeRecordCreateByBill(tx *gorm.DB, bill *model.Bill) (err error) {
	if bill == nil {
		return businesserror.ErrInternalError
	}
	now := time.Now()
	rr := &model.RechargeRecord{
		UID:        bill.UID,
		UserPhone:  bill.UserPhone,
		BillUUID:   bill.UUID,
		Pathway:    bill.SubType.ToRechargePathway(),
		Assets:     bill.Asset,
		CallbackAt: &now,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         &model.RechargeRecord{},
		InsertPayload:           rr,
	}).GetError()
	if err != nil {
		log.WithError(err).WithField("bill_uuid", bill.UUID).Error("admin recharge create rechargeRecord failed")
		return
	}

	return
}

func (svc *BCService) rechargeForBankTransfer(bt *model.BankTransfer, adminOperate bool, btUM map[string]interface{}) (err error) {
	now := time.Now()

	l := svc.log.WithFields(map[string]interface{}{
		"uid":     bt.ConfirmedUID,
		"section": "rechargeForBankTransfer",
	})

	// 创建账单
	afterHook := &queue_interface.NewQueueForUserRecharge{
		UserId:    bt.ConfirmedUID,
		UserPhone: bt.ConfirmedPhone,
		Asset:     bt.Amount,
		SubType:   queue_interface.AfterUserRecharge,
	}

	bill := &model.Bill{
		UID:          bt.ConfirmedUID,
		UUID:         libs.RandNumberString(),
		UserPhone:    bt.ConfirmedPhone,
		Type:         constant.BillTypeRecharge,
		SubType:      constant.BillSubTypeRechargeBankTransfer,
		Asset:        bt.Amount,
		PayByBalance: bt.Amount,
		ConfirmAt:    &now,
		DetailsEntity: &model.BillDetail{
			RechargePathway:          constant.BankTransfer,
			BankTransferSerialNumber: bt.SerialNum,
		},
		DiscountEntity: &model.DiscountDetail{
			OriginPrice: bt.Amount,
			DiscountSum: 0,
		},
	}

	rr := &model.RechargeRecord{
		UID:        bt.ConfirmedUID,
		UserPhone:  bt.ConfirmedPhone,
		BillUUID:   bill.UUID,
		Pathway:    constant.BankTransfer,
		Assets:     bt.Amount,
		CallbackAt: &now,
	}

	// 加锁, 每个用户一个锁, 加了代金券后， 也使用同一个锁
	lock := new(adlredis.RedisMutex)
	lock, err = svc.setLockForUpdateWallet(bill.UID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()

	msgUUID := []string{}
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		// 更新wallet
		err = svc.updateWallet(tx, bill)
		if err != nil {
			svc.log.WithError(err).WithField("bill", bill).Error("update wallet failed")
			return err
		}

		err = svc.createBill(tx, bill)
		if err != nil {
			l.ErrorE(err, "create bill failed")
			return err
		}

		err = svc.rechargeRecordCreate(tx, rr)
		if err != nil {
			l.ErrorE(err, "create recharge recode failed")
			return err
		}

		filter := map[string]interface{}{"serial_num": bt.SerialNum}
		if adminOperate {
			filter["status"] = constant.BTUnconfirmed
		}

		rows, err := bt.BTUpdateAffectRows(tx, &db_helper.QueryFilters{EqualFilters: filter}, btUM)
		if err != nil {
			l.ErrorE(err, "update bank transfer recharged flag to true failed")
			return err
		}
		if rows == 0 {
			return errors.New("affect 0 rows")
		}

		// 发送hook
		msgUUID, err = svc.afterUpdateWallet(tx, &model.AfterUpdateWalletParams{
			Bill:             bill,
			AfterOperateType: constant.UpdateWalletSkip,
			AfterHook:        afterHook,
		})
		if err != nil {
			l.ErrorE(err, "after update wallet: pub hook failed")
			return err
		}
		return nil
	})

	if len(msgUUID) != 0 {
		errMsg := svc.q.Pub(msgUUID...)
		if len(errMsg) != 0 {
			svc.log.Warn("Pub tx msg failed [%s]. msg_uuid_list: %+v", errMsg, msgUUID)
		}
	}

	if bt.ConfirmedPhone != "" {
		if conf.GetGlobalGsConfig().App.DebugApi {
			if bt.ConfirmedPhone == "***********" || bt.ConfirmedPhone == "***********" || bt.ConfirmedPhone == "***********" {
				_, area, phone, _, ok := svc.user.GetPhoneForSmsNotify(bt.ConfirmedUID, "")
				if !ok {
					return nil
				}
				// 发送短信通知
				input := notify.SmsNotifyInput{
					PhoneArea: area,
					Phone:     libs.PhoneBuildWithArea(area, phone),
					SMSType:   constant.BankRemitAccept,
				}

				_, err = svc.smsNotifyChannel.Notify(input)
				if err != nil {
					svc.log.WithField("phone", bt.ConfirmedPhone).ErrorE(err, "notify bank_remit_accept failed")
					return err
				}
			}
		} else {
			// 发送短信通知
			_, area, phone, _, ok := svc.user.GetPhoneForSmsNotify(bt.ConfirmedUID, "")
			if !ok {
				return nil
			}
			input := notify.SmsNotifyInput{
				PhoneArea: area,
				Phone:     libs.PhoneBuildWithArea(area, phone),
				SMSType:   constant.BankRemitAccept,
			}

			_, err = svc.smsNotifyChannel.Notify(input)
			if err != nil {
				svc.log.WithField("phone", bt.ConfirmedPhone).ErrorE(err, "notify bank_remit_accept failed")
				return err
			}
		}
	}

	return err
}

func (svc *BCService) beforeRechargeCheck(uid int) (isPreRecharge bool, err error) {
	// count instance 数量
	countInstance, err := svc.instance.CountInstance(uid)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "count user instance num failed")
		return
	}

	// get user
	user, err := svc.user.FindByUserId(uid)
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "get user failed")
		return
	}

	// count recharge record 数量
	rechargeRecord := &model.RechargeRecord{}
	countRecharge, err := rechargeRecord.RRCount(&db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"uid": uid,
		},
		NotNullField: []string{"callback_at"},
	})
	if err != nil {
		svc.log.WithField("uid", uid).ErrorE(err, "count recharge record by uid failed")
		return
	}

	if countInstance == 0 && user.OpenId == "" && !user.RealNameAuth && countRecharge >= 3 {
		return true, nil
	}

	return false, nil
}
