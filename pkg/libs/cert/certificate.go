package cert

import (
	_ "embed"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
)

/**
 * 解决 storage agent api 跨域问题.
 * cert 全部与 cmd/server/deploy/nginx/cert.key 相同.
 */

//go:embed cert.key
var keyContent string

//go:embed cert.pem
var pemContent string

func CopyCertToPath(certDirPath string) (keyPath, pemPath string, err error) {
	// save key
	keyPath = filepath.Join(certDirPath, "cert.key")

	err = writeSimpleFileToPath(keyContent, keyPath)
	if err != nil {
		return
	}

	// save pem
	pemPath = filepath.Join(certDirPath, "cert.pem")

	err = writeSimpleFileToPath(pemContent, pemPath)
	if err != nil {
		return
	}
	return
}

func writeSimpleFileToPath(fileContent string, absPath string) error {
	err := os.MkdirAll(filepath.Dir(absPath), os.<PERSON>Perm)
	if err != nil {
		return errors.Wrapf(err, "create directory path [%s] failed when write container init files", absPath)
	}

	f, err := os.OpenFile(absPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = f.Write([]byte(fileContent))
	if err != nil {
		return err
	}
	return nil
}
