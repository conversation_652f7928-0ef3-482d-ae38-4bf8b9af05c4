package libs

import (
	"fmt"
	"server/conf"
	"server/pkg/constant"
	"strings"
	"time"
)

func GenContainerRuntimeUUID(productUUID string) constant.ContainerRuntimeUUID {
	result := fmt.Sprintf("%s-%s-%s", conf.GetGlobalGsConfig().App.Platform, constant.RuntimeUUIDPrefix, productUUID)
	return constant.NewContainerRuntimeUUID(result)
}

func GenProductUUIDFromRuntimeUUID(runtimeUUID constant.ContainerRuntimeUUID) constant.InstanceUUIDType {
	// 默认传进来的runtimeUUID是正确的
	s := strings.TrimPrefix(runtimeUUID.String(), "-"+constant.RuntimeUUIDPrefix+"-")
	s = strings.TrimPrefix(s, constant.RuntimeUUIDPrefix+"-")
	s = strings.TrimPrefix(s, conf.GetGlobalGsConfig().App.Platform+"-"+constant.RuntimeUUIDPrefix+"-")
	return constant.NewInstanceUUIDType(s)
}

// GenInstanceUUID
/*
 * http://jira.seetatech.com/browse/AU-4
 * 一方面为了用户可以从实例编号看出主机编号，方便购买在相同主机上的实例，其次为了美观。
 * 主机编号使用：8位字母+数字
 * 实例编号：{主机编号}-{4位字母+数字}，拼接而成
 */
func GenInstanceUUID(machineID string) constant.InstanceUUIDType {
	instanceSuffix := GenRandomStrByUUID(8)
	result := fmt.Sprintf("%s-%s", machineID, instanceSuffix)
	return constant.InstanceUUIDType(result)
}

func GenDeploymentContainerUUID(deployment, machineID string) string {
	instanceSuffix := GenRandomStrByUUID(10)
	return fmt.Sprintf("%s-%s-%s", deployment, machineID, instanceSuffix)
}

func GenDeploymentContainerUUIDFromDCC(dcUUID string) string {
	dccSuffix := GenRandomStrByUUID(9)
	return fmt.Sprintf("%s-%s", dcUUID, dccSuffix)
}

func ParseDeploymentContainerCacheUUID(uuid string) (string, bool) {
	fields := strings.Split(uuid, "-")
	if len(fields) == 3 {
		// dc
		return uuid, false
	} else if len(fields) == 4 {
		// ddc
		return strings.TrimSuffix(uuid, "-"+fields[3]), true
	}

	// error
	return "", false
}

func ParseDeploymentUUIDFromDCUUID(dcUUID string) string {
	fields := strings.Split(dcUUID, "-")
	if len(fields) != 3 && len(fields) != 4 {
		return ""
	}
	return fields[0]
}

func ParseDeploymentUUIDGetMachineID(dcUUID string) string {
	fields := strings.Split(dcUUID, "-")
	if len(fields) != 3 && len(fields) != 4 {
		return ""
	}
	return fields[1]
}

func GenIdleJobUUID(something string) string {
	// TODO: GenIdleJobUUID in libs
	return "TODO"
}

// GenAlipayFaceCertifyUUID
// 用作人脸验证初始化请求参数的 outer_order_no
// AutoDL+时间(xxxx年xx月xx日xx时xx分xx秒)+12位数字
func GenAlipayFaceCertifyUUID() string {
	// 获取当前时间
	currentTime := time.Now().Format("20060102150405") // 格式化为数字：年月日时分秒

	// 生成一个12位的随机数字
	suffix := GenRandomStrByUUID(12)

	// 组合生成的唯一ID
	uniqueID := fmt.Sprintf("AutoDL%s%s", currentTime, suffix)

	return uniqueID
}
