package libs

func NewIntSet() intSet {
	return make(intSet)
}

func NewStringSet() stringSet {
	return make(stringSet)
}

type intSet map[int]struct{}

func (s intSet) Append(in ...int) {
	for k := range in {
		s[in[k]] = struct{}{}
	}
}

func (s intSet) ToSlice() []int {
	out := make([]int, 0)
	for k, _ := range s {
		out = append(out, k)
	}
	return out
}

func (s intSet) IsEmpty() bool {
	return len(s) == 0
}

type stringSet map[string]struct{}

func (s stringSet) Append(in ...string) {
	for k := range in {
		s[in[k]] = struct{}{}
	}
}

func (s stringSet) ToSlice() []string {
	out := make([]string, 0)
	for k, _ := range s {
		out = append(out, k)
	}
	return out
}

func (s stringSet) IsEmpty() bool {
	return len(s) == 0
}
