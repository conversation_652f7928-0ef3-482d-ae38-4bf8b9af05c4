package libs

import "testing"

func TestSafeFilePath<PERSON><PERSON>n(t *testing.T) {
	type PathTest struct {
		dir, path, result string
	}

	var cleantests = []PathTest{
		// Already clean
		{"/root/", "abc", "/root/abc"},
		{"/root/", "abc/def", "/root/abc/def"},
		{"/root/", "a/b/c", "/root/a/b/c"},
		{"/root/", ".", "/root"},
		{"/root/", "..", "/root"},
		{"/root/", "../..", "/root"},
		{"/root/", "../../abc", "/root/abc"},
		{"/root/", "/abc", "/root/abc"},
		{"/root/", "/", "/root"},

		// Remove trailing slash
		{"/root/", "abc/", "/root/abc"},
		{"/root/", "abc/def/", "/root/abc/def"},
		{"/root/", "a/b/c/", "/root/a/b/c"},
		{"/root/", "./", "/root"},
		{"/root/", "../", "/root"},
		{"/root/", "../../", "/root"},
		{"/root/", "/abc/", "/root/abc"},

		// Remove doubled slash
		{"/root/", "abc//def//ghi", "/root/abc/def/ghi"},
		{"/root/", "//abc", "/root/abc"},
		{"/root/", "///abc", "/root/abc"},
		{"/root/", "//abc//", "/root/abc"},
		{"/root/", "abc//", "/root/abc"},

		// Remove . elements
		{"/root/", "abc/./def", "/root/abc/def"},
		{"/root/", "/./abc/def", "/root/abc/def"},
		{"/root/", "abc/.", "/root/abc"},

		// Remove .. elements
		{"/root/", "abc/def/ghi/../jkl", "/root/abc/def/jkl"},
		{"/root/", "abc/def/../ghi/../jkl", "/root/abc/jkl"},
		{"/root/", "abc/def/..", "/root/abc"},
		{"/root/", "abc/def/../..", "/root"},
		{"/root/", "/abc/def/../..", "/root"},
		{"/root/", "abc/def/../../..", "/root"},
		{"/root/", "/abc/def/../../..", "/root"},
		{"/root/", "abc/def/../../../ghi/jkl/../../../mno", "/root/mno"},
		{"/root/", "/../abc", "/root/abc"},

		// Combinations
		{"/root/", "abc/./../def", "/root/def"},
		{"/root/", "abc//./../def", "/root/def"},
		{"/root/", "abc/../../././../def", "/root/def"},
	}

	for _, v := range cleantests {
		a := SafeFilePathJoin(v.dir, v.path)
		if a != v.result {
			t.Error("failed path join " + v.path)
		}
	}

}
