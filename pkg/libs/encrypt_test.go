package libs

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"testing"
)

var list = []string{
	"",
	"a",
	"bc",
	"de",
	"f",
	"fo",
	"foo",
	"foob",
	"fooba",
	"foobar",
	"autodl",
	"autodl is very good",
	"fmt.Println(Decode(Encode(v)) == v)",
	"121.4b37b0186f14a127ee3e963afcc82c46.YDWJIkH9CQCEEzjvdq9Rw6LZn72YsDgEWl53Htx.N-LfWw",
	`{
 "app": {
  "debug_api": true
 },
 "fs_list": [
  {
   "fs_id": "b2001",
   "type": "AutoDL_BaiduNetDisk",
   "access_token": "121.bcc644e41a0cec0fc719d49ba619a7f6.YH9ON2LFLFMyeG3fgPntA7gMkou3mLyozWflyk8.R7qV2Q",
   "access_token_expire_at": "2022-03-12T00:00:00Z",
   "refresh_token": ""
  },
  {
   "fs_id": "a1001",
   "type": "AutoDL_AliNetDisk",
   "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VtbV27VbvqQbd63kh5QHs51FhuLY1InBtGuNUj8aY6Uwr7iDNuGNMK6V34eaeFigs9BRlmXa8cvYU8U5Zr1CcLoYcxGiAgiW99XXXTbsOJ9JWV5mLAEmpE98Kit2ccCzuiaaidBkAxGJnAlxs0r3Ln7vn-v-kVzVIADzdnDYNrE",
   "access_token_expire_at": "2022-03-12T00:00:00Z",
   "refresh_token": "b652d9f5396944e5bf8a046756f61923"
  }
 ]
}
`,
}

func TestEncodeDecode(t *testing.T) {
	for _, v := range list {
		fmt.Println("-------------------")
		fmt.Println(v)
		ev, err := Encode(v)
		if err != nil {
			t.Fatalf(err.Error())
		}
		dv, err := Decode(ev)
		if err != nil {
			t.Fatalf(err.Error())
		}
		fmt.Println(ev)
		fmt.Println(dv)
		fmt.Println(dv == v)
		fmt.Println("-------------------")
	}
}

func TestDESEncodeDecode(t *testing.T) {
	key := "ningfd123456"

	for _, v := range list {
		fmt.Println("-------------------")
		ev := DesEncode(key, []byte(v))
		dv := DesDecode(key, ev)
		fmt.Println(ev)
		fmt.Println(dv)
		fmt.Println(string(dv) == v)
		fmt.Println("-------------------")
	}
}

func TestAESEncodeDecode(t *testing.T) {
	key := "ningfd123456"

	for _, v := range list {
		fmt.Println("-------------------")
		ev := AesEncode(key, []byte(v))
		dv := AesDecode(key, ev)
		fmt.Println(ev)
		fmt.Println(dv)
		fmt.Println(string(dv) == v)
		fmt.Println("-------------------")
	}
}

func TestConfoundAesDecode(t *testing.T) {
	code := "PjhpZJWk1XpsZFdrKCntwgFY1ve0cNuPX+cxuQ=="
	str := "123456abc"
	key := code[:16]

	sha1Key := sha1.Sum([]byte(key))

	var AESKey []byte
	AESKey = append(AESKey, sha1Key[:]...)
	AESKey = append(AESKey, sha1Key[:12]...)
	iv := AESKey[:16]
	fmt.Printf("iv:%v\n", iv)

	blk, err := aes.NewCipher(AESKey)
	if err != nil {
		panic(fmt.Sprintf("failed to get DES blk of %s, len: %d, %s", key, len(key), err.Error()))
	}
	decodeBytes, err := base64.StdEncoding.DecodeString(code[16:])

	if err != nil {
		fmt.Println("Base64 decoding error:", err)
		return
	}
	fmt.Printf("decodeBytes:%v\n", decodeBytes)

	buff := make([]byte, len(decodeBytes))

	cryped := cipher.NewCBCDecrypter(blk, iv)
	cryped.CryptBlocks(buff, decodeBytes)
	//decrypted := PKCS5Padding(buff)
	//decrypted := ZeroUnPadding(buff)
	decrypted := PKCS7UnPadding(buff)

	fmt.Println(string(decrypted) == str)
	fmt.Printf("decrypted:%s", decrypted)
}

func TestConfoundAesEncode(t *testing.T) { // KCntwgFY1ve0cNuPX+cxuQ==
	key := "PjhpZJWk1XpsZFdr"
	str := "123456abc"

	sha1Key := sha1.Sum([]byte(key))

	var AESKey []byte
	AESKey = append(AESKey, sha1Key[:]...)
	AESKey = append(AESKey, sha1Key[:12]...)

	blk, err := aes.NewCipher(AESKey)
	if err != nil {
		panic(fmt.Sprintf("failed to get DES blk of %s, len: %d, %s", key, len(key), err.Error()))
	}

	iv := AESKey[:16]
	fmt.Printf("iv:%v\n", iv)
	encodeBytes := PKCS7Padding([]byte(str), blk.BlockSize())

	buff := make([]byte, len(encodeBytes))

	cryped := cipher.NewCBCEncrypter(blk, iv)
	cryped.CryptBlocks(buff, encodeBytes)

	encodeToString := base64.StdEncoding.EncodeToString(buff)
	s := string(buff)
	fmt.Printf("encodeToString:%s\n", encodeToString)
	fmt.Printf("s:%s\n", s)
}

type user struct {
	Phone     string `json:"phone"`
	Password  string `json:"password"`
	VCode     string `json:"v_code"`
	PhoneArea string `json:"phone_area"`
}

type pay struct {
	Asset int `json:"asset"`
}

func TestConfound(t *testing.T) {
	//req := user{
	//	Phone:     "16666666666",
	//	Password:  "admin123",
	//	VCode:     "666666",
	//	PhoneArea: "+86",
	//}

	req := pay{Asset: 5000}

	typ := reflect.TypeOf(req)
	val := reflect.ValueOf(req)

	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
		val = val.Elem()
	}

	var fields []string
	result := make(map[string]string)
	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		jsonTag := field.Tag.Get("json")

		if field.Name != "Passcode" {
			fields = append(fields, field.Name)
			result[field.Name] = jsonTag
		}
	}
	fmt.Println(result)
	fmt.Println(fields)

	sort.Strings(fields)
	var concatenatedFields []string
	for _, fieldName := range fields {
		jsonTag := result[fieldName]
		fieldValue := fmt.Sprintf("%v", val.FieldByName(fieldName).Interface())
		concatenatedFields = append(concatenatedFields, fmt.Sprintf("%s=%s", jsonTag, fieldValue))
	}

	result2 := strings.Join(concatenatedFields, "&")

	fmt.Printf("result2:%s\n", result2)

	sha1Key := sha1.Sum([]byte(result2))
	sha1String := hex.EncodeToString(sha1Key[:])
	fmt.Printf("sha1String:%s\n", sha1String)

	md5Hash := md5.New()
	md5Hash.Write([]byte(sha1String))
	md5Sum := md5Hash.Sum(nil)

	md5String := hex.EncodeToString(md5Sum)

	fmt.Println(md5String)
}

func TestAutoDLAesDecode(t *testing.T) {
	t.Log(string(AutoDLAesDecode([]byte("/UWDN+-iZHXHqi/b-X/xAKlBhy3s8nEu9/jpQ/T5UkJSGRXoE5w6fV3iBF6LjK572RfFXwFEi6n9+qtKU6e599aLGg2FkzqQ0Z6LQ6Aa4PUCZqt6Ugnx1yU77gAaVJT+o5X60tO3yTlI80*/zRYrEa"))))
}
