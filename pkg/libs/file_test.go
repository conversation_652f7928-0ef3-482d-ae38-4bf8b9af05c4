package libs

import (
	"context"
	"testing"
	"time"
)

func TestLsDirWithHead(t *testing.T) {
	limit := 5
	files, err := LsDirWithHead("/tmp", limit)
	if err != nil {
		t.<PERSON>rror(err)
	}
	if limit > 0 && len(files) > limit {
		t.FailNow()
	}
}

func TestExistPathWithCtx(t *testing.T) {
	startedAt := time.Now()

	// timeout: 800ms
	ctxPath, cancel := context.WithTimeout(context.Background(), 800*time.Millisecond)
	defer cancel()

	e := existPathWithCtx(ctxPath, "/tmp", func(s string) bool {
		time.Sleep(5 * time.Second)
		return true
	})
	if e {
		t.FailNow()
	}

	finishedAt := time.Now()
	if finishedAt.Sub(startedAt) > 1*time.Second {
		t.FailNow()
	}
}

func TestName(t *testing.T) {
	begin, err := time.ParseInLocation("20060102150405", "20231218191800", time.Local)
	if err != nil {
		t.Fatal(err)
	}
	end := time.Now().Local()
	//end, _ := time.Parse("20060102150405", "20231218201800")

	t.Log(begin, end)

	m := end.Sub(begin).Minutes()
	t.Log(m)

	t.Log(int64(m))
}
