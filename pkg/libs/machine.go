package libs

import "server/pkg/constant"

func GetMachineGpuOrderNum(oldGpuOrderNum, bindingInstanceNum, MaxInstanceNum int64, online constant.OnOffLine) (orderNum int64) {
	orderNum = oldGpuOrderNum
	if bindingInstanceNum >= MaxInstanceNum {
		orderNum = 0
	} else {
		// 上架但不可开机或不可创建实例时，设置gpuOrderNum=0， 用于排序过滤
		if online == constant.OnlineNotCreate || online == constant.OnlineNotStartUp ||
			online == constant.OnlineNotCreateButSchedule || online == constant.OnlineNotStartUpButSchedule {
			orderNum = 0
		}
	}

	return
}
