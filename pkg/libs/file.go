package libs

import (
	"bytes"
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/zRedShift/mimemagic"
	"io"
	mathRand "math/rand"
	"mime/multipart"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"server/pkg/logger"
	"strings"
	"time"
)

var (
	textType = map[string]int{
		".py": 0, ".cc": 1, ".cpp": 2, ".c": 3, ".go": 4, ".java": 5, ".yaml": 6, ".md": 7, ".txt": 8, ".yml": 9, ".h": 10,
		".json": 11, ".php": 12,
	}
	imageType = map[string]int{".bmp": 0, ".jpg": 1, ".jpeg": 2, ".png": 3, ".gif": 4}

	junkFiles = []string{
		//"/root/.cache",
		"/root/.local/share/jupyter",
		"/root/miniconda3/pkgs",
		"/root/.local/share/Trash",
	}
)

const (
	// 到时候如果有其他类型的再添加于此
	DirUserFeedBackScreenShot = "user_feedback"
)

// FileType 具体文件类型, 如 .jpg 等.
type FileType string

type PathInfo struct {
	PurposeID *int     `json:"purpose_id"`
	Name      string   `json:"name"`
	IsDir     bool     `json:"is_dir"`
	Size      int64    `json:"size"`
	Path      string   `json:"path"`
	FileType  FileType `json:"file_type"`
}

// ObjectType 实体/上传文件 的类型, 写到一起
type ObjectType string

const (
	ScreenShotObject ObjectType = "screen_shot" // 上传问题截图, 不使用异步文件传输
	ImageFile        FileType   = "image"       // 图片文件
)

// --------------------------------------------- 三方登录 ----------------------------------------

// 用户上传的文件内容是否为 mime 的图片类型
func IsMimeImageFromUpload(file *multipart.FileHeader) (ok bool, err error) {
	f, err := file.Open()
	if err != nil {
		err = errors.Wrap(err, "open file failed")
		return
	}
	defer f.Close()

	b, err := io.ReadAll(f)
	if err != nil {
		err = errors.Wrap(err, "read file failed")
		return
	}

	ok = IsMimeImage(b)
	return
}

// 用户检查图像是否为 mime 的图片类型
func IsMimeImage(data []byte) bool {
	mime := mimemagic.MatchMagic(data)
	regex := regexp.MustCompile("^image.*")
	return regex.MatchString(mime.MediaType())
}

func Ext(path string) string {
	if strings.HasSuffix(path, ".tar.gz") {
		return ".tar.gz"
	}
	return filepath.Ext(path)
}

func UploadFile(file *multipart.FileHeader, uploadPath string) error {
	reader, err := file.Open()
	if err != nil {
		logger.NewLogger("uploadFile").ErrorE(err, "read upload chunk file failed")
		return err
	}
	defer reader.Close()
	return UploadFileFromReader(reader, uploadPath)
}

func UploadFileFromReader(file io.Reader, uploadPath string) error {
	err := os.MkdirAll(filepath.Dir(uploadPath), os.ModePerm)
	if err != nil {
		return err
	}
	writer, err := os.OpenFile(uploadPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, os.ModePerm)
	if err != nil {
		logger.NewLogger("uploadFile").ErrorE(err, "open upload path failed: ")
		return err
	}
	defer writer.Close()

	if _, err = io.Copy(writer, file); err != nil {
		logger.NewLogger("uploadFile").ErrorE(err, "write upload chunk file failed: ")
		return err
	}

	return nil
}

// TODO: 需要添加超时控制 (<1s), 当nfs不可用时, 此方法会等待知道nfs恢复正常, 容易引起故障
// ExistPath 严格检查存在, 是否可用
func ExistPath(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			logger.NewLogger("libs.ExistPath").WarnE(err, "check path '%s' available failed, consider false", path)
		}
		return false
	}
	return true
}

// ExistPathWithCtx 加入超时
func ExistPathWithCtx(ctx context.Context, path string) bool {
	ctxPath, cancel := context.WithTimeout(ctx, 800*time.Millisecond)
	defer cancel()
	return existPathWithCtx(ctxPath, path, func(s string) bool {
		return ExistPath(s)
	})
}

/*
TODO: fix stat timeout -> data race
ga_1  | WARN[2022-03-07T16:56:46+08:00] Check path '/storage/nas/migrate/diff-container-2151119da0-8355d1bb.tar' exist failed by timeout: 800ms  hostname=agent-172-181-217-72 line="server/pkg/libs.existPathWithCtx(): 151" sec=ExistPathWithCtx
ga_1  | INFO[2022-03-07T16:56:46+08:00] ContainerChecker is running...                hostname=agent-172-181-217-72 sec=Guard.runContainerChecker
ga_1  | INFO[2022-03-07T16:56:46+08:00] UsageSender is running...                     hostname=agent-172-181-217-72 sec=Guard.runUsageSender
ga_1  | INFO[2022-03-07T16:56:46+08:00] UsageChecker is running...                    hostname=agent-172-181-217-72 sec=Guard.runUsageChecker
ga_1  | INFO[2022-03-07T16:56:46+08:00] UsageSender is running...                     hostname=agent-172-181-217-72 sec=Guard.runUsageSender
ga_1  | WARN[2022-03-07T16:56:47+08:00] check path '/storage/nas/migrate/diff-container-2151119da0-8355d1bb.tar' available failed, consider false  err="stat /storage/nas/migrate/diff-container-2151119da0-8355d1bb.tar: no such file or directory" hostname=agent-172-181-217-72 line="server/pkg/libs.ExistPath(): 123" sec=libs.ExistPath
ga_1  | ==================
ga_1  | WARNING: DATA RACE
ga_1  | Read at 0x00c000037120 by goroutine 87:
ga_1  |   runtime.chansend()
ga_1  |       /usr/local/go/src/runtime/chan.go:158 +0x0
ga_1  |   server/pkg/libs.existPathWithCtx.func1()
ga_1  |       /nfd/poyehali/server/pkg/libs/file.go:144 +0x6f
ga_1  |
ga_1  | Previous write at 0x00c000037120 by goroutine 81:
ga_1  |   runtime.closechan()
ga_1  |       /usr/local/go/src/runtime/chan.go:355 +0x0
ga_1  |   server/pkg/libs.existPathWithCtx·dwrap·4()
ga_1  |       /nfd/poyehali/server/pkg/libs/file.go:140 +0x39
ga_1  |   server/pkg/libs.existPathWithCtx()
ga_1  |       /nfd/poyehali/server/pkg/libs/file.go:152 +0x341
ga_1  |   server/pkg/libs.ExistPathWithCtx()
ga_1  |       /nfd/poyehali/server/pkg/libs/file.go:133 +0xc8
ga_1  |   server/pkg-agent/agent_guard.(*Guard).uploadDiffHandler()
ga_1  |       /nfd/poyehali/server/pkg-agent/agent_guard/handler_diff.go:98 +0x864
ga_1  |   server/pkg-agent/agent_guard.(*Guard).uploadDiffHandler-fm()
*/
func existPathWithCtx(ctx context.Context, path string, existFunc func(string) bool) bool {
	var resultChan = make(chan bool, 1)

	go func() {
		defer close(resultChan)
		result := existFunc(path)
		resultChan <- result
	}()

	select {
	case r := <-resultChan:
		return r
	case <-ctx.Done():
		logger.NewLogger("ExistPathWithCtx").Warn("Check path '%s' exist failed by timeout: 800ms", path)
		return false
	}
}

// LsDirWithHead : ls -alt | head -n 300
func LsDirWithHead(dirPath string, head int) (list []os.FileInfo, fErr error) {
	defer func() {
		if errors.Is(fErr, io.EOF) {
			fErr = nil
		} else if os.IsNotExist(fErr) {
			fErr = nil
		}
	}()

	f, fErr := os.Open(dirPath)
	if fErr != nil {
		return nil, fErr
	}
	defer f.Close()

	list, fErr = f.Readdir(head)
	if fErr != nil {
		return nil, fErr
	}
	return list, nil
}

func RemoveAllUnderTheDir(dirPath string) error {
	dir, err := os.ReadDir(dirPath)
	if err != nil {
		return err
	}

	for _, d := range dir {
		_ = os.RemoveAll(SafeFilePathJoin(dirPath, d.Name()))
	}

	return nil
}

func CompressTarToPath(ctx context.Context, src, dst string) (err error) {
	l := logger.NewLogger("CompressTarToPath")
	devnull, err := os.OpenFile(os.DevNull, os.O_WRONLY, 0755)
	if err != nil {
		devnull = os.Stdout
		l.WarnE(err, "can not open null device ")
	}

	err = os.MkdirAll(filepath.Dir(src), os.ModePerm)
	if err != nil {
		l.WarnE(err, "os mkdir failed")
		return
	}

	// 去除垃圾文件
	excludeStr := ""
	for _, v := range junkFiles {
		excludeStr = excludeStr + " --exclude=" + v
	}

	cmdStr := fmt.Sprintf("tar %s -cvf %s -C %s .", excludeStr, dst, src)
	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stdout = devnull
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		err = errors.Wrap(err, stderr.String())
		l.WithError(err).Error("cmd run tar compress failed")
		return err
	}
	return
}

func DecompressTarToPath(ctx context.Context, src, dst string) (err error) {
	l := logger.NewLogger("DecompressTarToPath")
	devnull, err := os.OpenFile(os.DevNull, os.O_WRONLY, 0755)
	if err != nil {
		devnull = os.Stdout
		l.WarnE(err, "can not open null device ")
	}

	err = RemoveAllUnderTheDir(dst)
	if err != nil {
		l.WarnE(err, "remove all dir failed")
		return
	}
	cmdStr := fmt.Sprintf("tar -xvf %s -C %s", src, dst)
	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", cmdStr)
	var stderr bytes.Buffer
	cmd.Stdout = devnull
	cmd.Stderr = &stderr
	err = cmd.Run()
	if err != nil {
		err = errors.Wrap(err, stderr.String())
		l.WithError(err).Error("cmd run tar deCompress failed")
		return err
	}
	return
}

// --------------------- upload file --------------

var allowExt = []string{".jpg", ".JPG", ".png", ".PNG", ".gif", ".GIF", ".jpeg", ".JPEG", ".tiff", ".TIFF", ".pdf", ".PDF", ".doc", ".docx", ".DOC", ".DOCX"}
var allowFileType = []string{"image/jpg", "image/png", "image/gif", "image/jpeg", "image/tiff", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}

func AllowExt(ext string) bool {
	for _, v := range allowExt {
		if v == ext {
			return true
		}
	}
	return false
}
func AllowFileType(fileType string) bool {
	for _, v := range allowFileType {
		if v == fileType {
			return true
		}
	}
	return false
}

func randUUID(l int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	bytes := []byte(str)
	var result []byte
	r := mathRand.New(mathRand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

func RandFileUUID() string {
	return randUUID(20)
}

func GenUserFileName(uid int) string {
	return fmt.Sprintf("%d-%d-%s", uid, time.Now().Nanosecond(), RandFileUUID())
}

// clearFolder 删除文件夹下所有内容, 保留文件夹
// 如果 path 是文件, 不会删除
func clearFolder(path string) (err error) {
	files, err := filepath.Glob(filepath.Join(path, "*"))
	if err != nil {
		return
	}

	for _, v := range files {
		_ = os.RemoveAll(v)
	}
	return nil
}

func ClearFolder(path string) (err error) {
	return clearFolder(path)
}
