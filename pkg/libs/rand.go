package libs

import (
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	mathRand "math/rand"
	"server/pkg/constant"
	"strconv"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

// [0, max) string
func RandVCodeString(max int32) string {
	num := fmt.Sprintf("%06v", mathRand.New(mathRand.NewSource(time.Now().UnixNano())).Int31n(max))
	return num
}

// RandNumberString 订单账单的uuid 纯数字,随机数,不用包含信息
func RandNumberString() string {
	return fmt.Sprintf("%d%08v", time.Now().Unix(), mathRand.New(mathRand.NewSource(time.Now().UnixNano())).Int63n(100000000))
}

func RandCouponUUID() string {
	return constant.CouponPre + RandNumberString()
}

func RandDeploymentUUID() string {
	return GenRandomStrByUUID(10)
}

// 大写字母和数字
func RandNumCapitalization(l int) string {
	str := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	bytes := []byte(str)
	var result []byte
	r := mathRand.New(mathRand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)

}

// RandStrForPwd 指定长度的用作容器 root password. 注意此函数有 + 等不被 docker 接受的特殊字符.
/** 测试样例:
 * len: 10 7uJsopKGM7
 * len: 15 89LWmaav7Jl14g9
 * len: 20 brOJ1QGhzxZtVhTC1QjA
 * len: 5 M+PIT
 * len: -5 s/28lI8ooQOfznaDzd7MUS5q4esJprQeL9aJc3XFxrM=
 * len: 0 epGhxMfMtj8wVGsnBYq+w+X82qSqIrwq8Jc9m1bY2zo=
 */
func RandStrForPwd(length int) string {
	// date +%s |sha256sum |base64 |head -c 10 ;echo

	u := time.Now().UnixNano()

	h := sha256.New()
	h.Write([]byte(strconv.FormatInt(u, 10)))

	summed := h.Sum(nil)
	encoded := base64.StdEncoding.EncodeToString(summed)

	if length <= 0 || len(encoded) <= length {
		return encoded
	}

	return encoded[0:length]
}

// GenRandomStrByUUID 产生指定长度的纯数字字母的随机字符串. 基于 uuid.
/** 测试样例
 * -1 --- 返回空字符串
 *  0 --- 返回空字符串
 * 10 --- 2ef89fd289
 * 20 --- dc116925b8ed4c268011
 * 32 --- 1bf089473eef4ffba802e866081fb32d
 * 44 --- 91bea5820b80442ba68252350224d7d1e74fb4e3ecda
 * 64 --- b440d0fb6caf4d98aa2b2d4586254a3e1d8d3e7c1f544fbf9cedef8519154f06
 * 65 --- df97676715c1468f85d082116f9b6e1b421bc220a7ba493a9d0c40ce4769efae3
 */
func GenRandomStrByUUID(length int) string {
	if length <= 0 {
		return ""
	}

	var pure = func() string {
		str := uuid.NewV4().String()
		ps := strings.ReplaceAll(str, "-", "")
		return ps
	} // 稳定制造长度为 32 的字符串

	var result = ""

	pureTimes := length/len(pure()) + 1 // 注意 +1
	for i := 0; i < pureTimes; i++ {
		result += pure()
	}

	// 倒序, 因为 uuid 尾部的时间随机性更大.
	result = result[len(result)-length:]
	return result
}

// JupyterToken e.g. jupyter-6ff511900c-f63d5706-b913e1
// 其中 6ff511900c-f63d5706 取自 runtime_uuid: container-6ff511900c-f63d5706, 后面接 6 位随机字符串 b913e1。
func JupyterToken(u constant.ContainerRuntimeUUID) string {
	token := fmt.Sprintf("%s-%s", string(u), GenRandomStrByUUID(65))
	token = strings.TrimPrefix(token, "container-")
	token = fmt.Sprintf("jupyter-%s", token)
	return token
}

// RandNum 数字 [0,n)
func RandNum(n int) int {
	if n < 5 {
		n = 5
	}
	r := mathRand.New(mathRand.NewSource(time.Now().UnixNano()))
	return r.Intn(n)
}
