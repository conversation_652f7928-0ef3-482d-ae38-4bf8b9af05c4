package requests

import (
	"fmt"
	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"net/http"
	constant "server/pkg-agent/agent_constant"
	"time"
)

type Requests struct {
	url       string
	machineID string
	token     string
}

func NewRequests(machineID, token string, urls []string) (r *Requests, err error) {
	var errs []error
	for _, u := range urls {
		r, err = newRequests(u, machineID, token)
		if err == nil {
			return
		}
		errs = append(errs, err)
	}

	err = nil
	for _, e := range errs {
		errBegin := "errors"
		if err != nil {
			errBegin = err.Error()
		}
		err = fmt.Errorf("%s, %s", errBegin, e.<PERSON>())
	}

	return
}

func newRequests(url, machineID, token string) (r *Requests, err error) {
	r = &Requests{url: url, machineID: machineID, token: token}

	var res *grequests.Response
	res, err = grequests.Post(url, &grequests.RequestOptions{
		Headers:        r.getHeader(),
		RequestTimeout: time.Second * 5,
	})
	if err != nil {
		err = errors.Wrap(err, "grequests post failed")
		return
	}
	if res.StatusCode == http.StatusNotFound {
		err = errors.New("log server router response: 404 not found")
		return
	}

	return
}

func (r *Requests) GetUrl() string {
	return r.url
}
func (r *Requests) getHeader() map[string]string {
	return map[string]string{
		constant.AgentAuthorizationHeaderKey: r.token,
		constant.AgentMachineIDHeaderKey:     r.machineID,
	}
}

func (r *Requests) Post(content interface{}) error {
	res, err := grequests.Post(r.url, &grequests.RequestOptions{
		Headers:        r.getHeader(),
		JSON:           content,
		RequestTimeout: time.Second * 5,
	})
	if err != nil {
		err = errors.Wrap(err, "grequests post failed")
		return err
	}
	if res.StatusCode != http.StatusOK {
		return errors.Errorf("grequests failed, status code: [%d], body: [%s]", res.StatusCode, res.String())
	}
	return nil
}
