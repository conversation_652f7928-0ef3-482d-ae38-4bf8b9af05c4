package libs

import (
	"testing"
)

func TestMask(t *testing.T) {
	a := Mask("abcd")
	if a != "a***d" {
		t.<PERSON><PERSON><PERSON>("failed mask " + a)
	}

	a = Mask("abc")
	if a != "a***c" {
		t.<PERSON><PERSON><PERSON>("failed mask " + a)
	}

	a = Mask("ab")
	if a != "a***" {
		t.<PERSON><PERSON><PERSON>("failed mask " + a)
	}

	a = Mask("a")
	if a != "a***" {
		t.<PERSON><PERSON><PERSON>("failed mask " + a)
	}

	a = Mask("")
	if a != "***" {
		t.<PERSON><PERSON>("failed mask " + a)
	}
}
