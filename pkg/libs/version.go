package libs

import "fmt"

const (
	version010  = "0.1.0"
	version011  = "0.1.1"
	version012  = "0.1.2"
	version020  = "0.2.0"
	version021  = "0.2.1"
	version051  = "0.5.1"
	version060  = "0.6.0"
	version061  = "0.6.1"
	version070  = "0.7.0"
	version080  = "0.8.0"
	version110  = "1.1.0"
	version120  = "1.2.0"
	version130  = "1.3.0"
	version140  = "1.4.0"
	version160  = "1.6.0"
	version161  = "1.6.1"
	version170  = "1.7.0"
	version180  = "1.8.0"
	version190  = "1.9.0"
	version200  = "2.0.0"
	version210  = "2.1.0"
	version220  = "2.2.0"
	version240  = "2.4.0"
	version250  = "2.5.0"
	version260  = "2.6.0"
	version270  = "2.7.0"
	version274  = "2.7.4"
	version275  = "2.7.5"
	version280  = "2.7.4"
	version282  = "2.8.2"
	version291  = "2.9.1"
	version300  = "3.0.0"
	version340  = "3.4.0"
	version410  = "4.1.0"
	version430  = "4.3.1"
	version511  = "5.1.1"
	version530  = "5.3.0"
	version535  = "5.3.5"
	version560  = "5.6.0"
	version5120 = "5.12.0"
	version5172 = "5.17.2"
	version5530 = "5.53.0"
	version5540 = "5.54.0"
	version5550 = "5.55.0"
	version5553 = "5.55.3"
	version5555 = "5.55.5"
	version5582 = "5.58.2"
)

const LatestVersion = version5582

var (
	BuiltTime string
	GitCommit string
	GoVersion string
)

var VersionTemplate = fmt.Sprintf(`
 Version: %s
 BuiltTime: %s
 GitCommit: %s
 GoVersion: %s
`, LatestVersion, BuiltTime, GitCommit, GoVersion)

var VersionMap = map[string]string{
	"Version":   LatestVersion,
	"BuiltTime": BuiltTime,
	"GitCommit": GitCommit,
	"GoVersion": GoVersion,
}
