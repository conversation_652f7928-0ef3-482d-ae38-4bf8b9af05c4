package controller

import (
	"github.com/gin-gonic/gin"
	_ "server/pkg-agent/agent_constant"
)

// Example
// ShowAccount godoc
// @Summary Example api doc
// @Description author: ningfd
// @Tags example
// @Accept  json
// @Produce  json
// @Param account body AccountRequest true "Add account"
// @Success 200 {object} AccountResponse
// @Failure 400 {object} agent_constant.NewContainerRuntimeParam
// @Failure 404 {object} agent_constant.NewContainerParam
// @Failure 500 {object} AccountResponse
// @Router /example/{id} [post]
func Example(c *gin.Context) {
}

// Account example
type AccountRequest struct {
	ID int `json:"id" example:"1" format:"int64"`
}

// Account example
type AccountResponse struct {
	ID   int    `json:"id" example:"1" format:"int64"`
	Name string `json:"name" example:"account name"`
	UUID string `json:"uuid" example:"550e8400-e29b-41d4-a716-************" format:"uuid"`
}

// @title GPU HUB API List
// @version 1.0
// @description The server API list of GPU HUB.
// @termsOfService http://swagger.io/terms/

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.basic BasicAuth

// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func __blank() {

}
