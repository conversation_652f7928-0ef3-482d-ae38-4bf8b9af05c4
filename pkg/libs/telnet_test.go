package libs_test

import (
	"context"
	"fmt"
	"math/rand"
	"net"
	"server/pkg/libs"
	"strconv"
	"sync"
	"testing"
	"time"
)

func TestTelnet(t *testing.T) {
	var testPorts = make(map[int]bool)  // 测试. key: port, val: is occupied
	var validPorts = make(map[int]bool) // 验证. key: port, val: is occupied

	var max = 46010
	var min = 46000

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	wg := sync.WaitGroup{}

	rand.Seed(time.Now().Unix())
	for i := 0; i < max-min; i++ {
		var occupied bool
		var port = min + i

		r := rand.Intn(100)
		if r < 40 { // 40%
			occupied = true

			wg.Add(1)
			go func() {
				server, err := net.Listen("tcp", "localhost:"+strconv.Itoa(port))
				if err != nil {
					return
				}
				defer server.Close()

				fmt.Printf("Listen to port: %d\n", port)
				wg.Done()

				select {
				case <-ctx.Done():
				}
			}()
		}

		testPorts[port] = occupied
	}

	wg.Wait() // 确保所有 listen 启动

	// test... 单个模式
	for k := range testPorts {
		occupiedPorts := libs.DoTelnet(ctx, "localhost", k)
		fmt.Println("Do telnet result: ", k, "->", occupiedPorts)
		validPorts[k] = len(occupiedPorts) != 0
	}

	// check...
	for k, v := range testPorts {
		if vv, ok := validPorts[k]; !ok || v != vv {
			t.Error("单个模式: 验证的端口绑定情况与测试集合不符")
		}
	}

	fmt.Println("单个模式: 验证的端口绑定情况与测试集对比")
	for k, v := range testPorts {
		fmt.Printf("Port: %d | TestOccupied: %-5v | ValidOccupied: %-5v", k, v, validPorts[k])
		if v != validPorts[k] {
			fmt.Print(" ✕ ")
		} else if v {
			fmt.Print(" √ ")
		}
		fmt.Println()
	}
	fmt.Println()

	// test... 多个模式
	var ports []int
	for k := range testPorts {
		ports = append(ports, k)
	}

	// check...
	occupiedPorts := libs.DoTelnet(ctx, "localhost", ports...)
	for _, port := range occupiedPorts {
		if !testPorts[port] {
			t.Error("多个模式: 验证的端口绑定情况与测试集合不符")
		}
	}

	fmt.Println("多个模式: 验证的端口绑定情况与测试集对比")
	for _, port := range occupiedPorts {
		fmt.Printf("Port: %d | TestOccupied: %-5v | ValidOccupied: true\n", port, testPorts[port])
	}
}
