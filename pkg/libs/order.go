package libs

import (
	"fmt"
	"server/conf"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"strings"
	"time"
)

// GetInstanceExpireTime 实例续费使用。根据一个时间，计费方式以及计费周期，算出实例的到期时间
func GetInstanceExpireTime(t *time.Time, chargeType constant.ChargeType, num int) (*time.Time, error) {
	if t == nil {
		return nil, businesserror.ErrInternalError
	}
	formatTime, _ := time.ParseInLocation(constant.FormatTimeString, t.Format(constant.FormatTimeString), time.Local)

	switch chargeType {
	case constant.ChargeTypeDaily:
		//if conf.GetGlobalGsConfig().App.DebugApi {
		//	ti := formatTime.Add(time.Duration(num) * 1 * time.Minute)
		//	return &ti, nil
		//}
		ti := formatTime.AddDate(0, 0, num)
		return &ti, nil
	case constant.ChargeTypeWeekly:
		//if conf.GetGlobalGsConfig().App.DebugApi {
		//	ti := formatTime.Add(time.Duration(num) * 2 * time.Minute)
		//	return &ti, nil
		//}
		ti := formatTime.AddDate(0, 0, 7*num)
		return &ti, nil
	case constant.ChargeTypeMonthly:
		//if conf.GetGlobalGsConfig().App.DebugApi {
		//	ti := formatTime.Add(time.Duration(num) * 10 * time.Minute)
		//	return &ti, nil
		//}
		ti := formatTime.AddDate(0, 0, 30*num)
		return &ti, nil
	case constant.ChargeTypeYearly:
		//if conf.GetGlobalGsConfig().App.DebugApi {
		//	ti := formatTime.Add(time.Duration(num) * 20 * time.Minute)
		//	return &ti, nil
		//}
		ti := formatTime.AddDate(0, 0, 365*num)
		return &ti, nil
	case constant.ChargeTypePayg:
		return nil, nil
	}
	return nil, businesserror.ErrInternalError
}

func GetInstanceLastExpireTime(t *time.Time, chargeType constant.ChargeType, num int) (*time.Time, error) {
	if t == nil {
		return nil, businesserror.ErrInternalError
	}
	formatTime, _ := time.ParseInLocation(constant.FormatTimeString, t.Format(constant.FormatTimeString), time.Local)

	switch chargeType {
	case constant.ChargeTypeDaily:
		if conf.GetGlobalGsConfig().App.DebugApi {
			ti := formatTime.Add(time.Duration(num) * -1 * time.Minute)
			return &ti, nil
		}
		ti := formatTime.AddDate(0, 0, -num)
		return &ti, nil
	case constant.ChargeTypeWeekly:
		if conf.GetGlobalGsConfig().App.DebugApi {
			ti := formatTime.Add(time.Duration(num) * -2 * time.Minute)
			return &ti, nil
		}
		ti := formatTime.AddDate(0, 0, -7*num)
		return &ti, nil
	case constant.ChargeTypeMonthly:
		if conf.GetGlobalGsConfig().App.DebugApi {
			ti := formatTime.Add(time.Duration(num) * -10 * time.Minute)
			return &ti, nil
		}
		ti := formatTime.AddDate(0, 0, -30*num)
		return &ti, nil
	case constant.ChargeTypeYearly:
		if conf.GetGlobalGsConfig().App.DebugApi {
			ti := formatTime.Add(time.Duration(num) * -20 * time.Minute)
			return &ti, nil
		}
		ti := formatTime.AddDate(0, 0, -365*num)
		return &ti, nil
	case constant.ChargeTypePayg:
		return nil, nil
	}
	return nil, businesserror.ErrInternalError
}

func GenerateBillWarningMsg(balance, confirmAsset, totalBillAsset int64) interface{} {
	return fmt.Sprintf("balance:%d, bill1:%d, bill2:%d", balance, confirmAsset, totalBillAsset)
}

func DeploymentPriceGet(runtimeType constant.ContainerRuntimeType, price int64) int64 {
	return GetPriceFloatingRate(runtimeType) * price / 1000
}

func GetPriceFloatingRate(runtimeType constant.ContainerRuntimeType) int64 {
	//switch runtimeType {
	//case constant.ContainerRuntimeOfDeployment:
	//	hour := time.Now().Hour()
	//	if hour >= 0 && hour < 8 {
	//		return 1100
	//	} else {
	//		return 1300
	//	}
	//}
	return 1000
}

func ParseMachineIDFromProductID(productID string) string {
	// instance: {machineID}-xxx
	// dc:		 xxx-{machineID}-xxx
	// dc reuse: xxx-{machineID}-xxx-xxx

	ps := strings.Split(productID, "-")
	if len(ps) == 2 {
		// instance
		return ps[0]
	} else if len(ps) == 3 {
		// dc
		return ps[1]
	} else if len(ps) == 4 {
		return ps[1]
	}
	return ""
}
