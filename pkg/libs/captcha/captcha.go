package captcha

import (
	"bytes"
	"encoding/base64"
	"fmt"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"image"
	"image/color"
	"image/png"
	"io"
	"math/rand"
	"os"
	"time"
)

// 生成块拼图验证码base64
func GenerateBlockPuzzleCaptcha(points PicturePoint) (captchaId, originalImageBase64, jigsawImageBase64 string, err error) {
	captchaId = uuid.NewV4().String()
	backOut, jigsawOut, err := CutImageByTemplate(points)
	if err != nil {
		log.WithField("err", err).Error("Cut image bt template failed.")
		return
	}

	originalImageBase64, err = imageToBase64(backOut)
	if err != nil {
		log.WithField("err", err).Error("Encode background base64 failed.")
		return
	}

	jigsawImageBase64, err = imageToBase64(jigsawOut)
	if err != nil {
		log.WithField("err", err).Error("Encode jigsaw base64 failed.")
		return
	}
	return
}

// 获取裁剪的背景和拼图并转换为对应base64
func imageToBase64(img image.Image) (res string, err error) {
	emptyBuff := bytes.NewBuffer(nil)
	err = png.Encode(emptyBuff, img)
	if err != nil {
		return
	}
	var dist = make([]byte, 500000)
	base64.StdEncoding.Encode(dist, emptyBuff.Bytes())
	index := bytes.IndexByte(dist, 0)
	res = string(dist[0:index])
	return
}

type PicturePoint struct {
	X, Y int
}

// 对图片进行扣图操作
func CutImageByTemplate(offset PicturePoint) (backOut, jigsawOut image.Image, err error) {
	// 随机获取一张背景图和一张模板图(模板图应该为随机)
	backgroundImg, err := GetBackgroundImage()
	if err != nil {
		return
	}
	templateImg, err := GetTemplateImage()
	if err != nil {
		return
	}
	// 对背景图用模板进行处理
	templateBounds := templateImg.Bounds()
	backgroundBounds := backgroundImg.Bounds()
	for x := 0; x < templateBounds.Dx(); x++ {
		for y := 0; y < templateBounds.Dy(); y++ {
			_, _, _, a := templateImg.At(x, y).RGBA()
			if isOpacity(a) {
				continue
			}
			r, g, b, a := backgroundImg.At(x+offset.X, y+offset.Y).RGBA()
			templateImg.(*image.NRGBA).SetNRGBA(x, y, color.NRGBA{
				R: uint8(r),
				G: uint8(g),
				B: uint8(b),
				A: uint8(a),
			})
			opacity := uint8(float64(a) * 0.5)
			backgroundImgColor := backgroundBounds.ColorModel().Convert(color.NRGBA{
				R: uint8(r),
				G: uint8(g),
				B: uint8(b),
				A: opacity,
			})
			rr, gg, bb, aa := backgroundImgColor.RGBA()
			backgroundImg.(*image.NRGBA).SetNRGBA(x+offset.X, y+offset.Y, color.NRGBA{
				R: uint8(rr),
				G: uint8(gg),
				B: uint8(bb),
				A: uint8(aa),
			})
		}
	}

	backOut = backgroundImg
	jigsawOut = templateImg
	return
}

// 随机获取一张背景图片
func GetBackgroundImage() (background image.Image, err error) {
	// 随机获取目录中的图片
	rand.Seed(time.Now().UnixNano())
	buf := bytes.NewBuffer(backgroundImages[rand.Intn(len(backgroundImages))])
	background, err = imageReaderToPicture(buf)
	if err != nil {
		log.WithField("err", err).Error("Open image in catalog failed.")
		return
	}
	return
}

// 随机获取一张模板图片
func GetTemplateImage() (template image.Image, err error) {
	// 随机获取目录中的图片
	rand.Seed(time.Now().UnixNano())
	buf := bytes.NewBuffer(jigsawImages[rand.Intn(len(jigsawImages))])
	template, err = imageReaderToPicture(buf)
	if err != nil {
		log.WithField("err", err).Error("Open image in catalog failed.")
		return
	}
	return
}

// 读取路径下的图片
func imageReaderToPicture(reader io.Reader) (img image.Image, err error) {
	img, _, err = image.Decode(reader)
	return
}

// 读取路径下的图片
func imagePathToPicture(path string) (img image.Image, err error) {
	var file *os.File
	file, err = os.Open(path)
	defer func() {
		_ = file.Close()
	}()
	if err != nil {
		log.WithField("path", path).WithError(err).Error("Open the named file for reading failed.")
		return
	}

	img, _, err = image.Decode(file)
	if err != nil {
		return
	}
	return
}

// 判断是否透明
func isOpacity(value uint32) bool {
	if value < 65535 {
		return true
	}
	return false
}

// 随机生成小图模板的坐标值(坐标值范围背景图长宽减去小图长宽)
// 为了让移动的小图尽量在中间范围 x: 50-200 y: 20-80
func GetPoints() (point PicturePoint) {
	rand.Seed(time.Now().UnixNano())
	x := rand.Intn(150) + 50
	y := rand.Intn(60) + 20
	point = PicturePoint{
		X: x,
		Y: y,
	}
	return point
}

func CheckPointIsValid(originPoint, targetPoint *PicturePoint, offsetX int) bool {
	if abs(originPoint.X, targetPoint.X) <= offsetX && originPoint.Y == targetPoint.Y {
		return true
	}
	return false
}

// 取绝对值
func abs(x, y int) int {
	if x > y {
		return x - y
	}
	return y - x
}

func getRandomOffset(bgWidth, tempWidth int) (offset int) {
	diffWidth := bgWidth - tempWidth
	offset = rand.Intn(diffWidth - 1)
	return
}

// 获取所有模板图片,从模板目录下获取所有的图片
func GetTemplateImages() (templateImages []string) {
	path := ""
	fileInfoList, err := os.ReadDir(path)
	if err != nil {
		log.WithError(err).Warn("Read dir failed.")
		return
	}
	for i := range fileInfoList {
		name := fmt.Sprintf("%s, %s", path, fileInfoList[i].Name())
		templateImages = append(templateImages, name)
	}
	return
}

// 获取所有不等于指定模板的模板(为后续做干扰模板做准备)
func exclude(templateImages []string, str string) (temp []string) {
	for i := range templateImages {
		if str != templateImages[i] {
			temp = append(temp, templateImages[i])
		}
	}
	return
}

// 获取干扰模板
// func GetInterfere(background string, vo TemplateVo) (jigsaw TemplateVo) {
// 	templates := exclude(GetTemplateImages(), vo.Src)
// 	src := GetRandomTemplate(templates)
// 	img := ImagePathToPicture(src)
// 	return
// }

// 获取每张图片的大小
func GetPictureSize(imagePath string) (height, width int) {
	img, err := imagePathToPicture(imagePath)
	if err != nil {
		return
	}
	b := img.Bounds()
	width = b.Max.X
	height = b.Max.Y
	return
}

// 给图片加水印
func MakeWaterMark() {

}
