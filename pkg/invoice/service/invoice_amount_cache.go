package service

import (
	"encoding/json"
	"fmt"
	"time"

	"server/pkg/logger"
	"server/pkg/redis_plugin"
)

// InvoiceAmountCacheManager 发票金额缓存管理器
type InvoiceAmountCacheManager struct {
	redis *redis_plugin.RedisClient
	log   *logger.Logger
}

// 缓存相关常量
const (
	// 缓存键前缀
	UserInvoiceAmountCacheKeyPrefix = "user:invoice:amount"
	
	// 缓存TTL
	UserInvoiceAmountCacheTTL = 30 * time.Minute
	
	// 缓存更新锁前缀
	UserInvoiceAmountLockPrefix = "lock:user:invoice:amount"
	
	// 锁超时时间
	LockTimeout = 10 * time.Second
)

// NewInvoiceAmountCacheManager 创建缓存管理器
func NewInvoiceAmountCacheManager(redis *redis_plugin.RedisClient) *InvoiceAmountCacheManager {
	return &InvoiceAmountCacheManager{
		redis: redis,
		log:   logger.NewLogger("invoice_amount_cache"),
	}
}

// GetCacheKey 获取缓存键
func (cm *InvoiceAmountCacheManager) GetCacheKey(uid int) string {
	return fmt.Sprintf("%s:%d", UserInvoiceAmountCacheKeyPrefix, uid)
}

// GetLockKey 获取锁键
func (cm *InvoiceAmountCacheManager) GetLockKey(uid int) string {
	return fmt.Sprintf("%s:%d", UserInvoiceAmountLockPrefix, uid)
}

// Get 获取缓存数据
func (cm *InvoiceAmountCacheManager) Get(uid int) (data map[string]int64, found bool) {
	cacheKey := cm.GetCacheKey(uid)
	
	result, err := cm.redis.Get(cacheKey)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Warn("get cache failed")
		return nil, false
	}
	
	if result == "" {
		return nil, false
	}
	
	data = make(map[string]int64)
	err = json.Unmarshal([]byte(result), &data)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("unmarshal cache data failed")
		// 缓存数据损坏，删除缓存
		cm.Delete(uid)
		return nil, false
	}
	
	return data, true
}

// Set 设置缓存数据
func (cm *InvoiceAmountCacheManager) Set(uid int, data map[string]int64) error {
	cacheKey := cm.GetCacheKey(uid)
	
	jsonData, err := json.Marshal(data)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("marshal cache data failed")
		return err
	}
	
	err = cm.redis.Set(cacheKey, string(jsonData), UserInvoiceAmountCacheTTL)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("set cache failed")
		return err
	}
	
	cm.log.WithField("uid", uid).Info("cache updated successfully")
	return nil
}

// Delete 删除缓存数据
func (cm *InvoiceAmountCacheManager) Delete(uid int) error {
	cacheKey := cm.GetCacheKey(uid)
	
	err := cm.redis.Del(cacheKey)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("delete cache failed")
		return err
	}
	
	cm.log.WithField("uid", uid).Info("cache deleted successfully")
	return nil
}

// TryLock 尝试获取更新锁
func (cm *InvoiceAmountCacheManager) TryLock(uid int) (bool, error) {
	lockKey := cm.GetLockKey(uid)
	
	success, err := cm.redis.SetNX(lockKey, "1", LockTimeout)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("try lock failed")
		return false, err
	}
	
	return success, nil
}

// ReleaseLock 释放更新锁
func (cm *InvoiceAmountCacheManager) ReleaseLock(uid int) error {
	lockKey := cm.GetLockKey(uid)
	
	err := cm.redis.Del(lockKey)
	if err != nil {
		cm.log.WithError(err).WithField("uid", uid).Error("release lock failed")
		return err
	}
	
	return nil
}

// RefreshAsync 异步刷新缓存
func (cm *InvoiceAmountCacheManager) RefreshAsync(uid int, calculator func(int) (map[string]int64, error)) {
	go func() {
		// 尝试获取锁，避免重复刷新
		locked, err := cm.TryLock(uid)
		if err != nil || !locked {
			cm.log.WithField("uid", uid).Warn("skip refresh due to lock")
			return
		}
		defer cm.ReleaseLock(uid)
		
		// 重新计算数据
		data, err := calculator(uid)
		if err != nil {
			cm.log.WithError(err).WithField("uid", uid).Error("calculate invoice amount failed")
			return
		}
		
		// 更新缓存
		err = cm.Set(uid, data)
		if err != nil {
			cm.log.WithError(err).WithField("uid", uid).Error("refresh cache failed")
		}
	}()
}

// BatchDelete 批量删除缓存
func (cm *InvoiceAmountCacheManager) BatchDelete(uids []int) error {
	if len(uids) == 0 {
		return nil
	}
	
	keys := make([]string, len(uids))
	for i, uid := range uids {
		keys[i] = cm.GetCacheKey(uid)
	}
	
	err := cm.redis.DelBatch(keys)
	if err != nil {
		cm.log.WithError(err).WithField("uids", uids).Error("batch delete cache failed")
		return err
	}
	
	cm.log.WithField("uids", uids).Info("batch cache deleted successfully")
	return nil
}
