package service

import (
	"encoding/json"
	bcm "server/pkg/billing_center/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/invoice/model"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/plugin/redis_plugin"
	"time"

	"gorm.io/gorm"
)

type InvoiceService struct {
	log          *logger.Logger
	bc           module_definition.BCInterface
	user         module_definition.UserInterface
	userCache    *redis_plugin.UserInfoCache
	invoiceRedis *redis_plugin.InvoiceRedisPlugin
}

const InvoiceServiceModuleName = "invoice_service"

func NewInvoiceServiceProvider(
	self *InvoiceService,
	bc module_definition.BCInterface,
	user module_definition.UserInterface,
	userCache *redis_plugin.UserInfoCache,
	invoiceRedis *redis_plugin.InvoiceRedisPlugin,
) {
	self.log = logger.NewLogger(InvoiceServiceModuleName)
	self.bc = bc
	self.user = user
	self.userCache = userCache
	self.invoiceRedis = invoiceRedis
}

func (svc *InvoiceService) InvoiceAuth(uid int, userInvoiceUUID string) (*model.UserInvoice, error) {
	var invoice *model.UserInvoice
	invoiceModel := &model.UserInvoice{}
	err := db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: invoiceModel,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				invoiceModel.GetInvoiceUUIDColumn(): userInvoiceUUID,
				invoiceModel.GetUIDColumn():         uid,
			},
		},
	}, &invoice).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).WithField("invoice_uuid", userInvoiceUUID).Warn("user has not invoice authority")
		err = businesserror.ErrAuthorizeFailed
		return nil, err
	}
	return invoice, nil
}

func (svc *InvoiceService) InvoiceTitleInfoAuth(uid, InvoiceTitleInfoID int) (err error) {
	var InvoiceTitleInfo *model.InvoiceTitleInfo
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.InvoiceTitleInfo{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id":  InvoiceTitleInfoID,
				"uid": uid,
			},
		},
	}, &InvoiceTitleInfo).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).WithField("id", InvoiceTitleInfoID).Warn("user has not invoice title info authority")
		err = businesserror.ErrAuthorizeFailed
		return
	}
	return
}

func (svc *InvoiceService) InvoiceMailInfoAuth(uid, invoiceMailInfoID int) (err error) {
	var invoiceMailInfo *model.InvoiceMailInfo
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.InvoiceMailInfo{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id":  invoiceMailInfoID,
				"uid": uid,
			},
		},
	}, &invoiceMailInfo).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).WithField("id", invoiceMailInfoID).Warn("user has not invoice mail authority")
		err = businesserror.ErrAuthorizeFailed
		return
	}
	return
}

func (svc *InvoiceService) CreateInvoice(uid int, param *model.CreateUserInvoiceParam) (err error) {
	if param == nil {
		svc.log.Error("create user invoice param is nil")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	if param.InvoiceAmount < constant.InvoiceAmountMinValue {
		svc.log.Warn("invoice amount lower than min amount")
		err = businesserror.ErrInvoiceAmountTooSmallErr
		return
	}

	mailInfo, err := svc.GetInvoiceMailInfo(param.InvoiceMailInfoID)
	if err != nil {
		svc.log.WithField("err", err).WithField("mail_info_id", param.InvoiceMailInfoID).Error("get user invoice mail info failed")
		return
	}
	if mailInfo.UID != uid {
		svc.log.Error("invoice mail info not belong to the user")
		err = businesserror.ErrInvalidRequestParams
		return
	}

	titleInfo, err := svc.GetInvoiceTitleInfo(param.InvoiceTitleInfoID)
	if err != nil {
		svc.log.WithField("err", err).WithField("tital_info_id", param.InvoiceTitleInfoID).Error("get user invoice title info failed")
		return
	}
	if titleInfo.UID != uid {
		svc.log.Error("invoice title info not belong to the user")
		err = businesserror.ErrInvalidRequestParams
		return
	}

	actualAmount, err := svc.GetUserInvoiceActualAmount(uid)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user invoice actual amount failed")
		return
	}
	actualAmount = libs.AssetRound(actualAmount, false)
	if param.InvoiceAmount > actualAmount && param.UserInvoiceType == constant.RechargingInvoice {
		svc.log.Warn("invoice amount greater than actual amount")
		err = businesserror.ErrInvoiceAmountErr
		return
	}

	var totalInvoiceAmount int64

	// 给param.EntityInfo赋值
	orderUUIDs, dailyBillUUIDs, arrearOrderUUIDs := []string{}, []string{}, []string{}
	for i, ob := range param.EntityInfo {
		if ob.Type == constant.DailyBillType {
			// 按账单-日结账单
			dailyBill, err := svc.bc.GetDailyBillByUuid(ob.DailyBillUUID)
			if err != nil {
				svc.log.WithField("err", err).WithField("daily_bill_uuid", ob.DailyBillUUID).Error("get daily_bill failed")
				err = businesserror.ErrInternalError
				return err
			}
			if dailyBill.InvoiceUUID != "" {
				err = businesserror.ErrInvoiceAlreadyExistInBill
				return err
			}

			order, err := svc.bc.GetOrder(dailyBill.OrderUUID)
			if err != nil {
				svc.log.WithField("err", err).WithField("order_uuid", ob.OrderUUID).Error("get order failed")
				err = businesserror.ErrInternalError
				return err
			}

			ob.OrderType = order.OrderType
			ob.ProductType = order.OrderType.ToProductType()
			ob.InvoiceAmount = dailyBill.PayAmount
			ob.PayAmount = dailyBill.PayAmount
			ob.ProductUUID = dailyBill.ProductUUID
			ob.PayAt = dailyBill.PayAt
			ob.OrderUUID = dailyBill.OrderUUID

			dailyBillUUIDs = append(dailyBillUUIDs, ob.DailyBillUUID)
		} else if ob.Type == constant.OrderChargeType {
			// 按账单-订单
			order, err := svc.bc.GetOrder(ob.OrderUUID)
			if err != nil {
				svc.log.WithField("err", err).WithField("order_uuid", ob.OrderUUID).Error("get order failed")
				err = businesserror.ErrInternalError
				return err
			}

			ob.OrderType = order.OrderType
			ob.ProductType = order.OrderType.ToProductType()
			ob.InvoiceAmount = order.PayByBalance - order.RefundAmount
			ob.PayAmount = order.PayByBalance
			ob.ProductUUID = order.ProductUUID
			ob.PayAt = *order.PayAt
			ob.Details = order.DetailsEntity
			if order.InvoiceUUID != "" {
				err = businesserror.ErrInvoiceAlreadyExistInOrder
				return err
			}

			orderUUIDs = append(orderUUIDs, ob.OrderUUID)
		} else {
			arrearOrderUUIDs = append(arrearOrderUUIDs, ob.OrderUUID)
		}

		param.EntityInfo[i] = ob
		totalInvoiceAmount += param.EntityInfo[i].InvoiceAmount
	}

	if param.UserInvoiceType == constant.BillInvoice {
		if totalInvoiceAmount > actualAmount {
			svc.log.Warn("invoice amount greater than actual amount")
			err = businesserror.ErrInvoiceAmountErr
			return
		}
		param.InvoiceAmount = totalInvoiceAmount
	}

	invoiceUUID := libs.RandNumberString()
	userInvoice := &model.UserInvoice{
		UID:             uid,
		InvoiceUUID:     invoiceUUID,
		InvoiceAmount:   param.InvoiceAmount,
		InvoiceProperty: param.InvoiceProperty,
		Status:          constant.InvoiceReviewing,
		UserInvoiceType: param.UserInvoiceType,
		StatusIndex:     constant.InvoiceStatusIndexMap[constant.InvoiceReviewing],
		Content:         param.ContentType.Content(),
		Description:     param.Description,
		CreatedAt:       time.Now(),
	}

	invoiceTitleInfoContent := &model.InvoiceTitleContent{
		Title:       titleInfo.Title,
		Type:        titleInfo.Type,
		USCI:        titleInfo.USCI,
		BankName:    titleInfo.BankName,
		BankAccount: titleInfo.BankAccount,
		Address:     titleInfo.Address,
		Telephone:   titleInfo.Telephone,
	}

	invoiceMailInfoContent := &model.InvoiceMailContent{
		MailAddress:    mailInfo.MailAddress,
		EMailAddress:   mailInfo.EMailAddress,
		Recipient:      mailInfo.Recipient,
		RecipientPhone: mailInfo.RecipientPhone,
		AddressType:    mailInfo.AddressType,
	}
	err = userInvoice.JsonTitleAndMailInfo(invoiceTitleInfoContent, invoiceMailInfoContent)
	if err != nil {
		svc.log.WithField("err", err).Error("invoice JsonTitleAndMailInfo failed")
		err = businesserror.ErrInternalError
		return
	}

	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		errDB = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.UserInvoice{},
			InsertPayload:           userInvoice,
		})
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).WithField("uid", uid).Error("create user invoice failed")
			err = businesserror.ErrDatabaseError
			return db_helper.GetModelError(err)
		}
		if len(orderUUIDs) > 0 {
			err = svc.bc.UpdateOrderInvoiceUUID(tx, userInvoice.InvoiceUUID, orderUUIDs)
			if err != nil {
				svc.log.WithField("err", err).WithField("order_uuids", orderUUIDs).Error("update order invoice_uuid failed")
				return db_helper.GetModelError(err)
			}
		}
		if len(dailyBillUUIDs) > 0 {
			err = svc.bc.UpdateDailyBillInvoiceUUID(tx, userInvoice.InvoiceUUID, dailyBillUUIDs)
			if err != nil {
				svc.log.WithField("err", err).WithField("daily_bill_uuids", dailyBillUUIDs).Error("update daily bill invoice_uuid failed")
				return db_helper.GetModelError(err)
			}
		}
		if len(arrearOrderUUIDs) > 0 {
			err = svc.bc.UpdateArrearNewInvoiceUUID(tx, userInvoice.InvoiceUUID, arrearOrderUUIDs)
			if err != nil {
				svc.log.WithField("err", err).WithField("order_uuids", dailyBillUUIDs).Error("update arrear invoice status failed")
				return db_helper.GetModelError(err)
			}
		}
		// 创建发票明细记录
		err = svc.InsertInvoiceHistory(tx, uid, userInvoice.InvoiceUUID, param.EntityInfo)
		if err != nil {
			svc.log.WithField("err", err).WithField("invoice_uuid", userInvoice.InvoiceUUID).Error("create invoice history failed")
			return db_helper.GetModelError(err)
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("create user invoice failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) InvoiceCreateByAdmin(param *model.InvoiceCreateByAdminParam) (err error) {
	if param.InvoiceAmount < constant.InvoiceAmountMinValue {
		err = businesserror.ErrInvoiceAmountTooSmallErr
		return
	}

	//existed, err := svc.GetUserReviewingInvoice(param.UID)
	//if err != nil {
	//	svc.log.WithField("err", err).Warn("get user reviewing invoice failed")
	//	return
	//}
	//
	//if existed {
	//	err = businesserror.ErrReviewingInvoiceExisted
	//	return err
	//}

	invoiceUUID := libs.RandNumberString()
	userInvoice := &model.UserInvoice{
		UID:             param.UID,
		InvoiceUUID:     invoiceUUID,
		InvoiceAmount:   param.InvoiceAmount,
		InvoiceProperty: constant.ElectronicInvoice,
		Status:          constant.InvoiceFinished,
		Content:         constant.InvoiceContent1,
		UserInvoiceType: constant.RechargingInvoice,
		StatusIndex:     constant.InvoiceStatusIndexMap[constant.InvoiceFinished],
		CreatedAt:       time.Now(),
	}

	invoiceTitleInfoContent := &model.InvoiceTitleContent{
		Title: "其他(线下开票)",
	}

	err = userInvoice.JsonTitleAndMailInfo(invoiceTitleInfoContent, nil)

	if err != nil {
		svc.log.WithField("err", err).Error("invoice JsonTitleAndMailInfo failed")
		err = businesserror.ErrInternalError
		return
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.UserInvoice{},
		InsertPayload:   userInvoice,
	}).GetError()

	if err != nil {
		svc.log.WithField("err", err).WithField("uid", param.UID).Error("create user invoice failed")
		err = businesserror.ErrDatabaseError
		return err
	}

	return
}

func (svc *InvoiceService) ConfirmInvoice(param *model.ConfirmInvoiceParam) (err error) {
	if param == nil {
		svc.log.Error("update user invoice param is null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	_, err = svc.GetUserInvoice(param.InvoiceUUID)
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("get user invoice failed ")
		err = businesserror.ErrDatabaseError
		return
	}
	data := map[string]interface{}{"status": constant.InvoiceFinished,
		"status_index": constant.InvoiceStatusIndexMap[constant.InvoiceFinished]}
	if param.ExpressAccount != "" {
		data["invoice_express_account"] = param.ExpressAccount
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.UserInvoice{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"invoice_uuid": param.InvoiceUUID}}},
		data).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("update use invoice failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) RejectInvoice(param *model.RejectInvoiceParam) (err error) {
	if param == nil {
		svc.log.Error("update user invoice param is null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	_, err = svc.GetUserInvoice(param.InvoiceUUID)
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("get user invoice failed ")
		err = businesserror.ErrDatabaseError
		return
	}
	// 这里去掉发票的状态判断，无论发票的状态是什么，都支持驳回用户的发票申请
	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.UserInvoice{},
			DBTransactionConnection: tx,
			Filters:                 db_helper.QueryFilters{EqualFilters: map[string]interface{}{"invoice_uuid": param.InvoiceUUID}}},
			map[string]interface{}{"status": constant.InvoiceReject, "status_index": constant.InvoiceStatusIndexMap[constant.InvoiceReject], "reject_reason": param.RejectReason}).GetError()
		if err != nil {
			svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("update user invoice failed")
			err = businesserror.ErrDatabaseError
			return
		}
		err = svc.bc.SetOrderInvoiceUUIDNull(tx, param.InvoiceUUID)
		if err != nil {
			svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("set order invoice_uuid null failed")
			return db_helper.GetModelError(err)
		}
		err = svc.bc.SetDailyBillInvoiceUUIDNull(tx, param.InvoiceUUID)
		if err != nil {
			svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("set daily bill invoice_uuid null failed")
			return db_helper.GetModelError(err)
		}
		// err = svc.bc.SetArrearNewInvoiceUUIDNull(tx, param.InvoiceUUID)
		// if err != nil {
		//	svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("update arrear invoice status failed")
		//	return db_helper.GetModelError(err)
		// }
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("finally update user invoice failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) SetInvoiceExpressInfo(param *model.InvoiceExpressParam) (err error) {
	if param == nil {
		svc.log.Error("update user invoice express info param is null")
		err = businesserror.ErrInvalidRequestParams
		return
	}
	_, err = svc.GetUserInvoice(param.InvoiceUUID)
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("get user invoice failed ")
		err = businesserror.ErrDatabaseError
		return
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.UserInvoice{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"invoice_uuid": param.InvoiceUUID}}},
		map[string]interface{}{"invoice_express_account": param.ExpressAccount}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("invoice_uuid", param.InvoiceUUID).Error("update user invoice express info failed")
		err = businesserror.ErrDatabaseError
		return
	}
	return
}

func (svc *InvoiceService) UserInvoiceList(db *gorm.DB, uid int, param *model.UserInvoiceListParam) (paged *db_helper.PagedData, list []*model.UserInvoiceInfo, err error) {
	var userInvoiceList []*model.UserInvoice

	if db == nil {
		db = db_helper.GlobalDBConn()
	}

	db = db.Table(model.TableNameUserInvoice).Where("deleted_at is null")
	if uid != 0 {
		db = db.Where("uid = ?", uid)
	}
	if param.Status != "" {
		db = db.Where("status = ?", param.Status)
	}
	if param.Title != "" {
		db = db.Where("title_info like ?", "%"+param.Title+"%")
	}
	if param.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", param.DateFrom)
	}
	if param.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", param.DateTo)
	}
	if param.InvoiceAmount != 0 {
		db = db.Where("invoice_amount = ?", param.InvoiceAmount)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Warn("Count failed.")
		err = businesserror.ErrInternalError
		return
	}
	paged = db_helper.BuildPagedDataUtil(param.PageIndex, param.PageSize, int(count), 0)

	// 按照创建时间进行倒序排序
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Order("id desc").
		Find(&userInvoiceList).
		Error
	if err != nil {
		svc.log.WithError(err).Warn("Get user invoice list failed.")
		err = businesserror.ErrInternalError
		return
	}
	var uids []int
	for _, invoice := range userInvoiceList {
		uids = append(uids, invoice.UID)
	}

	phoneMap := map[int]string{}
	nameMap := map[int]string{}
	if len(userInvoiceList) != 0 {
		phoneMap, err = svc.user.CacheIDToPhoneMGet(uids)
		if err != nil {
			svc.log.WithField("ids", uids).ErrorE(err, "id to phone failed")
			err = nil
		}
		nameMap, err = svc.user.CacheIDToNameMGet(uids)
		if err != nil {
			svc.log.WithField("ids", uids).ErrorE(err, "id to name failed")
			err = nil
		}
	}

	list = make([]*model.UserInvoiceInfo, 0)
	for i := range userInvoiceList {
		phone := phoneMap[userInvoiceList[i].UID]
		userName := nameMap[userInvoiceList[i].UID]
		info := &model.UserInvoiceInfo{
			InvoiceUUID:     userInvoiceList[i].InvoiceUUID,
			UserPhone:       phone,
			UserName:        userName,
			InvoiceAmount:   userInvoiceList[i].InvoiceAmount,
			InvoiceProperty: userInvoiceList[i].InvoiceProperty,
			UserInvoiceType: userInvoiceList[i].UserInvoiceType,
			Status:          userInvoiceList[i].Status,
			Content:         userInvoiceList[i].Content,
			RejectReason:    userInvoiceList[i].RejectReason,
			CreateAt:        userInvoiceList[i].CreatedAt,
			TitleInfo:       userInvoiceList[i].TitleContent,
			MailInfo:        userInvoiceList[i].MailContent,
			ExpressAccount:  userInvoiceList[i].InvoiceExpressAccount,
			BillURL:         userInvoiceList[i].BillURL,
			InvoiceURL:      userInvoiceList[i].InvoiceURL,
		}
		err = userInvoiceList[i].LoadTitleAndMailInfo()
		if err == nil {
			info.TitleInfo = userInvoiceList[i].TitleContent
			info.MailInfo = userInvoiceList[i].MailContent
		}
		list = append(list, info)
	}
	paged.List = list
	return
}

func (svc *InvoiceService) GetUserInvoice(invoiceUUID string) (invoice *model.UserInvoice, err error) {
	invoiceModel := &model.UserInvoice{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: invoiceModel,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				invoiceModel.GetInvoiceUUIDColumn(): invoiceUUID,
			},
		},
	}, &invoice).GetError()
	if err != nil {
		return
	}
	return
}

func (svc *InvoiceService) GetUserReviewingInvoice(uid int) (existed bool, err error) {
	var invoice *model.UserInvoice
	invoiceModel := &model.UserInvoice{}
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: invoiceModel,
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				invoiceModel.GetStatusColumn(): constant.InvoiceReviewing,
				invoiceModel.GetUIDColumn():    uid,
			},
		},
	}, &invoice).GetError()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		svc.log.WithField("err", err).Error("get reviewing invoice failed")
		return false, businesserror.ErrDatabaseError
	}
	return true, nil
}

// 获取用户所有能开票总额
func (svc *InvoiceService) GetUserInvoiceActualAmount(uid int) (totalRealInvoiceAmount int64, err error) {
	// 1.获取用户充值所有充值金额
	rechargingAmount, err := svc.bc.GetUserRechargeAmount(uid)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharging amount failed")
		err = businesserror.ErrInternalError
		return
	}
	// 2.获取用总的开票金额
	invoicedAmount, err := svc.getUserInvoiceAmountByType(uid, []constant.UserInvoiceType{constant.BillInvoice, constant.RechargingInvoice})
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user invoiced amount failed")
		err = businesserror.ErrInternalError
		return
	}
	totalRealInvoiceAmount = rechargingAmount - invoicedAmount
	return
}

func (svc *InvoiceService) getUserInvoiceAmountByType(uid int, invoiceTypes []constant.UserInvoiceType) (amount int64, err error) {
	err = db_helper.GlobalDBConn().Table(model.TableNameUserInvoice).
		Where("uid = ?", uid).
		Where("status != ?", constant.InvoiceReject).
		Where("status != ?", constant.InvoiceRevoked).
		Where("user_invoice_type in (?)", invoiceTypes).
		Select("IFNULL(abs(sum(user_invoice.invoice_amount)), 0)").
		Find(&amount).Error
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).WithField("invoice_type", invoiceTypes).Error("get user invoice failed")
		return
	}
	return
}

func (svc *InvoiceService) GetUserInvoiceAmountInfo(uid int) (data map[string]int64, err error) {
	data = make(map[string]int64, 0)
	// 1.获取用户充值所有充值金额
	rechargingAmount, err := svc.bc.GetUserRechargeAmount(uid)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharging amount failed")
		err = businesserror.ErrInternalError
		return
	}
	// 2.获取用户账单开票金额
	billInvoicedAmount, err := svc.getUserInvoiceAmountByType(uid, []constant.UserInvoiceType{constant.BillInvoice})
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user bill invoiced amount failed")
		err = businesserror.ErrInternalError
		return
	}
	// 3.获取用户充值开票金额
	rechargingInvoicedAmount, err := svc.getUserInvoiceAmountByType(uid, []constant.UserInvoiceType{constant.RechargingInvoice})
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user recharging invoiced amount failed")
		err = businesserror.ErrInternalError
		return
	}

	// 4.去除提现金额
	asset, err := svc.bc.BillGetUserAllWithdraw(uid)
	if err != nil {
		return
	}
	rechargingAmount -= asset

	// 5.去除冻结金额
	wallet, err := svc.bc.GetWalletAndAllVoucher(uid)
	if err != nil {
		return
	}

	rechargingAmount -= wallet.BlockedAsset

	// 获取用户所有能开票总额
	totalRealInvoiceAmount := rechargingAmount - billInvoicedAmount - rechargingInvoicedAmount

	// 获取用户总的消费金额
	data["total_recharging_amount"] = rechargingAmount            // 累计充值金额
	data["bill_invoiced_amount"] = billInvoicedAmount             // 账单已开票金额
	data["recharging_invoiced_amount"] = rechargingInvoicedAmount // 充值已开票金额
	data["total_real_invoice_amount"] = totalRealInvoiceAmount    // 可开票金额
	return
}

func (svc *InvoiceService) GetUserTotalRealInvoiceAmount(uid int) (amount int64, err error) {
	data, err := svc.GetUserInvoiceAmountInfo(uid)
	if err != nil {
		return
	}
	return data["total_real_invoice_amount"], nil
}

// 获取用户所有欠票记录
func (svc *InvoiceService) GetUserArrearInvoiceRecords(uid int, newInvoiceUUID string) (infos []*bcm.ArrearInvoiceInfo, err error) {
	infos = make([]*bcm.ArrearInvoiceInfo, 0)
	_, arrearInvoices, err := svc.bc.GetArrearInvoice(uid, "", newInvoiceUUID, nil)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("get user arrear invoice bill failed")
		return
	}
	for _, arrearInvoice := range arrearInvoices {
		infos = append(infos, &bcm.ArrearInvoiceInfo{
			OrderUUID:    arrearInvoice.OrderUUID,
			ProductUUID:  arrearInvoice.ProductUUID,
			ArrearAmount: arrearInvoice.ArrearAmount,
			InvoiceUUID:  arrearInvoice.InvoiceUUID,
			PayAt:        arrearInvoice.CreatedAt,
		})
	}
	return
}

func (svc *InvoiceService) GetUserInvoiceOrder(uid int, invoiceUUID string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, invoiceOrderInfo []*model.InvoiceOrderOrBillInfo, err error) {
	invoiceOrderListParam := &bcm.InvoiceOrderListParams{
		UID:         uid,
		ChargeType:  []constant.ChargeType{constant.ChargeTypeDaily, constant.ChargeTypeWeekly, constant.ChargeTypeMonthly, constant.ChargeTypeYearly},
		Status:      []constant.OrderStatus{constant.OrderStatusSuccess, constant.OrderStatusUnused},
		InvoiceUUID: invoiceUUID,
	}
	paged, invoiceOrders, err := svc.bc.GetInvoiceOrderList(invoiceOrderListParam, pageReq)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("Get user invoice order list failed")
		return
	}

	invoiceOrderInfo = make([]*model.InvoiceOrderOrBillInfo, 0)
	for _, invoiceOrder := range invoiceOrders {
		invoiceAmount := invoiceOrder.PayByBalance - invoiceOrder.RefundAmount // 开票金额为支付的钱-退款的钱
		invoiceOrderInfo = append(invoiceOrderInfo,
			&model.InvoiceOrderOrBillInfo{
				OrderUUID:     invoiceOrder.UUID,
				ProductUUID:   invoiceOrder.ProductUUID,
				Type:          constant.OrderChargeType,
				PayAmount:     invoiceOrder.PayByBalance,
				InvoiceAmount: invoiceAmount,
				PayAt:         invoiceOrder.CreatedAt,
				SymbolUUID:    invoiceOrder.UUID,
				ProductType:   invoiceOrder.OrderType.ToProductType(),
				Details:       invoiceOrder.DetailsEntity,
				OrderType:     invoiceOrder.OrderType,
			})

	}
	data := make(map[string]interface{})
	data["order_bill_list"] = invoiceOrderInfo
	paged.List = data
	return
}

func (svc *InvoiceService) GetUserInvoiceDailyBill(uid int, invoiceUUID string, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, invoiceDailyBillInfo []*model.InvoiceOrderOrBillInfo, err error) {
	paged, dailyBills, err := svc.bc.GetDailyBillList(uid, invoiceUUID, pageReq)
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("Get daily bill list failed")
		return
	}

	invoiceDailyBillInfo = make([]*model.InvoiceOrderOrBillInfo, 0)
	for _, dailyBill := range dailyBills {
		typ := constant.ProductTypeInstance
		if dailyBill.ProductType != "" {
			typ = dailyBill.ProductType
		}
		detail := make(map[string]interface{})
		json.Unmarshal(dailyBill.DetailsJson, &detail)

		invoiceDailyBillInfo = append(invoiceDailyBillInfo,
			&model.InvoiceOrderOrBillInfo{
				OrderUUID:     dailyBill.OrderUUID,
				DailyBillUUID: dailyBill.UUID,
				ProductUUID:   dailyBill.ProductUUID,
				Type:          constant.DailyBillType,
				PayAmount:     dailyBill.PayAmount,
				InvoiceAmount: dailyBill.PayAmount,
				PayAt:         dailyBill.PayAt,
				SymbolUUID:    dailyBill.UUID,
				ProductType:   typ,
				Details:       detail,
			})
	}
	data := make(map[string]interface{})
	data["order_bill_list"] = invoiceDailyBillInfo
	paged.List = data
	return
}

// 作废发票
func (svc *InvoiceService) AdminSetUserInvoiceRevoke(uid int, invoiceUUID string) (err error) {
	if len(invoiceUUID) < 0 {
		return businesserror.ErrInvalidRequestParams
	}

	err = db_helper.NewDBConn().Transaction(func(tx *gorm.DB) (errDB db_helper.ModelError) {
		userInvoice := &model.UserInvoice{}
		err = userInvoice.UserInvoiceGetFirst(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":          uid,
				"invoice_uuid": invoiceUUID,
			},
		})
		if err != nil {
			svc.log.ErrorE(err, "get user invoice failed")
			return
		}

		// 更新发票作废中状态
		invoiceUm := map[string]interface{}{
			"status": constant.InvoiceRevoking,
		}

		err = userInvoice.UserInvoiceUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"uid":          uid,
				"invoice_uuid": invoiceUUID,
			},
		}, invoiceUm)
		if err != nil {
			svc.log.ErrorE(err, "update user invoice revoking failed")
			return
		}
		return
	}).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("uid", uid).Error("create user invoice failed")
		err = businesserror.ErrDatabaseError
		return
	}

	return
}

// UserRevokeInvoice 用户作废发票 - 包含完整的业务逻辑
func (svc *InvoiceService) UserRevokeInvoice(uid int, invoiceUUID string) (err error) {
	if len(invoiceUUID) == 0 {
		return businesserror.ErrInvalidRequestParams
	}
	// 1. Redis频次检查
	err = svc.CheckUserRevokeFrequency(uid)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":   uid,
			"error": err.Error(),
		}).Error("user revoke frequency check failed")
		return err
	}

	// 2. 验证用户对发票的权限
	userInvoice, err := svc.InvoiceAuth(uid, invoiceUUID)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":          uid,
			"invoice_uuid": invoiceUUID,
			"error":        err.Error(),
		}).Error("user invoice auth failed")
		return err
	}

	// 3. 检查发票是否满足作废条件
	err = svc.validateInvoiceRevokeConditions(userInvoice)
	if err != nil {
		return err
	}

	// 4. 先记录作废操作到Redis（用于频次限制）
	err = svc.recordUserRevokeOperation(uid)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":   uid,
			"error": err.Error(),
		}).Error("record user revoke operation failed")
		return businesserror.ErrServerBusy
	}

	// 5. 更新发票状态
	invoiceUm := map[string]interface{}{
		userInvoice.GetStatusColumn(): constant.InvoiceRevoking,
	}

	err = userInvoice.UserInvoiceUpdate(db_helper.GlobalDBConn(), &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			userInvoice.GetUIDColumn():         uid,
			userInvoice.GetInvoiceUUIDColumn(): invoiceUUID,
		},
	}, invoiceUm)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":          uid,
			"invoice_uuid": invoiceUUID,
			"error":        err.Error(),
		}).Error("update user invoice revoking failed")

		// 数据库更新失败，需要回滚Redis频次
		rollbackErr := svc.rollbackUserRevokeOperation(uid)
		if rollbackErr != nil {
			svc.log.WithFields(map[string]interface{}{
				"uid":            uid,
				"rollback_error": rollbackErr.Error(),
				"original_error": err.Error(),
			}).Error("failed to rollback redis operation after database update failed")
		}

		return businesserror.ErrDatabaseError
	}

	return nil
}

// CheckUserRevokeFrequency 检查用户作废发票的频次限制
func (svc *InvoiceService) CheckUserRevokeFrequency(uid int) error {
	// 直接获取当前作废次数
	count, err := svc.invoiceRedis.GetUserRevokeCount(uid)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":   uid,
			"error": err.Error(),
		}).Error("get user revoke count from redis failed")
		return businesserror.ErrServerBusy
	}

	// 检查是否超过限制（30天内最多2次）
	if count >= 2 {
		svc.log.WithFields(map[string]interface{}{
			"uid":           uid,
			"current_count": count,
			"limit":         2,
		}).Error("user revoke frequency exceeded")
		return businesserror.ErrUserRevokeFrequencyExceeded
	}

	return nil
}

// recordUserRevokeOperation 记录用户作废操作（用于频次限制）
func (svc *InvoiceService) recordUserRevokeOperation(uid int) error {
	err := svc.invoiceRedis.IncrementUserRevokeCount(uid)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":   uid,
			"error": err.Error(),
		}).Error("record user revoke operation to redis failed")
		return err
	}

	svc.log.WithFields(map[string]interface{}{
		"uid": uid,
	}).Error("user revoke operation recorded successfully")

	return nil
}

// rollbackUserRevokeOperation 回滚用户作废操作（用于数据一致性保证）
func (svc *InvoiceService) rollbackUserRevokeOperation(uid int) error {
	err := svc.invoiceRedis.DecrementUserRevokeCount(uid)
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"uid":   uid,
			"error": err.Error(),
		}).Error("rollback user revoke operation from redis failed")
		return err
	}

	svc.log.WithFields(map[string]interface{}{
		"uid": uid,
	}).Error("user revoke operation rollback successfully")

	return nil
}

// validateInvoiceRevokeConditions 验证发票是否满足作废条件
func (svc *InvoiceService) validateInvoiceRevokeConditions(invoice *model.UserInvoice) error {
	// 1. 检查发票状态是否为"已开票"
	if invoice.Status != constant.InvoiceFinished {
		svc.log.WithFields(map[string]interface{}{
			"status":       invoice.Status,
			"invoice_uuid": invoice.InvoiceUUID,
		}).Error("invoice status not allow revoke")
		return businesserror.ErrInvoiceStatusNotAllowRevoke
	}

	// 2. 检查发票类型是否为电子发票
	if invoice.InvoiceProperty != constant.ElectronicInvoice {
		svc.log.WithFields(map[string]interface{}{
			"invoice_property": invoice.InvoiceProperty,
			"invoice_uuid":     invoice.InvoiceUUID,
		}).Error("only electronic invoice can be revoked")
		return businesserror.ErrInvoiceNotElectronic
	}

	// 3. 检查invoice_url是否不为空
	if invoice.InvoiceURL == "" {
		svc.log.WithFields(map[string]interface{}{
			"invoice_uuid": invoice.InvoiceUUID,
			"invoice_url":  "empty",
		}).Error("invoice url is empty")
		return businesserror.ErrInvoiceUrlEmpty
	}

	// 4. 检查开具发票时间是否在30天内（按年月日小时格式计算）
	now := time.Now()
	invoiceDate := invoice.CreatedAt

	// 计算时间差（按年月日小时格式）
	nowHour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	invoiceHour := time.Date(invoiceDate.Year(), invoiceDate.Month(), invoiceDate.Day(), invoiceDate.Hour(), 0, 0, 0, invoiceDate.Location())
	hoursDiff := nowHour.Sub(invoiceHour).Hours()
	daysDiff := hoursDiff / 24

	if daysDiff > 30 {
		svc.log.WithFields(map[string]interface{}{
			"created_at":   invoiceDate.Format("2006-01-02 15:04"),
			"current_time": now.Format("2006-01-02 15:04"),
			"hours_diff":   hoursDiff,
			"days_diff":    daysDiff,
			"limit_days":   30,
			"invoice_uuid": invoice.InvoiceUUID,
		}).Error("invoice created time exceeded 30 days")
		return businesserror.ErrInvoiceExpired
	}

	return nil
}
