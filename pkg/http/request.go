package http

import (
	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"time"
)

// GetRequestUrl eg: http://*************:8000/api/debug/router
func GetRequestUrl(r *http.Request) string {
	if r == nil {
		return ""
	}
	prefix := "http://"
	if r.TLS != nil {
		prefix = "https://"
	}

	return prefix + r.Host + r.URL.String()
}

var client = http.Client{
	Transport: &http.Transport{
		MaxIdleConnsPerHost: 128,
	},
}

// 发送Get请求
func GetHttpUrl(url string) ([]byte, error) {
	rsp, err := grequests.Get(url, &grequests.RequestOptions{
		RequestTimeout: time.Second * 5,
	})
	if err != nil {
		err = errors.Wrap(err, "grequests get failed.")
		return nil, err
	}
	defer rsp.RawResponse.Body.Close()
	return io.ReadAll(rsp.RawResponse.Body)
}
