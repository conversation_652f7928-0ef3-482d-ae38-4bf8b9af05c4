package model_020

import (
	"database/sql"
	"encoding/json"
	biz "server/pkg/businesserror"
	"server/pkg/constant"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const tableNameInstance string = "instance"

type Instance struct {
	ID        int            `gorm:"AUTO_INCREMENT;unique_index;PRIMARY KEY;column:id;" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index;index:instance_uuid;" json:"-"`

	// 基本信息
	UUID      string `gorm:"column:uuid;index:instance_uuid;" json:"uuid"` // 实例 id
	UID       int    `gorm:"column:uid" json:"uid"`                        // 创建人, 详细信息从 /pkg/user 模块获取.
	MachineID string `gorm:"column:machine_id;" json:"machine_id"`         // machine_id -> /pkg/machine

	// 最新起止时间 与 最新状态信息
	StartedAt  sql.NullTime `gorm:"type:datetime;column:started_at;" json:"started_at"`   // 上次启动时间, 只在非 shutdown 有效. 此处需要保证每次运行都刷新次时间.
	FinishedAt sql.NullTime `gorm:"type:datetime;column:finished_at;" json:"finished_at"` // 上次结算时间, 只在非 shutdown 有效. 此处需要保证每次运行都刷新次时间.

	// Preload 信息, 且一定非空.
	LatestStatusID int           `gorm:"column:latest_status_id" json:"latest_status_id"`
	LatestStatus   StatusHistory `gorm:"foreignkey:LatestStatusID;association_foreignkey:CouponID" json:"latest_status"`
	LatestOptionID int           `gorm:"column:latest_option_id" json:"latest_option_id"`
	LatestOption   OptionHistory `gorm:"foreignkey:LatestOptionID;association_foreignkey:CouponID" json:"latest_option"`

	// 配置信息 json 与 struct
	ConfigContent datatypes.JSON        `gorm:"type:json;column:config_content;" json:"-"` // json in db
	Config        *CreateInstanceConfig `gorm:"-" json:"config"`                           // struct in memory

	// 运行参数 json 与 struct
	RuntimeParamContent datatypes.JSON `gorm:"type:json;column:runtime_param_content;" json:"-"` // json in db
	RuntimeParam        *RuntimeParam  `gorm:"-;" json:"runtime_param"`                          // struct in memory

	// 资源使用情况, usage 信息 json 与 struct
	UsageAt      time.Time          `gorm:"type:datetime;column:usage_at;" json:"usage_at"` // 更新 usage 的时间. 初始默认健康.
	UsageContent datatypes.JSON     `gorm:"type:json;column:usage_content;" json:"-"`       // json in db
	Usage        *InstanceUsageInfo `gorm:"-" json:"usage"`                                 // struct in memory

	// 用户自定义, 不参与业务
	Name        string `gorm:"column:name;" json:"name"`               // 实例名
	Description string `gorm:"column:description;" json:"description"` // 实例描述

	// 与其他模块的交互, 仅作为判断信息
	ChargeType constant.ChargeType `gorm:"column:charge_type;" json:"charge_type"` // 付费方式
	OrderUUID  string              `gorm:"column:order_uuid;" json:"order_uuid"`   // 双向绑定订单 uuid

	// 仅满足前端筛选需要的冗余信息, 不参与业务
	ToBeRemoved      bool         `gorm:"column:to_be_removed;type:tinyint(1)" json:"to_be_removed"`         // 标注此实例即将释放, 用于在 list 中排除此项.
	PaygPrice        int64        `gorm:"column:payg_price;" json:"payg_price"`                              // 如果是按量付费, 记录价格.gpu平台价格信息为实际的1000倍.如4.5元,实际存储4500
	ExpiredAt        sql.NullTime `gorm:"type:datetime;column:expired_at;" json:"expired_at"`                // 包年包月的到期时间
	ReleasedResource bool         `gorm:"column:released_resource;type:tinyint(1)" json:"released_resource"` // 是否已经释放了 GPU
	ImageName        string       `gorm:"type:varchar(255);column:image_name;NOT NULL;" json:"image_name"`
}

func (m *Instance) TableName() string {
	return tableNameInstance
}

// Init 实现 db_helper 接口.
func (m *Instance) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&Instance{})
}

// ----------------------------------------------------------------------------------------------

// LoadAllJsonByStruct 将内存中的结构体转化为数据库中的 json 形式.
func (m *Instance) LoadAllJsonByStruct() error {
	var err error
	err = m.LoadConfigJsonByStruct()
	if err != nil {
		return err
	}

	err = m.LoadRuntimeParamJsonByStruct()
	if err != nil {
		return err
	}

	err = m.LoadUsageJsonByStruct()
	if err != nil {
		return err
	}
	return nil
}

// LoadAllStructByJson 从数据库中的 json 反序列化出结构体.
func (m *Instance) LoadAllStructByJson() error {
	var err error
	err = m.LoadConfigStructByJson()
	if err != nil {
		return err
	}

	err = m.LoadRuntimeParamStructByJson()
	if err != nil {
		return err
	}

	err = m.LoadUsageStructByJson()
	if err != nil {
		return err
	}
	return nil
}

// --------------------------------------------------------------------------------

func (m *Instance) LoadConfigJsonByStruct() error {
	if m.Config == nil {
		return nil // nothing happened
	}

	var err error
	m.ConfigContent = make([]byte, 0)
	m.ConfigContent, err = json.Marshal(m.Config)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

func (m *Instance) LoadConfigStructByJson() error {
	if len(m.ConfigContent) == 0 {
		return nil // nothing happened
	}

	m.Config = &CreateInstanceConfig{}
	err := json.Unmarshal(m.ConfigContent, m.Config)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

func (m *Instance) LoadRuntimeParamJsonByStruct() error {
	if m.RuntimeParam == nil {
		return nil // nothing happened
	}

	var err error
	m.RuntimeParamContent = make([]byte, 0)
	m.RuntimeParamContent, err = json.Marshal(m.RuntimeParam)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

func (m *Instance) LoadRuntimeParamStructByJson() error {
	if len(m.RuntimeParamContent) == 0 {
		return nil // nothing happened
	}

	m.RuntimeParam = &RuntimeParam{}
	err := json.Unmarshal(m.RuntimeParamContent, m.RuntimeParam)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

func (m *Instance) LoadUsageJsonByStruct() error {
	if m.Usage == nil {
		return nil // nothing happened
	}

	var err error
	m.UsageContent = make([]byte, 0)
	m.UsageContent, err = json.Marshal(m.Usage)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

func (m *Instance) LoadUsageStructByJson() error {
	if len(m.UsageContent) == 0 {
		return nil // nothing happened
	}

	m.Usage = &InstanceUsageInfo{}
	err := json.Unmarshal(m.UsageContent, m.Usage)
	if err != nil {
		err = biz.ErrInternalError.New().Append(err)
		return err
	}

	return nil
}

// -------------------------------------------------------------------------------

func (m *Instance) IsPAYG() bool {
	return m.ChargeType.IsPayg()
}
