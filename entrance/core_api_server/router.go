package core_api_server

import (
	"server/conf"
	"server/entrance/initialize"
	"server/pkg/docs"
	"server/pkg/libs"

	"github.com/gin-gonic/gin"
)

/**
 * 需要确保在执行此步骤之前执行完成 pkg 的互相依赖的初始化.
 */

func initRouter(r *gin.Engine, ctrl *initialize.CoreControllerFactory, mw *initialize.CoreMiddlewareFactory) {
	// 全局应用RequestID中间件
	r.Use(mw.RequestIDMiddleware)

	r.GET("/health", func(context *gin.Context) {
		context.JSON(200, map[string]string{
			"status": "green",
			"code":   "ok",
		})
	})
	r.GET("/version", func(context *gin.Context) {
		context.JSON(200, libs.VersionMap)
	})
	if conf.GetGlobalGsConfig().App.DocsApi {
		r.GET("/swagger/*any", docs.SwaaggerRouter)
	}
	v1 := r.Group("/api/v1")
	{
		v1.POST("/gpu_stock/create", ctrl.GpuStockController.Create)
		v1.POST("/gpu_stock/reserve", ctrl.GpuStockController.Reserve)
		v1.POST("/gpu_stock/reserve_commit", ctrl.GpuStockController.ReserveCommit)
		v1.POST("/gpu_stock/release", ctrl.GpuStockController.Release)
		v1.POST("/gpu_stock/get_used", ctrl.GpuStockController.GetGpuStockUsed)
		v1.POST("/gpu_stock/get_used_count", ctrl.GpuStockController.CountMachineGpuStockUsed)
		v1.POST("/gpu_stock/get_used_all", ctrl.GpuStockController.CountAllMachineGpuStockUsed)
		v1.POST("/gpu_stock/get_reserve_detail", ctrl.GpuStockController.GetReserveDetail)
		v1.POST("/gpu_stock/get_all", ctrl.GpuStockController.GetGpuStockAll)
		v1.POST("/gpu_stock/get_free_all", ctrl.GpuStockController.GetAllFreeGpuStock)
		v1.POST("/gpu_stock/get_by_runtime_uuids", ctrl.GpuStockController.GetGpuStockByRuntimeUUID)
		v1.POST("/gpu_stock/get_by_uuid", ctrl.GpuStockController.GetGpuStockByUUID)
		v1.POST("/gpu_stock/delete", ctrl.GpuStockController.DeleteGpuStock)
		v1.POST("/gpu_stock/admin/list", ctrl.GpuStockController.GpuStockListForAdmin)
		v1.POST("/gpu_stock/admin/release", ctrl.GpuStockController.GpuStockReleaseByAdmin)
		v1.POST("/gpu_stock/admin/reserve", ctrl.GpuStockController.GpuStockReserveByAdmin)
		v1.POST("/gpu_stock/admin/delete", ctrl.GpuStockController.GpuStockDeleteByAdmin)

		v1.POST("/region/create", ctrl.RegionController.Create)
		v1.POST("/region/get_detail", ctrl.RegionController.GetRegionDetail)
		v1.POST("/region/get_list", ctrl.RegionController.GetRegionList)
		v1.POST("/region/get_detail_with_storage_info", ctrl.RegionController.GetRegionDetailWithStorageInfo)
		v1.POST("/region/get_storage_detail", ctrl.RegionController.GetStorageDetail)
		v1.POST("/region/check_region_sign_exist", ctrl.RegionController.CheckRegionSignExist)
		v1.POST("/region/check_region_name_exist", ctrl.RegionController.CheckRegionNameExist)
		v1.POST("/region/net_disk/init", ctrl.RegionController.InitNetDiskWithRegionForUser)
		v1.POST("/region/net_disk/get_list", ctrl.RegionController.GetNetDiskListForUser)
		v1.POST("/region/get_list_for_index", ctrl.RegionController.GetRegionListForIndex)
		v1.POST("/region/net_disk/set_quota", ctrl.RegionController.NetDiskSetQuota)
		v1.POST("/region/net_disk/get_user_mount_for_instance", ctrl.RegionController.GetUserNetDiskMountForInstance)
		v1.POST("/region/net_disk/get_quota_list", ctrl.RegionController.GetUserNetDiskQuotaList)
		v1.POST("/region/net_disk/get", ctrl.RegionController.GetNetDisk)
		v1.POST("/region/file_storage/init", ctrl.RegionController.InitFileStorageWithRegionForUser)
		v1.POST("/region/file_storage/get_list", ctrl.RegionController.GetFileStorageListForUser)
		v1.POST("/region/file_storage/get", ctrl.RegionController.GetFileStorageByUids)
		v1.POST("/region/file_storage/get_by_tenant", ctrl.RegionController.GetFileStorageForCharge)
		v1.POST("/region/file_storage/get_admin_list", ctrl.RegionController.GetFileStorageAdminList)
		v1.POST("/region/file_storage/update_setting", ctrl.RegionController.UpdateFileStorageSetting)
		v1.POST("/region/file_storage/get_detail", ctrl.RegionController.FsGetDetail)

		v1.POST("/hsfs/region/list", ctrl.RegionController.HSFSGetRegionList)
		v1.POST("/hsfs/init", ctrl.RegionController.HSFSInit)
		v1.POST("/hsfs/renewal", ctrl.RegionController.HSFSRenewal)
		v1.POST("/hsfs/expand", ctrl.RegionController.HSFSExpand)
		v1.POST("/hsfs/list", ctrl.RegionController.HSFSGetList)
		v1.POST("/hsfs/detail", ctrl.RegionController.HSFSGetDetail)
		v1.POST("/hsfs/setting", ctrl.RegionController.HSFSUpdateSetting)
		v1.POST("/hsfs/force_init", ctrl.RegionController.HSFSForceInit)

		v1.POST("/data_disk/create", ctrl.DataDiskController.Create)
		v1.POST("/data_disk/update", ctrl.DataDiskController.Update)
		v1.POST("/data_disk/get", ctrl.DataDiskController.Get)
		v1.POST("/data_disk/get_by_machine_ids", ctrl.DataDiskController.GetByMachineIds)
		v1.POST("/data_disk/get_list", ctrl.DataDiskController.GetList)
		v1.POST("/data_disk/reserve", ctrl.DataDiskController.Reserve)
		v1.POST("/data_disk/tx_commit", ctrl.DataDiskController.TxCommit)
		v1.POST("/data_disk/tx_rollback", ctrl.DataDiskController.TxRollback)
		//v1.POST("/data_disk/change_size", ctrl.DataDiskController.ChangeSize)
		v1.POST("/data_disk/release", ctrl.DataDiskController.Release)
		v1.POST("/data_disk/fix", ctrl.DataDiskController.Fix)

		v1.POST("/port/require", ctrl.PortController.Require)
		v1.POST("/port/free", ctrl.PortController.Free)
		v1.POST("/port/migrate", ctrl.PortController.Migrate)

		v1.POST("/machine/create", ctrl.MachineController.Create)
		v1.POST("/machine/basic_info/update", ctrl.MachineController.BasicInfoUpdate)
		v1.POST("/machine/get", ctrl.MachineController.Get)
		v1.POST("/machine/get_by_ids", ctrl.MachineController.GetByIDs)
		v1.POST("/machine/delete", ctrl.MachineController.Delete)
		v1.POST("/machine/get_list_for_business", ctrl.MachineController.GetListForBusiness)
		v1.POST("/machine/get_admin_list", ctrl.MachineController.GetAdminMachineList)
		v1.POST("/machine/check_health", ctrl.MachineController.CheckMachineHealth)
		v1.POST("/machine/check_status_health", ctrl.MachineController.CheckMachineStatusHealth)
		v1.POST("/machine/set_status", ctrl.MachineController.SetMachineStatus)
		v1.POST("/machine/get_all_machine_id", ctrl.MachineController.GetAllMachineID)
		v1.POST("/machine/get_by_gpu_type_id", ctrl.MachineController.GetMachineByGpuTypeID)
		v1.POST("/machine/update_online", ctrl.MachineController.UpdateOnline)
		v1.POST("/machine/get_list_for_common", ctrl.MachineController.GetMachineListForCommon)
		v1.POST("/machine/get_storage_oss_credentials", ctrl.MachineController.GetMachineStorageOssCredentials)
		v1.POST("/machine/set_region", ctrl.MachineController.SetMachineRegion)
		v1.POST("/machine/get_region", ctrl.MachineController.GetMachineRegion)
		v1.POST("/machine/update_setting", ctrl.MachineController.UpdateMachineSetting)
		v1.POST("/machine/work_order/create", ctrl.MachineController.WorkOrderCreate)
		v1.POST("/machine/work_order/update", ctrl.MachineController.WorkOrderUpdate)
		v1.POST("/machine/work_order/list", ctrl.MachineController.WorkOrderList)
		v1.POST("/machine/work_order/record/create", ctrl.MachineController.WorkOrderRecordCreate)
		v1.POST("/machine/work_order/record/list", ctrl.MachineController.WorkOrderRecordList)

		v1.POST("/gpu_type/create", ctrl.GpuTypeController.Create)
		v1.POST("/gpu_type/update", ctrl.GpuTypeController.Update)
		v1.POST("/gpu_type/get_list", ctrl.GpuTypeController.GetList)
		v1.POST("/gpu_type/delete", ctrl.GpuTypeController.Delete)
		v1.POST("/gpu_type/check_gpu_type_name", ctrl.GpuTypeController.CheckGpuTypeName)
		v1.POST("/gpu_type/get_gpu_id", ctrl.GpuTypeController.GetGpuID)
		v1.POST("/gpu_type/get", ctrl.GpuTypeController.GetGpu)
		v1.POST("/gpu_type/get_gpu_by_ids", ctrl.GpuTypeController.GetGpuByIDs)

		v1.POST("/container/check_exist_uuid", ctrl.ContainerController.CheckExistUUID)
		v1.POST("/container/get_record", ctrl.ContainerController.GetContainerRecord)
		v1.POST("/container/get_record_unscoped", ctrl.ContainerController.GetContainerRecordUnScoped)
		v1.POST("/container/get_records", ctrl.ContainerController.GetContainerRecords)
		v1.POST("/container/get_simplified_info", ctrl.ContainerController.GetContainerSimplifiedInfo)
		v1.POST("/container/get_simplified_info_list", ctrl.ContainerController.GetContainerSimplifiedInfoList)
		v1.POST("/container/get_simplified_info_list_with_usage", ctrl.ContainerController.GetContainerSimplifiedInfoListWithUsage)
		v1.POST("/container/check_status_num", ctrl.ContainerController.CheckContainerStatusNum)
		v1.POST("/container/get_operate_history", ctrl.ContainerController.GetContainerOperateHistory)
		v1.POST("/container/migrate/create", ctrl.ContainerController.CreateMigHistory)
		v1.POST("/container/migrate/check_in_migrating_status", ctrl.ContainerController.CheckMigHistoryInMigratingStatus)
		v1.POST("/container/migrate/update", ctrl.ContainerController.UpdateMigHistoryResult)
		v1.POST("/container/migrate/retry", ctrl.ContainerController.RetryMigHistory)
		v1.POST("/container/migrate/insert", ctrl.ContainerController.InsertMigHistory)
		v1.POST("/container/migrate/get_by_target_runtime_uuid", ctrl.ContainerController.GetMigrateByTargetRuntimeUUID)
		v1.POST("/container/migrate/list", ctrl.ContainerController.GetContainerMigrateList)
		//v1.POST("/container/migrate/count", ctrl.ContainerController.CountUserMigrateInstancePerDay)
		v1.POST("/container/operate", ctrl.ContainerController.OperateContainer)
		v1.POST("/container/operate_by_pub_mq", ctrl.ContainerController.OperateContainerByPubMQ)
		v1.POST("/container/get_container_usage_info", ctrl.ContainerController.GetContainerUsageInfo)
		v1.POST("/container/get_container_usage_info_list", ctrl.ContainerController.GetContainerUsageInfoList)
		v1.POST("/container/force_operate", ctrl.ContainerController.ForceOperateContainer)
		v1.POST("/container/get_container_running_error_runtime_uuid_list", ctrl.ContainerController.GetContainerRunningErrRuntimeUuidList)
		v1.POST("/container/remove_cache", ctrl.ContainerController.RemoveContainerCache)
		v1.POST("/container/recovery", ctrl.ContainerController.ContainerCacheRecovery)
		v1.POST("/container/delete_by_machine", ctrl.ContainerController.ContainerDeleteByMachine)
	}

	agent := r.Group("/agent/v1/")
	{
		// GPU machine agent
		agent.GET("connect", ctrl.AgentGuardController.RunReaderWriter)

		// NFS storage agent
		storageAgent := agent.Group("/storage")
		{
			storageAgent.GET("/connect", ctrl.AgentGuardController.StorageAgentRunReaderWriter)

			// 通过复用 middleware 解析 token, 再发回去. 但需要考虑验证.
			storageAgent.Use(mw.StorageAgentMiddleware.LoginRequired())
			storageAgent.POST("/auth", ctrl.AgentGuardController.StorageAgentAuthToken)
		}

		agent.GET("/version", func(context *gin.Context) {
			context.JSON(200, libs.VersionMap)
		})

		kv := agent.Group("/internal/kv")
		kv.Use(mw.KVMiddleware.KVRequired())
		{
			kv.GET("/key/:key", ctrl.KvController.Get)
			kv.POST("/key/:key", ctrl.KvController.Set)
		}
	}
	return
}
