package agent_constant

import (
	"encoding/json"
	"github.com/pkg/errors"
	"server/pkg-agent/agent_constant"
	"strings"
)

type MachineStatusParam struct {
	agent_constant.MachineHealthInfo
}

type MachineRegisterParam struct {
	agent_constant.MachineHardwareInfo
}

var (
	EchoMessageSkippedError = errors.New("this msg contain 'response_from_agent_flag', do not write response into socket pipe")
)

type ResponseCode string

const (
	CodeOK  = ""
	CodeErr = "Err"
)

func (c *MachineStatusParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return err
}

func (c *MachineRegisterParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return err
}
