package api_server

import (
	"server/entrance/initialize"
	"server/pkg/constant"
	"server/pkg/middleware"

	"github.com/gin-gonic/gin"
)

var (
	Adm = constant.Admin
	//SuperAdmin      = constant.SuperAdmin
	Fnc = constant.Finance
	Ops = constant.Operations
	TCS = constant.TechnicalCustomerService
	BCS = constant.BusinessCustomerService
)

func adminRouter(r *gin.Engine, ctrl *initialize.ControllerFactory, mw *middleware.Middleware) {
	admin := r.Group("/admin/v1")

	admin.POST("/new_login", ctrl.User.NewAdminLogin)   // 管理员登录(错误三次跳验证码)
	admin.POST("/login", ctrl.User.AdminLogin)          // 管理员登录(数字字母验证码)
	admin.POST("/fast_login", ctrl.User.AdminFastLogin) // 管理员短信登录
	//admin.POST("/init_admin", ctrl.User.InitAdmin)      // 初始管理员

	// *********************************************
	// 			以下每个接口都需要分别鉴权
	// *********************************************

	// 管理员设置
	admin.POST("/user/admin_set", mw.ALR(true), ctrl.User.SetUserAdmin)       // 设置用户为管理员
	admin.POST("/user/admin_cancel", mw.ALR(true), ctrl.User.CancelUserAdmin) // 将管理员变为普通用户

	// 用户管理
	admin.PUT("/user/update", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.UpdateUser)                            // 编辑用户
	admin.GET("/user/detail", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.GetUserDetail)                        // 获取用户详情
	admin.GET("/user/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.GetUserList)                            // 获取用户列表
	admin.POST("/user/check_user_exist", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.CheckUserExist)            // 检验用户是否存在
	admin.POST("/user/enable", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.EnableUser)                           // 启用用户
	admin.POST("/user/disable", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.DisableUser)                         // 禁用用户
	admin.POST("/user/create", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.CreateUser)                           // 创建用户
	admin.POST("/user/authority", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.UpdateUserAuthority)               // 更新用户相关权限
	admin.POST("/user/detail/phone", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.UserGroup.GetUserDetailByPhone)     // 电话获取用户详情
	admin.POST("/user/certification/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.UserCertificationList)   // 电话获取用户详情
	admin.PUT("/user/certification/review", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.UserCertificationReview) // 电话获取用户详情
	admin.GET("/user/certification", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.AdminUserCertificationDetail)  // 电话获取用户详情
	admin.PUT("/user/certification", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.UserCertificationAdminUpdate)   // 电话获取用户详情
	admin.GET("/user/operate_record", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.OperateRecordListForAdmin)
	admin.GET("/user/admin_operate_log", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.User.AdminOperateLogList)

	// 反馈
	admin.GET("/user/feedback/list", mw.ALR(false, Adm, TCS, BCS), ctrl.User.GetUserFeedBackList)     // 获取用户反馈列表
	admin.GET("/user/feedback/detail", mw.ALR(false, Adm, TCS, BCS), ctrl.User.GetUserFeedBackDetail) // 获取用户反馈详情
	admin.POST("/user/feedback/handle", mw.ALR(true, Adm, TCS, BCS), ctrl.User.HandleUserFeedBack)    // 处理用户反馈

	// 用户组 - 用户
	admin.POST("/group/user/create", mw.ALR(true, Adm, TCS, BCS), ctrl.UserGroup.CreateUserGroup)   // 创建用户组
	admin.DELETE("/group/user/delete", mw.ALR(true, Adm, TCS, BCS), ctrl.UserGroup.DeleteUserGroup) // 删除用户组记录
	admin.POST("/group/user/list", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.GetUserGroupList)   // 获取用户组列表
	admin.POST("/group/user/exist", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.CheckUserGroup)    // 检查用户在组里是否存在

	// 用户组
	admin.POST("/group/create", mw.ALR(true, Adm, TCS, BCS), ctrl.UserGroup.CreateGroup)                    // 新建组
	admin.POST("/group/update", mw.ALR(true, Adm, TCS, BCS), ctrl.UserGroup.UpdateGroup)                    // 编辑组
	admin.DELETE("/group/delete", mw.ALR(true, Adm, TCS, BCS), ctrl.UserGroup.DeleteGroup)                  // 删除组
	admin.GET("/group/detail", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.GetGroup)                       // 获取组详情
	admin.POST("/group/list", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.GroupList)                       // 组列表
	admin.POST("/group/check_name", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.CheckGroupName)            // 校验组名称
	admin.POST("/group/capacity", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.CheckGroupMaxUserCapacity)   // 校验组用户容量
	admin.POST("/group/max_capacity", mw.ALR(false, Adm, TCS, BCS), ctrl.UserGroup.CheckGroupIsMaxCapacity) // 校验组用户是否达到最大容量
	admin.GET("/group/get", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.UserGroup.GetAll)                       // 获取所有组

	// 用户会员管理
	admin.POST("/user/member/growth", mw.ALR(false, Adm, TCS, BCS), ctrl.User.GetUserMemberGrowth)       // 获取用户成长值
	admin.GET("/user/member/list", mw.ALR(false, Adm, TCS, BCS), ctrl.User.GetUserMemberList)            // 获取用户等级列表
	admin.PUT("/user/member/level", mw.ALR(true, Adm, TCS, BCS), ctrl.User.UpdateUserLevel)              // 管理员更改用户会员等级
	admin.GET("/user/member/level/list", mw.ALR(false, Adm, TCS, BCS), ctrl.User.GetUserMemberLevelList) // 管理员获取会员等级列表

	// 用户 - 子账号 - 数量管理
	admin.PUT("/sub_user", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.User.SubUserSettingByAdmin)

	// 机器
	admin.POST("/machine/update/info", mw.ALR(true, Adm, Ops), ctrl.Machine.UpdateInfo)                             // 编辑机器基本配置
	admin.POST("/machine/update/charge", mw.ALR(true, Adm, Ops), ctrl.Machine.UpdateCharge)                         // 编辑机器计费配置
	admin.POST("/machine/detail", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.AdminDetail)                      // 获取机器信息详情
	admin.DELETE("/machine/delete", mw.ALR(true, Adm, Ops), ctrl.Machine.Delete)                                    // 删除机器信息
	admin.POST("/machine/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.AdminMachineList)                   // 获取机器信息列表
	admin.POST("/machine/update_online", mw.ALR(true, Adm, Ops), ctrl.Machine.UpdateOnline)                         // 更新机器下架或上架
	admin.POST("/machine/user/visible", mw.ALR(true, Adm, Ops), ctrl.Machine.SetMachineUserVisibleLimit)            // 设置机器对用户是否可见
	admin.POST("/machine/user/group/get", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.MachineUserGroup.Get)             // 获取机器用户组关联记录
	admin.GET("/machine/gpu_type", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.AdminGetAllMachineGpuType)       // 获取所有机器的gpu号型
	admin.GET("/machine/tag/get", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.AdminMachineTag)                  // 获取机器的分类标签
	admin.POST("/machine/data_disk/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.AdminMachineDataDiskList) // 获取机器磁盘信息列表
	admin.POST("/machine/update_setting", mw.ALR(true, Adm, Ops), ctrl.Machine.UpdateMachineSetting)                // 更新机器配置

	// gpu stock
	admin.GET("/gpu_stock/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.GpuStock.GpuStockListForAdmin) //获取短信、微信消息发送列表
	admin.DELETE("/gpu_stock", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.GpuStock.GpuStockReleaseByAdmin)  //获取短信、微信消息发送列表
	admin.POST("/gpu_stock", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.GpuStock.GpuStockReserveByAdmin)    //获取短信、微信消息发送列表
	admin.POST("/gpu_stock/delete", mw.ALR(true, Adm, Ops), ctrl.GpuStock.GpuStockDeleteByAdmin)        // 删除主机GPU

	// 工单
	admin.POST("/machine/work_order/create", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.Machine.WorkOrderCreate)
	admin.PUT("/machine/work_order", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.Machine.WorkOrderUpdate)
	admin.POST("/machine/work_order/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.WorkOrderList)
	admin.POST("/machine/work_order/record/create", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.Machine.WorkOrderRecordCreate)
	admin.POST("/machine/work_order/record/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Machine.WorkOrderRecordList)

	// 镜像
	admin.POST("/image/create", mw.ALR(true, Adm), ctrl.Image.Create)                    // 创建镜像
	admin.POST("/image/update", mw.ALR(true, Adm), ctrl.Image.Update)                    // 编辑镜像
	admin.DELETE("/image/delete", mw.ALR(true, Adm), ctrl.Image.Delete)                  // 删除镜像
	admin.POST("/image/list", mw.ALR(false, Adm), ctrl.Image.List)                       // 获取镜像列表
	admin.POST("/image/get", mw.ALR(false, Adm), ctrl.Image.Get)                         // 获取筛选的镜像
	admin.POST("/image/check_image_name", mw.ALR(false, Adm), ctrl.Image.CheckIMageName) // 检查镜像名称
	admin.POST("/image/put_on", mw.ALR(true, Adm), ctrl.Image.PutOnShelves)              // 上架
	admin.POST("/image/pull_off", mw.ALR(true, Adm), ctrl.Image.PullOffShelves)          // 下架
	admin.GET("/image/is_off", mw.ALR(false, Adm), ctrl.Image.IsImageOff)                // 检查是否下架

	// 私有镜像
	admin.POST("/private/image/list", mw.ALR(false, Adm, TCS, BCS), ctrl.PrivateImage.AdminUserImageList) // 获取用户镜像列表

	// gpu-type
	admin.POST("/gpu_type/create", mw.ALR(true, Adm, Ops), ctrl.GpuType.Create)                            // 创建gpu号型
	admin.POST("/gpu_type/update", mw.ALR(true, Adm, Ops), ctrl.GpuType.Update)                            // 编辑gpu号型
	admin.DELETE("/gpu_type/delete", mw.ALR(true, Adm, Ops), ctrl.GpuType.Delete)                          // 删除gpu号型
	admin.POST("/gpu_type/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.GpuType.List)                     // 获取gpu号型列表
	admin.POST("/gpu_type/check_gpu_name", mw.ALR(false, Adm, Ops), ctrl.GpuType.CheckGpuTypeName)         // 检查gpu号型名称
	admin.POST("/gpu_type/check_gpu_occupation", mw.ALR(false, Adm, Ops), ctrl.GpuType.CheckGpuOccupation) // 检查gpu是否被占用

	// 订单
	admin.POST("/order", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.AdminGetOrderList) // 订单列表
	admin.GET("/order", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.OrderGetDetail)     // 订单详情

	// 账单
	admin.POST("/bill", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.AdminGetBillList)                                         // 账单列表
	admin.GET("/bill/confirm", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.BillConfirm)                                       // 查询是否到账
	admin.POST("/bill/info", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.BillInfo)                                            // 账单信息（分表后查问题用）
	admin.POST("/bill/daily_bill_statement/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.AdminGetDailyBillStatementList) // 按日的账单明细
	admin.GET("/bill/warning", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.BillGetWarningInfo)                                // 有问题账单，暂时废弃

	admin.POST("/contract", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.ContractCreate)                              // 创建合同
	admin.POST("/contract/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractGetList)                  // 获取合同列表
	admin.PUT("/contract", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractUpdate)                          // 编辑合同
	admin.PUT("/contract/status", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractUpdateStatus)             // 更新合同状态
	admin.PUT("/contract/file/upload", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractFileUpload)          // 上传合同文件
	admin.POST("/contract_bill", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.ContractBillCreate)                     // 发起账单
	admin.POST("/contract_bill/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminGetContractBillList)         // 获取账单列表
	admin.PUT("/contract_bill", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractBillUpdate)                 // 编辑账单
	admin.PUT("/contract_bill/status", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractBillUpdateStatus)    // 更新账单状态
	admin.PUT("/contract_bill/file/upload", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Contract.AdminContractBillFileUpload) // 上传账单文件、发票文件

	// 钱包
	admin.GET("/wallet", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.WalletAdminGet)                        // 获取用户账户信息
	admin.POST("/wallet/recharge", mw.ALR(true), ctrl.BC.WalletAdminRecharge)                              // 充值
	admin.POST("/wallet/withdraw", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.WalletAdminWithdraw)          // 提现
	admin.GET("/wallet/withdraw/most", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.WalletAdminWithdrawMost) // 提现
	admin.PUT("/bank_transfer", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.BTUpdate)
	admin.POST("/bank_transfer/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.BTList)
	admin.GET("/wallet/refundable", mw.ALR(false, BCS, Fnc), ctrl.BC.GetRefundableList)                // 获取可退款列表
	admin.POST("/wallet/refund/list", mw.ALR(false, BCS, Fnc), ctrl.BC.GetRefundList)                  // 获取全部退款列表
	admin.POST("/wallet/refund", mw.ALR(true, BCS, Fnc), ctrl.BC.CreateRefund)                         // 申请退款
	admin.GET("/wallet/amount/detail", mw.ALR(false, BCS, Fnc), ctrl.BC.AdminGetUserInvoiceAmountInfo) // 获取最大可提现金额
	admin.POST("/wallet/refund/delay", mw.ALR(true, BCS, Fnc), ctrl.BC.CreateRefundDelay)              // 延迟提现退款

	// 代金券
	admin.POST("/voucher", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherAdd)                                         // 新增代金券
	admin.POST("/voucher/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherGetList)                               // 获取代金券列表
	admin.PUT("/voucher", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherUpdate)                                       // 编辑代金券
	admin.GET("/voucher", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherGetDetail)                                   // 获取代金券详情
	admin.PUT("/voucher/close", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherClose)                                  // 手动截止代金券的发放
	admin.POST("/voucher/issue/appointment", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherIssueAppointmentTicket)    // 手动截止代金券的发放
	admin.POST("/voucher/issue/cashback", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherIssueRechargeCashbackTicket)  // 手动截止代金券的发放
	admin.POST("/user_voucher/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherUserAdminGetList)                 // 获取某一张代金券领取记录（包括未领取券）
	admin.POST("/user_voucher/claimed/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.VoucherClaimedUserAdminGetList)  // 获取代金券领取记录
	admin.POST("/user_voucher/revoke", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.AdminRevokeUserVoucher)                // 作废用户代金券
	admin.POST("/user_voucher/unclaimed/create", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.AdminCreateUnclaimedVoucher) // 创建未领取券

	// 优惠券
	admin.POST("/coupon", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CouponCreate)                            // 创建优惠券
	admin.GET("/coupon", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CouponDetail)                            // 优惠券详情
	admin.PUT("/coupon", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CouponUpdate)                             // 更新优惠券
	admin.POST("/coupon/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CouponList)                        // 优惠券列表
	admin.POST("/coupon/issue", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CouponIssueByAdmin)                // 管理员发放优惠券
	admin.POST("/coupon/receive_record", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CouponReceiveRecordList) // 优惠券领取历史
	admin.POST("/coupon/un_receive", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CouponCreateUnReceive)       // 生成未领取券（有兑换码）

	// 授信
	admin.POST("/credit_wallet/create", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CreditWalletCreate)
	admin.POST("/credit_wallet/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CreditWalletGetList)
	admin.POST("/credit_wallet/adjustment", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CreditWalletAdjustmentCreditLimit)
	admin.POST("/credit_wallet/repay", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.BC.CreditWalletRepay)
	admin.POST("/credit_wallet/history", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.BC.CreditWalletHistoryGetListForAdmin)

	// 发票
	admin.POST("/invoice/list", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.Invoice.AdminUserInvoiceList)                // 获取所有用户发票列表
	admin.POST("/invoice/confirm", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Invoice.AdminAgreeUserInvoice)             // 通过用户发票
	admin.POST("/invoice/reject", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Invoice.AdminRejectUserInvoice)             // 拒绝用户发票
	admin.POST("/invoice/express/update", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Invoice.AdminSetInvoiceExpressInfo) // 更新用户发票快递单号
	admin.POST("/invoice/detail", mw.ALR(false, Adm, Fnc, TCS, BCS), ctrl.Invoice.AdminGetUserInvoiceDetail)         // 获取用户发票明细(欠票,订单和日结账单记录)
	admin.POST("/invoice/offline", mw.ALR(true, Adm, Fnc, TCS, BCS), ctrl.Invoice.InvoiceCreateByAdmin)              // 后台提交线下发票
	admin.POST("/invoice/revoke", mw.ALR(true, Adm, TCS, BCS, Fnc), ctrl.Invoice.AdminSetUserInvoiceRevoke)          // 发票作废
	admin.GET("/invoice/amount/detail", mw.ALR(false, BCS, Fnc), ctrl.BC.AdminGetUserInvoiceAmountInfo)              // 暂留 获取最大可提现金额

	// 实例
	admin.POST("/instance", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.AdminInstance.GetPaged)                         // admin list instance
	admin.POST("/instance/start", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.AdminInstance.StartInstance)               // admin start instance
	admin.POST("/instance/stop", mw.ALR(true), ctrl.AdminInstance.StopInstance)                                     // admin stop instance
	admin.POST("/instance/release", mw.ALR(true), ctrl.AdminInstance.RemoveInstance)                                // admin release instance
	admin.GET("/instance/snapshot", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.AdminInstance.Snapshot)                 // admin snapshot
	admin.PUT("/instance/settings", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.AdminInstance.UpdateAdminSettings)       // admin update cpu, mem, disk, port
	admin.GET("/instance/settings", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.AdminInstance.GetAdminSettings)         // admin get cpu, mem, disk, port
	admin.POST("/instance/operation", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.AdminInstance.InstanceOperateHistory) // admin get instance operate history
	admin.POST("/instance/migrate", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.AdminInstance.InstanceMigrateManually)   // admin migrate instance
	admin.POST("/instance/clone", mw.ALR(true, Adm), ctrl.AdminInstance.InstanceClone)                              // admin clone instance
	admin.POST("/instance/clone/stop", mw.ALR(true, Adm), ctrl.AdminInstance.InstanceCloneStop)                     // admin clone instance

	// 地区
	admin.GET("/region", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Region.GetRegionDetail)         // 获取地区详情
	admin.POST("/region/create", mw.ALR(true, Adm, Ops), ctrl.Region.CreateRegion)               // 创建地区
	admin.GET("/region/list", mw.ALR(false, Adm, Ops, Fnc, TCS, BCS), ctrl.Region.GetRegionList) // 获取地区列表

	// 网盘
	admin.GET("/net_disk/quota", mw.ALR(false, Adm, TCS, BCS), ctrl.Region.UserNetDiskQuota) // 管理员获取用户网盘

	// 公共数据集
	admin.POST("/common_data/create", mw.ALR(true, Adm), ctrl.CommonData.CommonDataCreate)   // admin create common data
	admin.PUT("/common_data/update", mw.ALR(true, Adm), ctrl.CommonData.CommonDataUpdate)    // admin update common data
	admin.DELETE("/common_data/delete", mw.ALR(true, Adm), ctrl.CommonData.CommonDataDelete) // admin delete common data
	admin.GET("/common_data/list", mw.ALR(false, Adm), ctrl.CommonData.CommonDataAdminList)  // admin get common date list

	// 公共数据
	admin.POST("/public_data/review", mw.ALR(true, Adm), ctrl.PublicData.Review)
	admin.POST("/public_data/list", mw.ALR(false, Adm), ctrl.PublicData.AdminGetList)

	// 系统通知
	admin.POST("/sys_notice", mw.ALR(true, Adm), ctrl.SysNotice.Create)
	admin.GET("/sys_notice", mw.ALR(false, Adm), ctrl.SysNotice.Get)
	admin.POST("/sys_notice/list", mw.ALR(false, Adm), ctrl.SysNotice.GetList)

	// 弹性部署
	admin.POST("/deployment/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Deployment.DeploymentListAdmin)
	admin.PUT("/deployment/operate", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.Deployment.DeploymentStatusOperate)             // operate
	admin.GET("/deployment", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Deployment.DeploymentDetail)                           // detail
	admin.POST("/deployment/container/list", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Deployment.DCList)                     // list instance
	admin.PUT("/deployment/container/stop", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.Deployment.DCPowerOff)                   // list instance
	admin.GET("/deployment/container/off_reason", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Deployment.DCOffReason)           // 停止原因
	admin.GET("/deployment/ddp/overview", mw.ALR(false, Adm, Ops, TCS, BCS), ctrl.Deployment.DeploymentDurationPkgOverview) // 时长包概览

	// 文件存储
	admin.POST("/file_storage/list", mw.ALR(false, Adm, TCS, BCS), ctrl.Region.AdminFileStorageList)
	admin.POST("/file_storage/update_setting", mw.ALR(true, Adm, TCS, BCS), ctrl.Region.AdminUpdateFileStorageSetting)

	// 高速文件存储 High-Speed FileStorage
	admin.POST("/hsfs/list", mw.ALR(true, Adm, TCS, BCS), ctrl.Region.HSFSAdminGetList)
	admin.POST("/hsfs/setting", mw.ALR(true, Adm, TCS, BCS), ctrl.Region.HSFSAdminUpdateSetting)
	admin.POST("/hsfs/force_init", mw.ALR(true, Adm, TCS, BCS), ctrl.Region.HSFSAdminForceInit)

	// 专用nfs
	admin.POST("/exclusive_nfs/list", mw.ALR(false, Adm, TCS, BCS), ctrl.Region.ExclusiveNfsList)
	admin.POST("/exclusive_nfs/info", mw.KVRequired(), ctrl.Region.ExclusiveNfsInfo)

	// 发送的通知列表
	admin.POST("/notify/list", mw.ALR(false, Adm, TCS, BCS), ctrl.Notify.AdminUserNotifyList) //获取短信、微信消息发送列表

	// 其他 留后门的操作
	tool := admin.Group("/tools")
	{
		tool.GET("/refresh_user_cache", mw.ALR(true), ctrl.User.RefreshUserCache)
		tool.PUT("/net_disk/set_quota", mw.ALR(true, Adm), ctrl.Region.NetDiskSetQuota) // 管理员单独为用户设置网盘容量
		tool.POST("/net_disk/renewal", mw.ALR(true, Adm), ctrl.BC.AdminCreateOrderForNetDiskRenewal)
		tool.POST("/net_disk/expand", mw.ALR(true, Adm), ctrl.BC.AdminCreateOrderForNetDiskExpand)
		tool.POST("/offset_balance", mw.ALR(true), ctrl.BC.AdminOffsetBalance)
		tool.POST("/identity/transfer", mw.ALR(true, Adm, TCS), ctrl.User.AdminIdentityTransfer)
		tool.POST("/instance/stop", mw.ALR(true, Adm, TCS), ctrl.AdminInstance.StopInstance)
		tool.POST("/instance/force/stop", mw.ALR(true, Adm, TCS), ctrl.AdminInstance.InstanceForceStop)
		tool.POST("/instance/release/delay", mw.ALR(true, Adm, TCS, BCS), ctrl.AdminInstance.InstanceReleaseDelay)
		tool.POST("/user/clone/set_num", mw.ALR(true, Adm, TCS, BCS), ctrl.User.UserCloneNumUpdate)               // 更新拷贝和克隆次数，指定是否释放
		tool.POST("/user/instance/limit", mw.ALR(true, Adm, TCS, BCS), ctrl.User.SetUserInstanceNumLimit)         // 设置用户最大实例数限制
		tool.GET("/instance", mw.ALR(false, Adm, Ops), ctrl.AdminInstance.GetMachineErrInstanceList)              // 获取ErrMachine的非无卡运行中实例
		tool.POST("/send/sms", mw.ALR(true, Adm, Ops), ctrl.Notify.SendSms)                                       // 发短信
		tool.POST("/deployment/analyse", mw.ALR(false, Adm, TCS, Ops), ctrl.Deployment.DeploymentScheduleAnalyse) // 弹性部署调度分析
		tool.POST("/charge/pre", mw.ALR(true, Adm, BCS), ctrl.BC.AdminFixPreCharge)                               // admin 解决预充值回填钱包余额问题
		tool.GET("/charge/pre", mw.ALR(false, Adm, BCS), ctrl.BC.AdminGetPreChargeList)                           // admin 获取预充值记录
		tool.POST("/instance/ban", mw.ALR(true, Adm), ctrl.AdminInstance.BanPubRemoveCmdToContainer)              // admin 禁止此轮释放实例定时任务
		tool.POST("/instance/run", mw.ALR(true, Adm), ctrl.AdminInstance.RunPubRemoveCmdToContainer)              // admin 解除禁止释放实例定时任务
		tool.POST("/instance/cache/remove", mw.ALR(true, Adm), ctrl.AdminInstance.AdminAssignRemoveInstanceCache) // admin 释放实例缓存
		tool.POST("/trans", mw.ALR(true, Adm, TCS, BCS), ctrl.BC.AdminTransInsPrePay)                             // admin 转移包卡
		tool.GET("/trans/list", mw.ALR(true, Adm, TCS, BCS), ctrl.User.AdminGetTransInsPrePayList)                // admin 获取转移包卡订单记录
		tool.POST("/instance/recovery", mw.ALR(true, Adm, TCS, BCS), ctrl.AdminInstance.AdminInstanceRecovery)    // admin 恢复实例
		tool.GET("/machine/source", mw.ALR(true, Adm), ctrl.Machine.AdminGetMachineExistSource)                   // admin 获取主机上仍存在的资源
		tool.POST("/machine/operate", mw.ALR(true), ctrl.Machine.AdminOperateMachine)                             // admin 主机操作

		// 抽奖
		tool.POST("prize/add_whitelist", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.User.AddUserPrizeDrawWhitelist) // 添加审核白名单
		tool.POST("prize/cal_win_num", mw.ALR(true, Adm, Ops, TCS, BCS), ctrl.User.SetWinNum)                   // 计算中奖号码
	}
	return
}
