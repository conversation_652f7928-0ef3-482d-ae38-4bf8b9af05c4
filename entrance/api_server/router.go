package api_server

import (
	"server/conf"
	"server/entrance/initialize"
	serverApi "server/pkg/api"
	"server/pkg/docs"
	"server/pkg/libs"
	"server/pkg/libs/controller"
	"server/pkg/middleware"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

/**
 * 需要确保在执行此步骤之前执行完成 pkg 的互相依赖的初始化.
 */

func initRouter(r *gin.Engine, ctrl *initialize.ControllerFactory, mw *middleware.Middleware) {
	// 全局应用RequestID中间件（必须在最前面）
	r.Use(mw.RequestIDMiddleware())

	r.GET("/health", func(context *gin.Context) {
		context.JSON(200, map[string]string{
			"status": "green",
			"code":   "ok",
		})
	})
	internalApi := r.Group("")
	internalApi.Use(mw.KVRequired())
	{
		internalApi.GET(serverApi.GetSubUserSPACPath, ctrl.User.SubUserSPAC)
	}

	api := r.Group("/api/v1")
	api.Use(mw.IPAccessSpeedLimiter())
	{
		if conf.GetGlobalGsConfig().App.DocsApi {
			api.GET("/swagger/*any", docs.SwaaggerRouter)
		}

		api.GET("/version", func(context *gin.Context) {
			context.JSON(200, libs.VersionMap)
		})

		api.POST("/register", ctrl.User.Register)         // 用户自己注册
		api.POST("/register/invited", ctrl.User.Register) // 用户被邀请注册
		{
			// 登录接口汇总
			api.POST("/login", ctrl.User.Login)                          // 1.手机号密码登录
			api.POST("/new_login", ctrl.User.NewLogin)                   // 2.手机号密码登录(错误三次跳验证码)
			api.POST("/fast_login", ctrl.User.FastLogin)                 // 3.短信登录
			api.GET("/wx/login", ctrl.User.WXLogin)                      // 4.用户微信登录
			api.Any("/wx/callback", ctrl.User.WxCallBack)                // 4.1 微信扫码回调
			api.POST("/wx/polling/query", ctrl.User.WxLoginPollingQuery) // 4.2 轮循查询微信登录
			api.POST("/wx/phone/bind", ctrl.User.WxPhoneBind)            // 4.3 微信登录时绑定手机号
			api.POST("/wx/applet/phone", ctrl.User.GetUserPhoneByCode)   // 5. 微信小程序通过手机号进行登录
			api.POST("/login_failed/count", ctrl.User.LoginFailedCount)  // 6. 用户登录错误次数
			api.POST("/passport", ctrl.User.Passport)                    // ticket换token, 所有的登录行为的最后一步都走到了这里
		}
		api.PUT("/password/reset", ctrl.User.ResetPassword)           // 用户重置密码
		api.POST("/simply/send_message", ctrl.User.SimplySendMessage) // 不需要滑块验证发送短信
		api.POST("/send_message", ctrl.User.SendVCode)                // 发送短信
		api.POST("/send_voice_message", ctrl.User.SendVoiceCode)      // 发送语音验证码
		api.POST("/message/check", ctrl.User.CheckVCode)              // 校验短信验证码
		//api.POST("/send_email", ctrl.User.SendEmail)                                  // 发送邮件
		api.POST("/email/check", ctrl.User.CheckEmail)                                // 校验邮件验证码
		api.GET("/login/logo", ctrl.User.GetLoginLogo)                                // 登录logo
		api.GET("/captcha/block_puzzle", ctrl.User.GetBlockPuzzleCaptcha)             // 获取拼图验证码
		api.POST("/captcha/check", ctrl.User.CaptchaCheck)                            // 验证码校验
		api.GET("/captcha", ctrl.User.GetCaptcha)                                     // 获取验证码
		api.GET("/time_sync", ctrl.User.SyncTime)                                     // 时钟
		api.GET("/user/image_file/:feed_back_id", ctrl.User.GetUserFeedBackImageFile) // 获取用户问题截图
		api.GET("/common", ctrl.Common.Common)
		api.GET("/phone_area", ctrl.Common.PhoneAreaGetAll)
		api.Any("/wechat/message/push", ctrl.User.WechatMessagePush)                           // 微信消息推送
		api.POST("/wechat/message/send", mw.PersonalTokenCheck(), ctrl.User.WechatMessageSend) // 微信消息推送，token通过Authorization Header请求
		api.GET("/promotion/invite", ctrl.Promotion.InvitePromotion)
		api.GET("/certification/result", ctrl.User.UserCertificationResult) // 回调

		if isDebug := conf.GetGlobalGsConfig().App.DebugApi; isDebug {
			log.Info("Open debug apis.")
			debug := api.Group("/debug")
			{
				debug.GET("/captcha/get", ctrl.User.DebugGetCaptchaIDAndValue)
			}
		}

		file := api.Group("/file")
		file.Use(mw.LoginRequired())
		{
			// 小文件上传，图片，头像
			file.POST("", ctrl.File.Upload)
			api.GET("/file/:file", ctrl.File.Download)
		}

		user := api.Group("/user")
		user.Use(mw.LoginRequired())
		{
			user.POST("/logout", ctrl.User.Logout)                                 // 用户退出登录
			user.PUT("/password/update", ctrl.User.UpdatePassword)                 // 用户修改密码
			user.PUT("/nickname/update", ctrl.User.UpdateNickName)                 // 用户修改昵称
			user.PUT("/phone", ctrl.User.ChangePhone)                              // 用户修改手机号
			user.GET("/wechat/bind", ctrl.User.WeChatBind)                         // 用户绑定微信
			user.DELETE("/wechat", ctrl.User.WeChatUnbind)                         // 用户绑定微信
			user.GET("/wechat/bind/click", ctrl.User.WechatBindClick)              // 用户点击我已关注
			user.GET("/wechat/bind/open_id/fake", ctrl.User.WechatBindClickGpuHub) // 用户点击我已关注
			user.POST("/email/bind/exist", ctrl.User.CheckEmailExist)              // 判断邮箱是否已被绑定
			user.POST("/email/send", ctrl.User.SendEmailCode)                      // 发送 邮箱绑定的 验证码
			user.POST("/email/bind", ctrl.User.BindEmail)                          // 用户绑定邮箱
			user.POST("/email/update", ctrl.User.UpdateEmail)                      // 用户修改邮箱
			user.GET("/detail", ctrl.User.GetDetail)                               // 获取用户详情
			user.POST("/name/update", ctrl.User.UpdateUserName)                    // 用户修改名称
			user.POST("/feedback", ctrl.User.UserFeedBack)                         // 用户反馈与建议
			user.GET("/invite/detail", ctrl.User.GetUserInviteDetail)              // 获取用户邀请有礼详情
			user.GET("/growth_value", ctrl.User.GetGrowthValueList)                // 用户查看成长值获取记录
			user.POST("/upgrade/click", ctrl.User.ClickUpgrade)                    // 用户点击升级
			user.GET("/level", ctrl.User.GetUserLevel)                             // 用户获取会员等级
			user.POST("/machine/list", ctrl.User.GetUserMachineList)               // 用户获取机器列表
			user.POST("/uuid/detail", ctrl.User.GetUserByUUID)                     // 通过uuid获取用户
			user.POST("/uuid/username", ctrl.User.GetUserNameByUUID)               // 通过uuid获取用户昵称

			user.POST("/machine/follow", ctrl.User.FollowMachine)                  // 订阅空闲 GPU
			user.POST("/machine/unfollow", ctrl.User.UnfollowMachine)              // 取消订阅空闲 GPU
			user.GET("/followed_machine/list", ctrl.User.GetFollowedMachineList)   // 订阅空闲 GPU 列表
			user.GET("/followed_machine/count", ctrl.User.GetFollowedMachineCount) // 订阅空闲 GPU 数
			user.PUT("/container_monitor", ctrl.User.ContainerMonitorPushGatewaySet)
			user.PUT("/container_monitor/status", ctrl.User.ContainerMonitorStatusSet)
			user.GET("/container_monitor", ctrl.User.ContainerMonitorGet)

			user.POST("/pub_key", ctrl.User.CreatePubKey)
			user.GET("/pub_key", ctrl.User.GetPubKeyList)
			user.DELETE("/pub_key", ctrl.User.DeletePubKey)

			user.POST("/autopanel_token", ctrl.User.CreateAutopanelToken)
			user.GET("/autopanel_token", ctrl.User.GetAutopanelToken)
			user.DELETE("/autopanel_token", ctrl.User.DeleteAutopanelToken)

			user.POST("/prize/draw", ctrl.User.UserPrizeDraw)          // 用户抽奖
			user.GET("/prize/get", ctrl.User.CheckUserPrizeDrawType)   // 用户获取抽奖信息
			user.POST("/prize/draw_new", ctrl.User.UserPrizeDrawNew)   // 用户抽奖（对于4080）
			user.GET("/prize/get_new", ctrl.User.GetUserPrizeDrawType) // 用户获取抽奖信息(对于4080)
			user.GET("/prize/get_win_num", ctrl.User.GetWinNum)        // 获取中奖号码(对于4080)

			user.POST("/education_email/check", ctrl.User.UserCertificationStudent)       // 教育邮箱认证
			user.POST("/student/verification", ctrl.User.StudentVerification)             // 学生认证发邮箱
			user.POST("/education_email/check/exist", ctrl.User.CheckEducationEmailExist) // 校验教育邮箱
			user.GET("/member/detail", ctrl.User.UserMemberInfo)                          // 获取用户会员详情

			user.POST("/personal/token/create", ctrl.User.UserPersonalTokenCreate) // 用户创建个人token
			user.POST("/personal/token/delete", ctrl.User.UserPersonalTokenDelete) // 用户删除个人token
			user.GET("/personal/token/list", ctrl.User.UserPersonalTokenList)      // 用户token列表

			user.POST("/wx/applet/openId", ctrl.User.GetUserOpenIdByCode) // 获取用户微信小程序openID

			user.POST("/sub_user", ctrl.User.SubUserCreate)
			user.GET("/sub_user/list", ctrl.User.SubUserGetList)
			user.GET("/sub_user", ctrl.User.SubUserGetDetail)
			user.DELETE("/sub_user", ctrl.User.SubUserDelete)
			user.PUT("/sub_user/password", ctrl.User.SubUserPasswordChange)
			user.PUT("/sub_user/phone", ctrl.User.SubUserPhoneBindParams)
			user.PUT("/sub_user/payment_method", ctrl.BC.SubUserWalletPaymentMethodUpdate)
			user.POST("/sub_user/recharge", ctrl.BC.SubUserWalletRecharge)
			user.POST("/sub_user/withdraw", ctrl.BC.SubUserWithdraw)
			user.PUT("/sub_user/roles", ctrl.User.SubUserRolesUpdate)
			user.POST("/sub_user/insufficient/update", ctrl.User.SubUserInInSufficientBalanceShutdownUpdate)

			user.POST("/setting", ctrl.User.UpdateSetting)
			user.GET("/setting", ctrl.User.GetSetting)

			user.GET("/agreements", ctrl.User.UserAgreementSignedGet)
			user.POST("/agreements", ctrl.User.UserAgreementSign)
			user.POST("/certification", ctrl.User.UserCertificationCreate)
			user.GET("/certification", ctrl.User.UserCertificationDetail)
			user.PUT("/certification/revoke", ctrl.User.UserCertificationRevoke)
			user.POST("/certification/repeal", ctrl.User.UserCertificationRepeal)

			user.POST("/certification/query", ctrl.User.UserFaceCertifyQuery)              // 轮询
			user.POST("/certification/alipay/query", ctrl.User.UserFaceCertifyAlipayQuery) // 主动查询
			user.POST("/notify/retry", ctrl.Notify.UpdateRetrySendAliMessage)
			user.POST("/send_message/inner", ctrl.User.SendVCodeInner)            // 发送短信（替换手机号为uid）
			user.POST("/send_voice_message/inner", ctrl.User.SendVoiceVCodeInner) // 发送语音验证码（替换手机号为uid）
			user.GET("get_login_record", ctrl.User.GetLoginRecord)

		}

		bc := api.Group("")
		bc.Use(mw.LoginRequired())
		{
			// order
			bc.POST("/order/list", ctrl.BC.OrderGetList)                                                                  // 获取订单列表
			bc.GET("/order", ctrl.BC.OrderGetDetail)                                                                      // 获取订单详情
			bc.POST("/order/price/preview", ctrl.BC.GetPricePreview)                                                      // 获取订单详情
			bc.PUT("/order/pay", ctrl.BC.OrderPay)                                                                        // 付款
			bc.PUT("/order/cancel", ctrl.BC.OrderCancel)                                                                  // 取消
			bc.POST("/order/instance/create/prepay", ctrl.BC.CreateOrderForCreateInstancePrepay)                          // 创建实例
			bc.POST("/order/instance/create/payg", ctrl.BC.CreateOrderForCreateInstancePayg)                              // 创建实例
			bc.POST("/order/instance/clone/prepay", ctrl.BC.CreateOrderForCloneInstancePrepay)                            // 迁移实例预付费
			bc.POST("/order/instance/clone/payg", ctrl.BC.CreateOrderForCloneInstancePayg)                                // 实例迁移
			bc.POST("/order/instance/renewal", ctrl.BC.CreateOrderForRenewalInstance)                                     // 实例续费
			bc.POST("/order/instance/change", ctrl.BC.CreateOrderForChangeInstance)                                       // 实例调整配置
			bc.POST("/order/data_disk/change", ctrl.BC.CreateOrderForDataDiskChangeSize)                                  // 数据盘扩缩容
			bc.POST("/order/data_disk/change/preview", ctrl.BC.DataDiskChangeSizePreview)                                 // 数据盘扩缩容 - 预览
			bc.POST("/order/instance/charge_type/prepay", ctrl.BC.CreateOrderForPaygToPrepay)                             // 按量付费转包卡
			bc.GET("/order/instance/charge_type/payg/preview", ctrl.BC.CreateOrderForPrepayToPaygPreview)                 // 包卡转按量付费 - 预览
			bc.GET("/order/instance/charge_type/check", ctrl.BC.CheckForChangeChargeType)                                 // 包卡转按量付费 - 预览
			bc.POST("/order/instance/charge_type/payg", ctrl.BC.CreateOrderForPrepayToPayg)                               // 包卡转按量付费 - 执行
			bc.POST("/order/net_disk/expand", ctrl.BC.CreateOrderForNetDiskExpand)                                        // 网盘扩容
			bc.POST("/order/net_disk/renewal", ctrl.BC.CreateOrderForNetDiskRenewal)                                      // 网盘续费
			bc.POST("/order/deployment/ddp/preview", ctrl.BC.CreateOrderForDeploymentDuration)                            // 创建弹性部署时长包 - 预览
			bc.POST("/order/deployment/ddp", ctrl.BC.CreateOrderForDeploymentDuration)                                    // 创建弹性部署时长包
			bc.DELETE("/order/deployment/ddp/preview", ctrl.BC.CreateOrderForCancelDeploymentDurationRefund)              // 退订弹性部署时长包 - 预览
			bc.DELETE("/order/deployment/ddp", ctrl.BC.CreateOrderForCancelDeploymentDurationRefund)                      // 退订弹性部署时长包
			bc.DELETE("/order/deployment/ddp/single/preview", ctrl.BC.CreateOrderForCancelDeploymentDurationRefundSingle) // 退订单个弹性部署时长包 - 预览
			bc.DELETE("/order/deployment/ddp/single", ctrl.BC.CreateOrderForCancelDeploymentDurationRefundSingle)         // 退订单个弹性部署时长包

			// bill
			bc.POST("/bill/list", ctrl.BC.BillGetList)   // 账单列表（账单明细）
			bc.GET("/bill/confirm", ctrl.BC.BillConfirm) // 查看账单是否确认
			bc.GET("/bill", ctrl.BC.BillGet)             // 查看账单详情/bill

			bc.POST("/bill/balance_statement/list", ctrl.BC.GetBalanceStatementList)      // 收支明细
			bc.POST("/bill/daily_bill_statement/list", ctrl.BC.GetDailyBillStatementList) // 按日的账单明细
			bc.GET("/bill/daily_bill_statement/export", ctrl.BC.DailyBillStatementExport) //日结账单导出csv

			// wallet
			bc.GET("/wallet", ctrl.BC.WalletGet)                                           // 钱包详情, 余额
			bc.GET("/wallet/balance", ctrl.BC.WalletGetForCreateOrder)                     // 下单获取余额
			bc.GET("/wallet/recharge", ctrl.BC.RechargeQueryLocal)                         // 轮循查询
			bc.GET("/wallet/bank_transfer/exclusive_account", ctrl.BC.BTSubunitDistribute) // 生成汇款专属账号

			bc.POST("/wallet/recharge/wx", ctrl.BC.RechargeWxCreate)          // 充值
			bc.POST("/wallet/recharge/wx/v2", ctrl.BC.RechargeWxCreateV2)     // 充值
			bc.GET("/wallet/recharge/wx/query", ctrl.BC.RechargeWxQuery)      // 主动查询
			api.POST("/pay/weixin/callback", ctrl.BC.RechargeWxCallback)      // 微信支付回调
			api.POST("/pay/weixin/refund/callback", ctrl.BC.RefundWxCallback) // 微信退款回调

			bc.POST("/wallet/recharge/alipay", ctrl.BC.RechargeAliPayCreate)          // 充值
			bc.POST("/wallet/recharge/alipay/v2", ctrl.BC.RechargeAliPayCreateV2)     // 充值
			bc.GET("/wallet/recharge/alipay/query", ctrl.BC.RechargeAliQuery)         // 主动查询
			bc.GET("/wallet/recharge/alipay/qrcode", ctrl.BC.RechargeAliUpdateQrCode) // 主动查询
			api.POST("/pay/alipay/callback", ctrl.BC.RechargeAliCallback)             // 支付宝支付回调

			bc.POST("/wallet/recharge/wechat/applet", ctrl.BC.RechargeWechatAppletCreate)     // 微信小程序充值
			bc.GET("/wallet/recharge/wechat/applet/query", ctrl.BC.RechargeWechatAppletQuery) // 主动查询

			bc.POST("/wallet/recharge/credit_wallet", ctrl.BC.CreditWalletLoan)

			// voucher
			bc.POST("/voucher/list", ctrl.BC.VoucherUserGetList)                   // 获取代金券列表
			bc.POST("/voucher/issue/exchange", ctrl.BC.VoucherIssueExchangeTicket) // 用户领取兑换券
			bc.GET("/voucher/register", ctrl.BC.VoucherUserGetRegisterTicketInfo)  // 用户注册完成后查看获得的注册券信息
			bc.POST("/voucher/set", ctrl.BC.UserSetVoucherIsAutoUse)               // 用户设置代金券是否自动抵扣使用

			// net disk expansion
			bc.POST("/net_disk/expand", ctrl.BC.CreateOrderForNetDiskExpand)
			bc.POST("/net_disk/renewal", ctrl.BC.CreateOrderForNetDiskRenewal)

			// coupon
			bc.GET("/coupon_market", ctrl.BC.CouponListForCouponMarket)             // 优惠券市场
			bc.GET("/coupon", ctrl.BC.CouponDetail)                                 // 优惠券详情
			bc.GET("/coupon/receive", ctrl.BC.CouponReceiveByUser)                  // 用户领取优惠券
			bc.POST("/user_coupon", ctrl.BC.UserCouponList)                         // 用户优惠券列表
			bc.POST("/user_coupon/exchange", ctrl.BC.UserCouponIssueByExchangeCode) // 使用兑换码兑换优惠券

			// credit wallet
			bc.POST("/credit_wallet/history", ctrl.BC.CreditWalletHistoryGetListForUser)

			// contract bill
			bc.POST("/contract/list", ctrl.Contract.GetContractList)             // 获取合同列表
			bc.POST("/contract_bill/list", ctrl.Contract.GetContractBillList)    // 获取账单列表
			bc.POST("/contract_bill/confirm", ctrl.Contract.ConfirmContractBill) // 账单确认
		}

		deployment := api.Group("/deployment")
		deployment.Use(mw.LoginRequired(), mw.EnterpriseAuth())
		{
			deployment.POST("", ctrl.Deployment.DeploymentCreate)                          // create
			deployment.POST("/overview", ctrl.Deployment.DeploymentCreateOverview)         // create overview
			deployment.PUT("", ctrl.Deployment.DeploymentUpdate)                           // update
			deployment.PUT("/overview", ctrl.Deployment.DeploymentUpdateOverview)          // update overview
			deployment.GET("", ctrl.Deployment.DeploymentDetail)                           // detail
			deployment.GET("/overview", ctrl.Deployment.DeploymentOverview)                // detail overview
			deployment.POST("/list", ctrl.Deployment.DeploymentList)                       // list
			deployment.POST("/container/list", ctrl.Deployment.DCList)                     // list instance
			deployment.PUT("/container/power_off", ctrl.Deployment.DCPowerOff)             // list instance
			deployment.GET("/container/off_reason", ctrl.Deployment.DCOffReason)           // 停止原因
			deployment.PUT("/operate", ctrl.Deployment.DeploymentStatusOperate)            // operate
			deployment.PUT("/refresh", ctrl.Deployment.DeploymentRefresh)                  // refresh
			deployment.DELETE("", ctrl.Deployment.DeploymentDelete)                        // delete
			deployment.GET("/allocate", ctrl.Deployment.DeploymentGetListForAllocate)      // 获取弹性部署分配列表
			deployment.POST("/allocate", ctrl.Deployment.DeploymentAllocate)               // 弹性部署分配
			deployment.GET("/ddp/overview", ctrl.Deployment.DeploymentDurationPkgOverview) // 时长包概览
			deployment.POST("/ddp/list", ctrl.Deployment.GetDeploymentDurationList)        // 弹性部署时长包列表
			deployment.GET("/blacklist", ctrl.Deployment.GetDeploymentBlacklistWeb)        // 黑名单列表
			deployment.POST("/blacklist", ctrl.Deployment.DeploymentSetBlacklist)          // 设置黑名单
			deployment.POST("/blacklist/delete", ctrl.Deployment.DeploymentBlackDelete)    // 删除黑名单
		}

		dev := api.Group("/dev")
		dev.Use(mw.PersonalTokenCheck())
		{ // 钱包详情, 余额
			dev.POST("/wallet/balance", ctrl.BC.WalletGetForCreateOrderDev)                   // 下单获取余额
			dev.POST("/machine/region/gpu_stock", ctrl.Machine.GetMachineRegionGpuTypeForDev) // 获取区域内的机器gpu号型
			dev.POST("/image/private/list", ctrl.Deployment.PrivateImageList)

			deployment := dev.Group("/deployment").Use(mw.EnterpriseAuth())
			{
				deployment.POST("", ctrl.Deployment.DeploymentCreate)                          // create
				deployment.POST("/overview", ctrl.Deployment.DeploymentCreateOverview)         // create overview
				deployment.PUT("", ctrl.Deployment.DeploymentUpdate)                           // update
				deployment.PUT("/overview", ctrl.Deployment.DeploymentUpdateOverview)          // update overview
				deployment.GET("", ctrl.Deployment.DeploymentDetail)                           // detail
				deployment.GET("/list", ctrl.Deployment.DeploymentListForDev)                  // detail
				deployment.GET("/overview", ctrl.Deployment.DeploymentOverview)                // detail overview
				deployment.POST("/list", ctrl.Deployment.DeploymentListDev2)                   // list
				deployment.DELETE("", ctrl.Deployment.DeploymentDelete)                        // delete
				deployment.POST("/container/list", ctrl.Deployment.DCListForDev)               // list instance
				deployment.POST("/container/event/list", ctrl.Deployment.DCEventList)          // list instance
				deployment.PUT("/container/stop", ctrl.Deployment.DCPowerOff)                  // list instance
				deployment.PUT("/operate", ctrl.Deployment.DeploymentStatusOperate)            // operate
				deployment.PUT("/refresh", ctrl.Deployment.DeploymentRefresh)                  // refresh
				deployment.PUT("/replica_num", ctrl.Deployment.DeploymentSetReplicaNum)        // refresh
				deployment.POST("/blacklist", ctrl.Deployment.DeploymentSetBlacklist)          // set deployment blacklist
				deployment.GET("/ddp/overview", ctrl.Deployment.DeploymentDurationPkgOverview) // 时长包概览
				deployment.GET("/blacklist", ctrl.Deployment.DeploymentGetBlacklist)           // get deployment blacklist
			}
		}

		instance := api.Group("/instance")
		instance.Use(mw.LoginRequired())
		{
			// 注: 实例的创建只由订单成功来控制. 实例续费虽然在实例页面, 但应当访问 order 路由.
			instance.GET("", ctrl.Instance.GetPaged)                                    // 获取实例列表
			instance.POST("", ctrl.Instance.GetPaged)                                   // 获取实例列表
			instance.POST("/receipt/create", ctrl.WorkOrder.CreateInstanceReceipt)      // 生成回执
			instance.GET("/snapshot", ctrl.Instance.GetInstanceSnapshot)                // 获取实例的机器等详情 (快照)
			instance.GET("/detail", ctrl.Instance.GetInstanceDetail)                    // 获取实例的机器等详情
			instance.GET("/exist", ctrl.Instance.CheckInstanceExist)                    // 检查实例存在与否
			instance.POST("/power_on", ctrl.Instance.PowerOnInstance)                   // 开机
			instance.POST("/restart", ctrl.Instance.RestartInstance)                    // 开机
			instance.GET("/power_on/can_non_gpu", ctrl.Instance.CanPowerOnByNonGPUMode) // 查询开机限制
			instance.POST("/power_off", ctrl.Instance.PowerOffInstance)                 // 关机
			instance.POST("/release", ctrl.Instance.ReleaseInstance)                    // 释放
			instance.POST("/init", ctrl.Instance.InitInstance)                          // 初始化
			instance.POST("/change_image", ctrl.Instance.ChangeImage)                   // 更换镜像
			instance.POST("/image/save", ctrl.Instance.SaveImage)                       // 保存镜像
			instance.POST("/change_protocol", ctrl.Instance.ChangeProtocol)             // 修改协议

			instance.GET("/count", ctrl.Instance.CountInstance)                // 统计用户实例数量
			instance.GET("/count/v1", ctrl.Instance.CountUserInstanceInstance) // 统计用户实例数量排除了(abort状态的)
			//instance.GET("/clone/count", ctrl.Instance.CountCloneInstance)     // 统计用户迁移实例次数

			instance.PUT("/name", ctrl.Instance.UpdateInstanceName)               // 更新名称
			instance.PUT("/description", ctrl.Instance.UpdateInstanceDescription) // 更新描述 (提前写在这预防改需求)

			instance.POST("/timed/shutdown", ctrl.Instance.ScheduledShutdown) // 定时任务: 关机
			instance.GET("/monitor", ctrl.Instance.InstanceMonitor)           // 实例监控

			//instance.GET("/migrate/list", ctrl.Instance.InstanceMigrateList)    // 实例迁移历史
			instance.POST("/clone/retry", ctrl.Instance.CloneRetry)                         // 实例克隆重试
			instance.POST("/clone/file_transfer/done", ctrl.Instance.CloneFileTransferDone) // 实例克隆, 拷贝数据盘
			//instance.POST("/migrate/cancel", ctrl.Instance.InstanceCancelMigrate)  // 实例取消迁移
			instance.POST("/allocate", ctrl.Instance.InstanceAllocate)             // 分配给子帐号
			instance.GET("/allocate", ctrl.Instance.InstanceGetListForAllocate)    // 查看实例分配情况
			instance.GET("/notify/records", ctrl.Instance.GetNotifyWarningRecords) // 查看实例分配情况
			instance.POST("/file_transfer", ctrl.Instance.FileTransfer)
			instance.POST("/file_transfer/cancel", ctrl.Instance.FileTransferCancel)
			instance.POST("/file_transfer/list", ctrl.Instance.TransferInstanceList)
			instance.POST("/setting", ctrl.Instance.UpdateSetting)
			instance.PUT("/ssh_pwd", ctrl.Instance.SSHPwdUpdate)

			instance.POST("/unpayg/renewal", ctrl.Instance.GetUnExpireList) // 获取未到期包年包月实例列表

			instance.POST("/schedule/create", ctrl.Instance.CreateInstanceSchedule)                       // 调度创建实例
			instance.POST("/schedule/wait/list", ctrl.Instance.GetWaitingInstanceScheduleList)            // 调度中的实例列表
			instance.POST("/schedule/success/list", ctrl.Instance.GetSuccessInstanceScheduleList)         // 最近三天通过调度创建的实例列表
			instance.POST("/schedule/stop", ctrl.Instance.StopInstanceSchedule)                           // 取消实例调度
			instance.POST("/schedule/order/price/preview", ctrl.Instance.GetInstanceSchedulePricePreview) // 价格预览
			instance.POST("/schedule/is_send_msg/update", ctrl.Instance.UpdateInstanceIsSendMsgStatus)    // 发送短信状态更新
			instance.POST("/clone/get_clone_code", ctrl.Instance.GetCloneCode)                            //获取克隆码
			instance.POST("/check_show_clone", ctrl.Instance.CheckShowClone)                              //检查是否显示克隆码选项
			instance.POST("/clone/get_src_cuda", ctrl.Instance.GetSrcCuda)                                //获取源实例cuda版本

			instance.POST("/tag/list", ctrl.Instance.V2GetTagList)
			instance.POST("/tag", ctrl.Instance.V2AddTag)
			instance.POST("/tag/del", ctrl.Instance.V2DeleteTag)
		}

		machine := api.Group("/machine")
		machine.POST("/list", ctrl.Machine.List)                    // 云市场不需要token验证获取机器信息列表
		machine.GET("/gpu_type", ctrl.Machine.GetAllMachineGpuType) // 获取所有机器的gpu号型
		machine.GET("/tag", ctrl.Machine.GetMachineTag)             // 获取所有机器标签
		machine.Use(mw.LoginRequired())
		{
			machine.POST("/detail", ctrl.Machine.Detail)                           // 获取机器信息详情
			machine.POST("/simple/detail", ctrl.Machine.SimpleDetail)              // 检查机器简单信息
			machine.POST("/check_machine_online", ctrl.Machine.CheckMachineOnline) // 检查机器是否上架
			machine.POST("/region/gpu_type", ctrl.Machine.GetMachineRegionGpuType) // 获取区域内的机器gpu号型
			machine.POST("/search", ctrl.Machine.Search)                           // 简单的搜索接口，只根据字段查询正常的机器
		}

		gpuType := api.Group("/gpu_type")
		gpuType.POST("/list", ctrl.GpuType.List) // 前台不需要token验证 获取gpu信息列表

		image := api.Group("/image")
		image.Use(mw.LoginRequired())
		{
			// image.POST("/get", ctrl.Image.Get)                        // 获取筛选的镜像
			image.POST("/get", ctrl.Image.GetOnShelvesImages) // 改动: 仅能看到上架的

			// image.GET("/get_framework", ctrl.Image.GetImageFramework) // 获取机器学习的框架
			image.GET("/get_framework", ctrl.Image.GetOnShelvesImageFramework) // 改动: 仅能看到上架的

			// 新版前端下拉框, 暂时返回所有, 需要前端继续考虑分段拼接下拉框的可行性
			image.POST("/all", ctrl.Image.GetImageCompleteInfo)                              // 获取镜像完整信息
			image.POST("/private/list", ctrl.PrivateImage.List)                              // 获取用户镜像列表
			image.POST("/private/update", ctrl.PrivateImage.UpdateImageName)                 // 更新镜像名称
			image.DELETE("/private/delete", ctrl.PrivateImage.Delete)                        // 删除用户镜像
			image.POST("/private/check_name", ctrl.PrivateImage.CheckPrivateImageName)       // 校验用户镜像名称
			image.GET("/private/num", ctrl.PrivateImage.GetImageNum)                         // 获取用户镜像数量
			image.POST("/private/get", ctrl.PrivateImage.GetAll)                             // 获取用户所有镜像
			image.POST("/private/share", ctrl.PrivateImage.CreateShareImage)                 // 用户共享镜像
			image.POST("/private/share/cancel", ctrl.PrivateImage.CancelShareImage)          // 用户取消共享镜像
			image.POST("/private/share/user", ctrl.PrivateImage.GetShareImageUser)           // 获取共享镜像用户
			image.POST("/private/usage", ctrl.PrivateImage.GetUserUsageInfo)                 // 获取镜像使用信息
			image.GET("/private/allocate", ctrl.PrivateImage.PrivateImageGetListForAllocate) // 查看镜像分配情况
			image.POST("/private/allocate", ctrl.PrivateImage.PrivateImageAllocate)          // 分配给子账号

			image.GET("/codewithgpu", ctrl.PrivateImage.CodeWithGpuValidImage)
			image.POST("/codewithgpu/list", ctrl.PrivateImage.CodeWithGpuGetImageList)
			image.POST("/codewithgpu/popular", ctrl.PrivateImage.CodeWithGpuGetImagePopularList)
		}

		// 公网镜像，只对image_worker服务
		publicImage := api.Group("/public_image")
		{
			publicImage.GET("", ctrl.PrivateImage.PublicImageGetWaitingForWorker) // 查看是否有待完成的任务
			publicImage.POST("", ctrl.PrivateImage.PublicImageWorkerPostStatus)   // 进行中的任务返回进度
		}

		api.GET("/example", controller.Example)

		netDisk := api.Group("/net_disk")
		netDisk.Use(mw.LoginRequired())
		{
			netDisk.POST("/init", ctrl.Region.InitUserNetDisk)      // 初始化网盘
			netDisk.GET("/list", ctrl.Region.GetNetDiskListForUser) // 获取用户网盘列表
		}

		fileStorage := api.Group("/file_storage")
		fileStorage.Use(mw.LoginRequired())
		{
			fileStorage.POST("/init", ctrl.BC.InitUserFileStorage)          // 初始化文件存储
			fileStorage.GET("/list", ctrl.Region.GetFileStorageListForUser) // 获取用户文件存储列表
			fileStorage.GET("/mount/list", ctrl.Region.FileStorageMountAccessList)
			fileStorage.POST("/mount", ctrl.Region.FileStorageMountCtrl)
			fileStorage.GET("/autofs/max_usage", ctrl.Region.AutoFsGetUsage)
		}

		region := api.Group("/region")
		{
			region.GET("/list", ctrl.Region.GetRegionListForIndex) // gpu市场筛选
		}

		invoice := api.Group("invoice")
		invoice.Use(mw.LoginRequired())
		{
			invoice.POST("/list", ctrl.Invoice.UserInvoiceList)              // 用户发票列表
			invoice.POST("/create", ctrl.Invoice.CreateUserInvoice)          // 创建用户发票
			invoice.POST("/detail", ctrl.Invoice.GetUserInvoiceDetail)       // 用户获取发票明细
			invoice.GET("/amount/detail", ctrl.Invoice.GetInvoiceAmountInfo) // 获取用户开票金额信息
			// invoice.GET("/arrear/detail", ctrl.Invoice.GetArrearInvoiceInfo)            // 用户获取所有欠票记录
			invoice.POST("/order/list", ctrl.Invoice.GetInvoiceOrderList)             // 用户获取所有能开发票的订单
			invoice.POST("/daily_bill/list", ctrl.Invoice.GetInvoiceDailyBillList)    // 用户获取所有能开发票的日结账单
			invoice.POST("/title_info/create", ctrl.Invoice.CreateInvoiceTitleInfo)   // 创建发票抬头信息
			invoice.POST("/title_info/update", ctrl.Invoice.UpdateInvoiceTitleInfo)   // 更新发票抬头信息
			invoice.POST("/title_info/list", ctrl.Invoice.InvoiceTitleInfoList)       // 发票抬头信息列表
			invoice.GET("/title_info/get", ctrl.Invoice.GetInvoiceTitleInfo)          // 获取发票抬头信息
			invoice.DELETE("/title_info/delete", ctrl.Invoice.DeleteInvoiceTitleInfo) // 删除发票抬头信息
			invoice.POST("/mail_info/create", ctrl.Invoice.CreateInvoiceMailInfo)     // 创建发票邮寄信息
			invoice.POST("/mail_info/update", ctrl.Invoice.UpdateInvoiceMailInfo)     // 更新发票邮寄信息
			invoice.POST("/mail_info/list", ctrl.Invoice.InvoiceMailInfoList)         // 发票邮寄信息列表
			invoice.GET("/mail_info/get", ctrl.Invoice.GetInvoiceMailInfo)            // 获取发票邮寄信息
			invoice.DELETE("/mail_info/delete", ctrl.Invoice.DeleteInvoiceMailInfo)   // 删除发票邮寄信息
			invoice.POST("/user_revoke", ctrl.Invoice.UserRevokeInvoice)              // 用户作废发票
		}

		commonData := api.Group("common_data")
		{
			commonData.GET("/list", ctrl.CommonData.CommonDataUserList) // 前台用户公共数据列表
		}

		publicData := api.Group("/public_data")
		publicData.Use(mw.LoginRequired())
		{
			api.GET("/public_data/list", ctrl.PublicData.RedisGetList)
			publicData.POST("/list", ctrl.PublicData.RedisGetListLogin)
			publicData.POST("", ctrl.PublicData.Create)
			publicData.DELETE("", ctrl.PublicData.Delete)
			publicData.PUT("", ctrl.PublicData.Update)
			publicData.GET("", ctrl.PublicData.Get)
			publicData.GET("/share", ctrl.PublicData.MyShare)
			publicData.PUT("/star", ctrl.PublicData.Star)
			publicData.GET("/star", ctrl.PublicData.MyStar)
			publicData.DELETE("/star", ctrl.PublicData.StarCancel)
			publicData.GET("/name_exist", ctrl.PublicData.NameCheck)
		}
		notice := api.Group("/sys_notice")
		notice.Use(mw.LoginRequired())
		{
			notice.PUT("", ctrl.SysNotice.Read)
			notice.GET("", ctrl.SysNotice.GetNotice)
		}

		// 控制台首页面板数据
		dashboard := api.Group("/dashboard")
		dashboard.Use(mw.LoginRequired())
		{
			dashboard.GET("/instance", ctrl.Instance.GetDashboardInstance)
			dashboard.GET("/storage", ctrl.Instance.GetDashboardStorage)
		}
		kv := api.Group("/internal/kv")
		kvAuth := kv.Group("")
		kvAuth.Use(mw.KVRequired())
		{
			kv.GET("/key/:key", ctrl.Kv.Get)
			kvAuth.POST("/key/:key", ctrl.Kv.Set)
		}

		ft := api.Group("/internal/ft")
		ft.Use(mw.KVRequired())
		{
			ft.POST("/status/:key", ctrl.FT.FTTaskStatusUpdate)
		}

		//// ADFS storage agent
		//storageAgent := api.Group("/storage")
		//{
		//	storageAgent.GET("/connect", ctrl.AgentGuard.StorageAgentRunReaderWriter)
		//	storageAgent.POST("/logs", ctrl.AgentGuard.StorageAgentSaveLogs)
		//
		//	// 通过复用 middleware 解析 token, 再发回去. 但需要考虑验证.
		//	storageAgent.Use(mw.LoginRequired())
		//	storageAgent.POST("/auth", ctrl.AgentGuard.StorageAgentAuthToken)
		//}

		// 售卖服务器
		api.POST("/server/sku/list", ctrl.Server.GetSKUList)
		api.POST("/server/sku/get", ctrl.Server.GetSKU)
		server := api.Group("", mw.LoginRequired())
		{
			server.POST("/server/cart_product/list", ctrl.Server.GetCartProductList)
			server.POST("/server/cart_product/get", ctrl.Server.GetCartProduct)
			server.POST("/server/cart_product/create", ctrl.Server.CreateCartProduct)
			server.POST("/server/cart_product/delete", ctrl.Server.DeleteCartProduct)
			server.POST("/server/cart_product/share", ctrl.Server.ShareCartProduct)
		}
		api.POST("/server/cart_product/get_shared", ctrl.Server.GetSharedCartProduct)

		// 给托管云服务调用的接口，也是公网调用。接口请求使用RSA签名（接口都是https，响应先不做加密）。
		// 这类接口未来如果其它服务需要调用的话，需要颁发唯一ID和对应的证书，就像微信支付那样，现在实现起来太复杂先不做了。
		hostingcloudApi := api.Group("hostingcloud")
		{
			hostingcloudApi.POST("/user/detail", ctrl.User.HostingcloudGetUserDetail)

			// 根据/sso/ticket接口返回的ticket验证登录
			hostingcloudApi.POST("/passport", ctrl.User.HostingcloudPassport)
		}

		sso := api.Group("/sso", mw.LoginRequired())
		{
			// 生成用于sso的ticket
			sso.POST("/ticket", ctrl.User.GenerateSSOTicket)
		}

		api.GET("/public_api/region/file_storage", ctrl.Region.PublicApiGetFileStorageList)
		api.POST("/public_api/region/file_storage/reformat", ctrl.Region.PublicApiReformatFileStorage)
		api.GET("/public_api/region/hsfs", ctrl.Region.PublicApiGetHSFSList)

		// 高速文件存储 High-Speed FileStorage
		hsfs := api.Group("")
		hsfs.Use(mw.LoginRequired())
		{
			// 获取支持高速文件存储的地区列表
			hsfs.GET("/hsfs/region/list", ctrl.Region.HSFSGetRegionList)

			// 初始化高速文件存储，预览
			hsfs.POST("/hsfs/init/preview", ctrl.Region.HSFSInitPreview)
			// 初始化高速文件存储（开通、购买、支付）
			hsfs.POST("/hsfs/init", ctrl.Region.HSFSInit)

			// 续费高速文件存储，预览
			hsfs.POST("/hsfs/renewal/preview", ctrl.Region.HSFSRenewalPreview)
			// 续费高速文件存储
			hsfs.POST("/hsfs/renewal", ctrl.Region.HSFSRenewal)

			// 扩容高速文件存储，预览
			hsfs.POST("/hsfs/expand/preview", ctrl.Region.HSFSExpandPreview)
			// 扩容高速文件存储
			hsfs.POST("/hsfs/expand", ctrl.Region.HSFSExpand)

			// 获取高速文件存储详情
			hsfs.GET("/hsfs/detail", ctrl.Region.HSFSGetDetail)
			// 更新高速文件存储配置
			hsfs.POST("/hsfs/setting", ctrl.Region.HSFSUpdateSetting)
		}
	}

	subUserRouter(api, ctrl, mw)
	adminRouter(r, ctrl, mw)
	return
}
