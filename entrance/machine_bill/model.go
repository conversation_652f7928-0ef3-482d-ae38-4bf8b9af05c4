package machine_bill

import "server/pkg/constant"

type machineInfo struct {
	machineName string
	regionSign  constant.RegionSignType
	gpuName     string
	gpuNum      int
}

type globalMachineInfo struct {
	MachineID   string                  `gorm:"type:varchar(255);column:machine_id;NOT NULL;" json:"machine_id"`
	RegionSign  constant.RegionSignType `gorm:"type:varchar(255);column:region_sign;" json:"region_sign"`
	MachineName string                  `gorm:"type:varchar(255);column:machine_name;NOT NULL;" json:"machine_name"`
	GpuName     string                  `gorm:"type:varchar(255);column:gpu_name;NOT NULL;" json:"gpu_name"`
	GpuNumber   int64                   `gorm:"type:bigint(20);column:gpu_number;" json:"gpu_number"`
}
