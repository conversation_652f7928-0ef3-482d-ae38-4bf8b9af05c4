package agent_server

import (
	"net/http"
	"server/entrance/initialize"
	"server/pkg/libs"

	"github.com/gin-gonic/gin"
)

func initRouter(r *gin.Engine, ctrl *initialize.CoreControllerFactory, mw *initialize.CoreMiddlewareFactory) {
	r.GET("/health", func(context *gin.Context) {
		context.JSON(http.StatusOK, map[string]string{"status": "green", "code": "ok"})
	})

	agent := r.Group("/agent/v1/")
	{
		// GPU machine agent
		agent.GET("connect", ctrl.AgentGuardController.RunReaderWriter)

		// NFS storage agent
		storageAgent := agent.Group("/storage")
		{
			storageAgent.GET("/connect", ctrl.AgentGuardController.StorageAgentRunReaderWriter)

			// 通过复用 middleware 解析 token, 再发回去. 但需要考虑验证.
			storageAgent.Use(mw.StorageAgentMiddleware.LoginRequired())
			storageAgent.POST("/auth", ctrl.AgentGuardController.StorageAgentAuthToken)
		}

		agent.GET("/version", func(context *gin.Context) {
			context.JSON(200, libs.VersionMap)
		})

		kv := agent.Group("/internal/kv")
		kv.Use(mw.KVMiddleware.KVRequired())
		{
			kv.GET("/key/:key", ctrl.KvController.Get)
			kv.POST("/key/:key", ctrl.KvController.Set)
		}
	}

	// file transfer服务调用kv存储进度信息
	api := r.Group("/api/v1")
	{
		kv := api.Group("/internal/kv")
		kv.Use(mw.KVMiddleware.KVRequired())
		{
			kv.GET("/key/:key", ctrl.KvController.Get)
			kv.POST("/key/:key", ctrl.KvController.Set)
		}
	}
	return
}
