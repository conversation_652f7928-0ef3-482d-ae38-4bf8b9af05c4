/*
Copyright © 2021 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"fmt"
	"server/cmd/toolkit/pkg"

	"github.com/spf13/cobra"
)

var (
	concurrency int
	nums        int
	asset       int64
	userPhone   string
)

// rechargeCmd represents the recharge command
var rechargeCmd = &cobra.Command{
	Use:   "recharge",
	Short: "充值测试, 自动登录管理员. 可以测试多用户并发充值, 单用户并发充值, 正常充值等",
	Run: func(cmd *cobra.Command, args []string) {
		/*
			指定管理员帐号密码, 直接登录
			获取所有user列表
			给所有用户充值
		*/
		// parse flag
		if asset == 0 {
			fmt.Printf("parse flag: asset == 0, asset = 1, 充值金额, 默认1元\n\n")
			asset = 1000
		} else {
			asset = asset * 1000
		}

		if concurrency == 0 && nums == 0 && userPhone == "" {
			fmt.Println("parse flag: 请输入正确的测试内容")
			return
		}

		if concurrency != 0 {
			if nums != 0 {
				fmt.Printf("parse flag: c为多用户充值并发测试, n为单用户并发测试. 同时指定时 c 生效\n\n")
				nums = 0
			}
			if userPhone != "" {
				fmt.Printf("parse flag: c为多用户充值并发测试, u为单用户测试. 同时指定时 c 生效\n\n")
				userPhone = ""
			}

		} else {
			if nums == 0 && userPhone != "" {
				nums = 1
			}
			if nums != 0 && userPhone == "" {
				fmt.Printf("parse flag: n为单用户并发测试. 未指定用户 u 时, 将使用第一个获取到的用户进行充值\n\n")
			}
		}

		pkg.Recharge(concurrency, nums, asset, userPhone)
	},
}

//func init() {
//	rootCmd.AddCommand(rechargeCmd)
//
//	rechargeCmd.PersistentFlags().IntVarP(&concurrency, "concurrency", "c", 0, "多用户充值并发数量")
//	rechargeCmd.PersistentFlags().IntVarP(&nums, "nums", "n", 0, "单用户充值并发数量")
//	rechargeCmd.PersistentFlags().Int64VarP(&asset, "asset", "a", 0, "充值金额, 默认1元")
//	rechargeCmd.PersistentFlags().StringVarP(&userPhone, "userPhone", "u", "", "指定充值用户手机号")
//	// Here you will define your flags and configuration settings.
//
//	// Cobra supports Persistent Flags which will work for this command
//	// and all subcommands, e.g.:
//	// rechargeCmd.PersistentFlags().String("foo", "", "A help for foo")
//
//	// Cobra supports local flags which will only run when this command
//	// is called directly, e.g.:
//	// rechargeCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
//}
