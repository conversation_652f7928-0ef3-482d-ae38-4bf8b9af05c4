package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"time"
)

// 拆分账单bill表，根据uid路由到水平拆分后的表中
// 使用方法 ./toolkit --config conf.yaml shard-bill

var (
	fromID int
	toID   int
)

var shardBillCmd = &cobra.Command{
	Use:   "shard-bill",
	Short: "Shard bill data into sharding table",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("shard-bill")
		l.Info("start to shard bill...")

		if fromID < 0 {
			l.Error("from_id should > 0")
			return
		}
		if toID < 0 {
			l.Error("to_id should > 0")
			return
		}

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		// 拆分逻辑 分批读取bill表数据，每一条数据根据uid路由定位table，将数据insert进去

		// 读取bill表
		start := time.Now()
		var total int64
		var lastBillID int
		var bills []model.Bill
		insertDB := db_helper.GlobalDBConn()
		queryDB := db_helper.GlobalDBConn().Table("bill")

		if fromID > 0 {
			queryDB = queryDB.Where("id >= ?", fromID)
		}
		if toID > 0 {
			queryDB = queryDB.Where("id < ?", toID)
		}

		// 执行逻辑
		err = queryDB.FindInBatches(&bills, 2000, func(tx *gorm.DB, batch int) error {
			m := make(map[string][]model.Bill)

			// 根据uid%64路由定位table
			for _, bill := range bills {
				if bill.ID > lastBillID {
					lastBillID = bill.ID
				}

				bill.ID = 0 // 表ID使用自增ID
				uid := bill.UID
				table := constant.GetBillShardingTable(uid % constant.BillShardingCount)

				if _, ok := m[table]; !ok {
					m[table] = make([]model.Bill, 0, 1000)
				}
				m[table] = append(m[table], bill)

				total++
			}

			for table, userBills := range m {
				err = insertDB.Table(table).Create(&userBills).Error
				if err != nil {
					return fmt.Errorf("table %s batch insert err: %v", table, err)
				}
			}

			return nil
		}).Error
		if err != nil {
			l.Error("FindInBatches bill err: %v", err)
			return
		}

		end := time.Now()
		l.Info("total %d, lastBillID %d, start %s, end %s, cost %.2f minutes", total, lastBillID, start.String(), end.String(), end.Sub(start).Minutes())
	},
}

//func init() {
//	rootCmd.AddCommand(shardBillCmd)
//	shardBillCmd.PersistentFlags().IntVarP(&fromID, "from_id", "", 0, "where id >= from_id and id < to_id")
//	shardBillCmd.PersistentFlags().IntVarP(&toID, "to_id", "", 0, "where id >= from_id and id < to_id")
//}
