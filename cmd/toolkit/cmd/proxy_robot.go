package cmd

import (
	"bytes"
	"context"
	"fmt"
	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"github.com/tidwall/gjson"
	"os"
	"os/exec"
	"server/pkg/logger"
	"strings"
	"time"
)

var (
	testRobotWebHook   = "https://open.feishu.cn/open-apis/bot/v2/hook/ed1aa385-fa7b-4371-bb9e-4e22710d44f2"
	robotWebHook       = "https://open.feishu.cn/open-apis/bot/v2/hook/e9e7e1fe-1005-4b26-8841-16b7c745f130"
	downLoadUrl        = "https://huggingface.co/latent-consistency/lcm-lora-sdv1-5/resolve/main/README.md?download=true"
	proxyName          string
	proxyUrl           string
	lastRequestSuccess = true
	count              = 0
	retrySendMsg       = false
)

type alertLevel string

const (
	Emergency alertLevel = "Emergency"
	Warning   alertLevel = "Warning"
	Info      alertLevel = "Info"
)

func CheckProxyStatus(l *logger.Logger) {
	ctx, cancel := context.WithTimeout(context.Background(), 40*time.Second)
	defer cancel()

	checkCmd := exec.CommandContext(ctx, "wget", "-S", downLoadUrl)
	checkCmd.Env = []string{"http_proxy=" + proxyUrl, "https_proxy=" + proxyUrl}

	var stderr bytes.Buffer
	checkCmd.Stderr = &stderr

	err := checkCmd.Run()
	if ctx.Err() == context.DeadlineExceeded || err != nil {
		// 代理异常
		err = errors.Wrap(err, stderr.String())
		l.WithError(err).Error("proxy check failed")
		proxyServer, err := GetProxyService()
		if err != nil {
			l.WithError(err).Error("get proxy service failed during startup")
			return
		}
		l.WithField("proxyService", proxyServer).Info("get proxyServer")
		err = RobotSendMSg(l, Emergency, fmt.Sprintf("代理地区：%s\n代理服务：%s\n启动时代理状态：异常", proxyName, proxyServer))
		if err != nil {
			l.WithFields(map[string]interface{}{"proxy_name": proxyName, "proxy_server": proxyServer}).Error("send startup failure message failed")
			return
		}
	} else {
		// 代理正常
		l.Info("proxy is normal at startup")
		proxyServer, err := GetProxyService()
		if err != nil {
			l.WithError(err).Error("get proxy service failed during startup")
			return
		}
		err = RobotSendMSg(l, Info, fmt.Sprintf("代理地区：%s\n代理服务：%s\n启动时代理状态：正常", proxyName, proxyServer))
		if err != nil {
			l.WithFields(map[string]interface{}{"proxy_name": proxyName, "proxy_server": proxyServer}).Error("send startup success message failed")
			return
		}
	}
}

var ProxyRobotCmd = &cobra.Command{
	Use:   "proxy-robot-warning",
	Short: "Use the robot to send proxy exception notifications",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("proxy-robot-warning")
		l.Info("start to check proxy...")

		// 服务重启发送服务正常还是异常短信
		// 在服务启动时先检查代理状态并发送短信
		CheckProxyStatus(l)
		// 只有连续五次都发送请求失败才会发送报警
		run := func() {
			ctx, cancel := context.WithTimeout(context.Background(), 40*time.Second)
			defer cancel()

			cmd := exec.CommandContext(ctx, "wget", "-S", downLoadUrl)
			cmd.Env = []string{"http_proxy=" + proxyUrl, "https_proxy=" + proxyUrl}

			var stderr bytes.Buffer
			cmd.Stderr = &stderr

			// 运行命令
			err := cmd.Run()
			if ctx.Err() == context.DeadlineExceeded {
				err = errors.Wrap(err, stderr.String())
				l.WithError(err).Error("request timeout")
				count++
				if count >= 5 {
					lastRequestSuccess = false
					proxyServer, err := GetProxyService()
					if err != nil {
						l.WithError(err).Error("get proxy service failed1")
						return
					}
					l.WithField("proxyService", proxyServer).Info("get proxyServer")

					if retrySendMsg || count == 5 {
						err = RobotSendMSg(l, Emergency, fmt.Sprintf("代理地区：%s\n代理服务：%s", proxyName, proxyServer))
						if err != nil {
							retrySendMsg = true
							l.WithFields(map[string]interface{}{"proxy_name": proxyName, "proxy_server": proxyServer, "retrySendMsg": retrySendMsg}).Error("send message failed1")
							return
						}
						retrySendMsg = false
					}

				}
				return
			}

			if err != nil {
				err = errors.Wrap(err, stderr.String())
				l.WithError(err).Error("send request failed .")
				count++
				if count >= 5 {
					lastRequestSuccess = false
					proxyServer, err := GetProxyService()
					if err != nil {
						l.WithError(err).Error("get proxy service failed2")
						return
					}

					l.WithField("proxyService", proxyServer).Info("get proxyServer")

					if retrySendMsg || count == 5 {
						err = RobotSendMSg(l, Emergency, fmt.Sprintf("代理地区：%s\n代理服务：%s", proxyName, proxyServer))
						if err != nil {
							retrySendMsg = true
							l.WithFields(map[string]interface{}{"proxy_name": proxyName, "proxy_server": proxyServer, "retrySendMsg": retrySendMsg}).Error("send message failed2")
							return
						}
						retrySendMsg = false
					}

				}
				return
			}

			l.Info("proxy is normal")
			count = 0

			if !lastRequestSuccess { // 由失败 --> 成功
				lastRequestSuccess = true
				retrySendMsg = false
				proxyServer, err := GetProxyService()
				if err != nil {
					fmt.Println("get proxy service failed,err: ", err)
					return
				}
				err = RobotSendMSg(l, Info, fmt.Sprintf("代理地区：%s\n代理服务：%s", proxyName, proxyServer))
				if err != nil {
					l.WithFields(map[string]interface{}{"proxy_name": proxyName, "proxy_server": proxyServer}).Error("send  info message failed")
					return
				}
			}

			err = os.Remove("README.md?download=true")
			if err != nil {
				fmt.Println("delete file err: ", err)
				return
			}
		}

		run()

		ticker := time.NewTicker(5 * time.Minute)
		//ticker := time.NewTicker(1 * time.Minute)
		for {
			select {
			case <-ticker.C:
				run()
			}
		}
	},
}

func GetProxyService() (proxyServer string, err error) {
	// 读取config.json,解析代理服务
	content, err := os.ReadFile("/root/proxy/config.json")
	if err != nil {
		fmt.Println("read proxy config.json failed err: ", err)
		return "", err
	}
	result := gjson.ParseBytes(content)

	host := result.Get("outbounds.2.streamSettings.wsSettings.headers.Host")
	if !host.Exists() {
		fmt.Println("the specified field host was not found", err)
		return "", err
	}

	splits := strings.Split(host.String(), ".")
	proxyServer = splits[0]
	return proxyServer, nil
}

func RobotSendMSg(l *logger.Logger, level alertLevel, msg string) (err error) {
	text := fmt.Sprintf("[%s]🎉🎉\n", "代理恢复") + msg
	if level == Emergency {
		text = fmt.Sprintf("[%s]🚨🚨\n", "代理失效") + msg
	}

	resp, err := grequests.Post(robotWebHook, &grequests.RequestOptions{
		JSON: map[string]interface{}{
			"msg_type": "text",
			"content": map[string]string{
				"text": text,
			},
		},
	})

	l.Info("send feishu webhook resp %s", resp.String())
	if err != nil {
		l.ErrorE(err, "send feishu webhook resp failed")
		return err
	}
	return nil
}

func init() {
	rootCmd.AddCommand(ProxyRobotCmd)
	ProxyRobotCmd.PersistentFlags().StringVarP(&proxyName, "name", "n", "", "proxy name")
	ProxyRobotCmd.PersistentFlags().StringVarP(&proxyUrl, "url", "u", "", "proxy basic url")
}
