package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"time"
)

// 同步原来的bill表中的数据到指定表
// 使用方法 ./toolkit --config conf.yaml sync-bill

var (
	syncFromID  int
	syncToID    int
	syncToTable string
)

var syncBillCmd = &cobra.Command{
	Use:   "sync-bill",
	Short: "Sync bill data to specific table",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("shard-bill")
		l.Info("start to shard bill...")

		if syncFromID < 0 {
			l.Error("from_id should > 0")
			return
		}
		if syncToID < 0 {
			l.Error("to_id should > 0")
			return
		}
		if syncToTable == "" {
			l.<PERSON>rror("to_table should not be empty")
			return
		}

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		start := time.Now()
		var total int64
		var lastBillID int
		var bills []model.Bill
		insertDB := db_helper.GlobalDBConn()
		queryDB := db_helper.GlobalDBConn().Table("bill")

		if syncFromID > 0 {
			queryDB = queryDB.Where("id >= ?", syncFromID)
		}
		if syncToID > 0 {
			queryDB = queryDB.Where("id < ?", syncToID)
		}

		err = queryDB.FindInBatches(&bills, 2000, func(tx *gorm.DB, batch int) error {
			for i, bill := range bills {
				if bill.ID > lastBillID {
					lastBillID = bill.ID
				}

				bills[i].ID = 0 // 表ID使用自增ID
				total++
			}

			err = insertDB.Table(syncToTable).Create(&bills).Error
			if err != nil {
				return fmt.Errorf("batch insert err: %v", err)
			}

			return nil
		}).Error
		if err != nil {
			l.Error("FindInBatches bill err: %v", err)
			return
		}

		end := time.Now()
		l.Info("total %d, lastBillID %d, start %s, end %s, cost %.2f minutes", total, lastBillID, start.String(), end.String(), end.Sub(start).Minutes())
	},
}

//func init() {
//	rootCmd.AddCommand(syncBillCmd)
//	syncBillCmd.PersistentFlags().IntVarP(&syncFromID, "from_id", "", 0, "where id >= from_id and id < to_id")
//	syncBillCmd.PersistentFlags().IntVarP(&syncToID, "to_id", "", 0, "where id >= from_id and id < to_id")
//	syncBillCmd.PersistentFlags().StringVarP(&syncToTable, "to_table", "", "", "insert into")
//}
