package cmd

import (
	"context"
	"fmt"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"server/conf"
	"server/pkg/cold_data"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"server/plugin/mysql_plugin"
	"sync"
	"time"
)

type DBConf struct {
	Host     string `mapstructure:"host"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"db_name"`
}

type MigrateConfig struct {
	ServerDB   DBConf `mapstructure:"server_db"`
	CoreDB     DBConf `mapstructure:"core_db"`
	ColdDataDB DBConf `mapstructure:"cold_data_db"`
}

var coldDataMigrateCmd = &cobra.Command{
	Use:   "code-data-archiving",
	Short: "",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("code-data-archiving")
		l.Info("toolkit start running ... ")

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		// 单独搞个配置文件
		if cfgFile == "" {
			l.Error("config file is required")
			return
		}

		l.Info("load config file [%s]", cfgFile)

		var cfg MigrateConfig

		viper.SetConfigFile(cfgFile)
		err := viper.ReadInConfig()
		if err != nil {
			l.Error("read cfg file [%s] failed: %+v", cfgFile, err)
			return
		}

		err = viper.Unmarshal(&cfg)
		if err != nil {
			l.Error("unmarshal cfg file [%s] failed: %+v", cfgFile, err)
			return
		}

		l.Debug("init config...,\n%s", libs.IndentString(cfg))

		mysqlSlowLog := time.Second * 3
		debug := false
		if conf.GetGlobalGsConfig().App.DebugLog {
			mysqlSlowLog = time.Millisecond * 300
			debug = true
		}

		// db init
		serverDB, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				cfg.ServerDB.User, cfg.ServerDB.Password,
				cfg.ServerDB.Host, cfg.ServerDB.DBName,
			),
			Debug:         debug,
			SlowThreshold: mysqlSlowLog,
		})
		if err != nil {
			l.Error("connect to server db err: %v", err)
			return
		}

		l.Info("connect to server db success")

		coreDB, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				cfg.CoreDB.User, cfg.CoreDB.Password,
				cfg.CoreDB.Host, cfg.CoreDB.DBName,
			),
			Debug:         debug,
			SlowThreshold: mysqlSlowLog,
		})
		if err != nil {
			l.Error("connect to core db err: %v", err)
			return
		}

		l.Info("connect to core db success")

		coldDataDB, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				cfg.ColdDataDB.User, cfg.ColdDataDB.Password,
				cfg.ColdDataDB.Host, cfg.ColdDataDB.DBName,
			),
			Debug:         debug,
			SlowThreshold: mysqlSlowLog,
		})
		if err != nil {
			l.Error("connect to server db err: %v", err)
			return
		}

		l.Info("connect to coldData db success")

		// 冷数据归档, 垃圾数据清理
		cdm := cold_data.NewCodeDataArchive(serverDB, coreDB, coldDataDB, debug)
		cdm.Run(ctx, panicChan)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(coldDataMigrateCmd)
}
