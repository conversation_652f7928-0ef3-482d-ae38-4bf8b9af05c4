package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"server/conf"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
)

// 创建水平拆分的bill表
// 使用方法 ./toolkit --config conf.yaml create-sharding-bill-table

var createShardingBillTableCmd = &cobra.Command{
	Use:   "create-sharding-bill-table",
	Short: "Create sharding bill table",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("create-sharding-bill-table")
		l.Info("start to create sharding bill table...")

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		// 执行逻辑
		db := db_helper.GlobalDBConn()
		for i := 0; i < constant.BillShardingCount; i++ {
			table := constant.GetBillShardingTable(i)
			_ = db.Exec(fmt.Sprintf("drop table %s", table)).Error
			err = db.Exec(fmt.Sprintf(createBillTableSQL, table)).Error
			if err != nil {
				l.Error("create table %s err: %v", table, err)
				return
			}
		}
	},
}

//func init() {
//	rootCmd.AddCommand(createShardingBillTableCmd)
//}

var createBillTableSQL = "CREATE TABLE `%s` ( `id` bigint(20) NOT NULL AUTO_INCREMENT, `uid` bigint(20) DEFAULT NULL, `uuid` varchar(255) DEFAULT NULL, `order_uuid` varchar(255) DEFAULT NULL, `runtime_uuid` varchar(255) DEFAULT NULL, `product_uuid` varchar(255) DEFAULT NULL, `bill_type` varchar(255) DEFAULT NULL, `bill_sub_type` varchar(255) DEFAULT NULL, `asset` bigint(20) DEFAULT NULL, `charge_type` varchar(255) DEFAULT NULL, `balance` bigint(20) DEFAULT NULL, `details` json DEFAULT NULL, `created_at` datetime DEFAULT NULL, `updated_at` datetime DEFAULT NULL, `confirm_at` datetime DEFAULT NULL, `user_phone` varchar(30) DEFAULT NULL, `pay_by_balance` bigint(20) DEFAULT NULL, `pay_by_voucher` bigint(20) DEFAULT NULL, `instance_uuid` varchar(255) DEFAULT NULL, `note` varchar(255) DEFAULT NULL, `discount` json DEFAULT NULL, `product_type` varchar(255) DEFAULT NULL, PRIMARY KEY (`id`), KEY `bill_uuid` (`uuid`), KEY `bill_orderuuid_index` (`order_uuid`), KEY `bill_product_uuid_index` (`product_uuid`), KEY `bill_runtime_uuid_index` (`runtime_uuid`), KEY `bill_uid_index` (`uid`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;"
