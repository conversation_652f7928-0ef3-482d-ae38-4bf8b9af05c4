package cmd

import (
	"errors"
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	userModel "server/pkg/user/model"
	"server/plugin/mysql_plugin"
	"time"
)

// loginCmd represents the login command
var migrateCertificationCmd = &cobra.Command{
	Use:   "migrate-certification",
	Short: "migrate certification",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("toolkit migrate-certification")

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Timer: nil,
		})
		if err != nil {
			l.<PERSON>rror("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		dbConn.AutoMigrate(&userModel.User{})
		dbConn.AutoMigrate(&userModel.UserMember{})
		dbConn.AutoMigrate(&userModel.UserCertification{})
		t := time.Now().AddDate(1, 0, 0)

		userList := []userModel.User{}
		db_helper.GlobalDBConn().FindInBatches(&userList, 200, func(tx *gorm.DB, batch int) error {
			for _, v := range userList {
				if !v.RealNameAuth {
					continue
				}

				uc := &userModel.UserCertification{}
				err = uc.UCGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{
					"uid":       v.ID,
					"auth_type": constant.UserCertificationPersonal,
				}})
				if err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						uc = &userModel.UserCertification{
							ExpiredAt: &t,
							UID:       v.ID,
							Name:      v.RealName,
							IDNumber:  v.IDNumber,
							AuthType:  constant.UserCertificationPersonal,
							Status:    constant.UserCertificationPass,
						}
						err = uc.UCCreate(nil)
						if err != nil {
							l.ErrorE(err, "uc create failed")
						}
					} else {
						l.ErrorE(err, "get uc failed")
					}
				}

				um := &userModel.UserMember{}
				err = um.UserMemberUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": v.ID}}, map[string]interface{}{
					"real_name":          constant.MemberIdentifyStatusPass,
					"real_name_deadline": t,
				})
				if err != nil {
					l.ErrorE(err, "update um real name failed")
				}
			}
			return nil
		})

		umList := []userModel.UserMember{}
		db_helper.GlobalDBConn().FindInBatches(&umList, 200, func(tx *gorm.DB, batch int) error {
			for _, um := range umList {
				updateMap := map[string]interface{}{}
				if um.Identify == constant.StudentVip {
					updateMap["student"] = constant.MemberIdentifyStatusPass
					if um.IdentifyDeadline != nil {
						updateMap["student_deadline"] = um.IdentifyDeadline
					} else {
						updateMap["student_deadline"] = t
					}

					uc := &userModel.UserCertification{}
					err = uc.UCGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{
						"uid": um.UID, "auth_type": constant.UserCertificationPersonalStudent}})
					if err != nil {
						if errors.Is(err, gorm.ErrRecordNotFound) {
							uc := &userModel.UserCertification{
								UID:      um.UID,
								AuthType: constant.UserCertificationPersonalStudent,
								AuthEntity: userModel.UserCertificationAuthEntity{
									EducationEmail: um.EducationEmail,
								},
								Status:    constant.UserCertificationPass,
								ExpiredAt: &t,
							}
							if um.IdentifyDeadline != nil {
								uc.ExpiredAt = um.IdentifyDeadline
							}
							err = uc.UCCreate(nil)
							if err != nil {
								l.ErrorE(err, "uc edu create failed")
							}
						}
					}

				}
				if um.Identify == constant.EnterpriseVip && um.Enterprise <= 0 {
					updateMap["enterprise"] = constant.MemberIdentifyStatusPass
					if um.IdentifyDeadline != nil {
						updateMap["enterprise_deadline"] = um.IdentifyDeadline
					} else {
						updateMap["enterprise_deadline"] = t
					}
					uc := &userModel.UserCertification{}
					err = uc.UCGet(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{
						"uid": um.UID, "auth_type": constant.UserCertificationEnterpriseCertificate}})
					if err != nil {
						if errors.Is(err, gorm.ErrRecordNotFound) {
							uc := &userModel.UserCertification{
								UID:        um.UID,
								AuthType:   constant.UserCertificationEnterpriseCertificate,
								AuthEntity: userModel.UserCertificationAuthEntity{},
								Status:     constant.UserCertificationPass,
								ExpiredAt:  &t,
							}
							if um.IdentifyDeadline != nil {
								uc.ExpiredAt = um.IdentifyDeadline
							}
							err = uc.UCCreate(nil)
							if err != nil {
								l.ErrorE(err, "uc enterprise create failed")
							}
						}
					}

				}

				if len(updateMap) != 0 {
					err = um.UserMemberUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"uid": um.UID}}, updateMap)
					if err != nil {
						l.ErrorE(err, "update um real name failed")
					}
				}
			}
			return nil
		})
	},
}

func init() {
	rootCmd.AddCommand(migrateCertificationCmd)
}
