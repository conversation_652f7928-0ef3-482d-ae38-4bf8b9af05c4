package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"time"
)

var (
	fixDailyBillUID       int
	fixDailyBillDateStart string
	fixDailyBillDateEnd   string
)

var fixDailyBillCmd = &cobra.Command{
	Use:   "fix-daily-bill",
	Short: "Check daily bill",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("fix-daily-bill")
		l.Info("start to fix daily bill...")

		if fixDailyBillUID <= 0 {
			l.Error("uid should > 0")
			return
		}
		if fixDailyBillDateStart == "" {
			l.Error("date_start should not be empty")
			return
		}
		if fixDailyBillDateEnd == "" {
			l.<PERSON>rror("date_end should not be empty")
			return
		}
		dateStart, err := time.Parse(constant.FormatDateString, fixDailyBillDateStart)
		if err != nil {
			l.WithError(err).Error("parse date_start failed")
			return
		}
		dateEnd, err := time.Parse(constant.FormatDateString, fixDailyBillDateEnd)
		if err != nil {
			l.WithError(err).Error("parse date_end failed")
			return
		}

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		db := db_helper.GlobalDBConn()
		params := make([]*model.CreateOrUpdateDailyBillParams, 0)
		dateFrom := dateStart
		for {
			if dateEnd.Sub(dateFrom).Hours() < 24 {
				break
			}
			dateTo := dateFrom.Add(time.Hour * 24)

			var bills []model.Bill
			err = db.Table(model.TableNameBill).
				Where("uid = ?", fixDailyBillUID).
				Where("bill_type = ?", constant.BillTypeCharge).
				//Where("bill_sub_type not in (?)", excludeBillSubTypes).
				Where("confirm_at > ?", dateFrom.Format(constant.FormatTimeString)).
				Where("confirm_at <= ?", dateTo.Format(constant.FormatTimeString)).Find(&bills).Error
			if err != nil {
				l.WithField("err", err).WithField("date_from", dateFrom).WithField("date_to", dateTo).Error("get bill failed")
				return
			}

			var dailyBills []model.DailyBill
			err = db.Table(model.TableNameDailyBill).
				Where("uid = ?", fixDailyBillUID).
				Where("pay_at = ?", dateTo.Format(constant.FormatTimeString)).
				Find(&dailyBills).Error
			if err != nil {
				l.WithError(err).Error("get daily bill failed")
				return
			}

			billProductUUIDMap := make(map[string]*model.InstanceDailyBillStatistics)
			for _, bill := range bills {
				if bill.ChargeType.IsRentType() {
					continue
				}
				_, ok := billProductUUIDMap[bill.OrderUUID]
				if ok {
					billProductUUIDMap[bill.OrderUUID].PayAmount += bill.PayByBalance // 仅加余额支付的钱
				} else {
					rightTime := time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), dateTo.Hour(), 0, 0, 0, dateTo.Location())
					ibds := &model.InstanceDailyBillStatistics{
						UID:         bill.UID,
						ProductUUID: bill.ProductUUID,
						OrderUUID:   bill.OrderUUID,
						ChargeType:  bill.ChargeType,
						BillType:    bill.Type,
						PayAmount:   bill.PayByBalance,
						PayAt:       rightTime,
						ProductType: bill.SubType.ToProductType(),
					}
					billProductUUIDMap[bill.OrderUUID] = ibds
				}
			}

			dailyBillProductUUIDMap := make(map[string]model.DailyBill)
			for _, dailyBill := range dailyBills {
				dailyBillProductUUIDMap[dailyBill.OrderUUID] = dailyBill
			}

			for _, instanceBillDailyStatistics := range billProductUUIDMap {
				if instanceBillDailyStatistics.PayAmount <= 0 {
					continue
				}
				if _, ok := dailyBillProductUUIDMap[instanceBillDailyStatistics.OrderUUID]; !ok {
					params = append(params, &model.CreateOrUpdateDailyBillParams{
						UID:         instanceBillDailyStatistics.UID,
						OrderUUID:   instanceBillDailyStatistics.OrderUUID,
						ProductUUID: instanceBillDailyStatistics.ProductUUID,
						ChargeType:  instanceBillDailyStatistics.ChargeType,
						PayAmount:   instanceBillDailyStatistics.PayAmount,
						BillType:    instanceBillDailyStatistics.BillType,
						PayAt:       instanceBillDailyStatistics.PayAt,
						ProductType: instanceBillDailyStatistics.ProductType,
					})
				}
			}

			dateFrom = dateTo
		}

		sql := ""
		createDailyBills := make([]*model.DailyBill, 0)
		for _, param := range params {
			var invoiceBill *model.DailyBill
			err = db.Table(model.TableNameDailyBill).Where("product_uuid = ?", param.ProductUUID).Where("pay_at = ?", param.PayAt).First(&invoiceBill).Error
			if err != nil {
				if err == gorm.ErrRecordNotFound {
					bill := &model.DailyBill{
						UID:         param.UID,
						ProductUUID: param.ProductUUID,
						OrderUUID:   param.OrderUUID,
						UUID:        libs.RandNumberString(),
						PayAmount:   param.PayAmount,
						PayAt:       param.PayAt,
						CreatedAt:   param.PayAt,
						UpdatedAt:   param.PayAt,
						ProductType: param.ProductType,
					}
					err = db.Table(model.TableNameDailyBill).Create(&bill).Error
					if err != nil {
						l.WithField("err", err).Error("create daily bill failed")
						return
					}
					createDailyBills = append(createDailyBills, bill)
					sql += fmt.Sprintf("\nINSERT INTO `daily_bill` (`uid`,`uuid`,`order_uuid`,`product_uuid`,`pay_amount`,`pay_at`,`created_at`,`updated_at`,`product_type`) VALUES (%d,'%s','%s','%s',%d,'%s','%s','%s','%s')", bill.UID, bill.UUID, bill.OrderUUID, bill.ProductUUID, bill.PayAmount, bill.PayAt.Format(constant.FormatTimeString), bill.CreatedAt.Format(constant.FormatTimeString), bill.UpdatedAt.Format(constant.FormatTimeString), bill.ProductType)
				} else {
					l.WithError(err).Error("get daily bill failed")
				}
			} else {
				l.WithField("product_uuid", param.ProductUUID).WithField("pay_at", param.PayAt).Info("daily bill exists")
			}
		}
		l.Info("create sql: %s", sql)
		for _, bill := range createDailyBills {
			l.Info("create daily bills:%+v", *bill)
		}
	},
}

//
//func init() {
//	rootCmd.AddCommand(fixDailyBillCmd)
//	fixDailyBillCmd.PersistentFlags().IntVarP(&fixDailyBillUID, "uid", "", 0, "uid")
//	fixDailyBillCmd.PersistentFlags().StringVarP(&fixDailyBillDateStart, "date_from", "", "", "date_from")
//	fixDailyBillCmd.PersistentFlags().StringVarP(&fixDailyBillDateEnd, "date_to", "", "", "date_to")
//}
