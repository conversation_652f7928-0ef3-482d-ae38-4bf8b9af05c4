package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	machineModel "server/pkg/machine/model"
	"server/plugin/mysql_plugin"
)

var updateMachineDiscountPriceCmd = &cobra.Command{
	Use:   "update-machine-discount-price",
	Short: "update machine discount price",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("update-machine-discount-price")
		l.Info("toolkit start running ... ")

		globalConf := conf.GetGlobalGsConfig()

		// mysql
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, dbConn, l)
		machineList := []machineModel.Machine{}
		_ = db_helper.GlobalDBConn().Table(machineModel.TableNameMachine).
			Where("deleted_at is null").
			FindInBatches(&machineList, 200, func(tx *gorm.DB, batch int) error {
				for _, machine := range machineList {
					if machine.NewMachineSkuContent == nil {
						continue
					}
					sku := machine.NewMachineSkuContent
					var paygDiscountPrice int64
					for _, info := range sku {
						if info.Type == constant.ChargeTypePayg {
							for _, levelInfo := range info.LevelConfig {
								if levelInfo.LevelName == constant.MemberUser {
									paygDiscountPrice = levelInfo.DiscountedPrice
								}
							}
						}
					}

					err = machine.MachineUpdateByMap(tx, &db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"machine_id": machine.MachineID,
						},
					}, map[string]interface{}{
						"payg_discount_price": paygDiscountPrice,
					})
					if err != nil {
						l.WithFields(map[string]interface{}{
							"machine_id":          machine.MachineID,
							"payg_discount_price": paygDiscountPrice,
						}).ErrorE(err, "update machine payg_discount_price failed")
					}
				}
				return nil
			}).Error
	},
}

func init() {
	rootCmd.AddCommand(updateMachineDiscountPriceCmd)
}
