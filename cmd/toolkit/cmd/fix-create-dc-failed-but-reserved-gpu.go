package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"server/conf"
	coreGpuStockModel "server/pkg-core/gpu_stock/model"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"strings"
)

var fixCreateDCFailedButReservedGpuCmd = &cobra.Command{
	Use: "fix-create-dc-failed-but-reserved-gpu",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("fix-create-dc-failed-but-reserved-gpu")
		l.Info("toolkit start running ... ")

		globalConf := conf.GetGlobalGsConfig()
		serviceDB, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
				//"root",
				//"seetatech",
				//"test.autodl.com:33306",
				//"gpuhub_v21",
			),
			Debug: globalConf.App.DebugLog,
		})
		if err != nil {
			l.Error("connect to service db err: %v", err)
			return
		}

		coreDB, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.Core.MySQL.User,
				globalConf.Core.MySQL.Password,
				globalConf.Core.MySQL.Host,
				globalConf.Core.MySQL.DBName,
				//"root",
				//"seetatech",
				//"test.autodl.com:33306",
				//"gpuhub_v21",
			),
			Debug: globalConf.App.DebugLog,
		})
		if err != nil {
			l.Error("connect to service db err: %v", err)
			return
		}
		allTroubleUUIDList := []string{}
		productUUIDList := make([]coreGpuStockModel.GpuStock, 0)
		uuidMap := make(map[string]struct{})
		err = coreDB.Table(coreGpuStockModel.TableNameGpuStock).
			Where("product_uuid != ''").
			FindInBatches(&productUUIDList, 500, func(tx *gorm.DB, batch int) error {
				dcUUIDList := make([]string, 0)
				valueString := ""

				for _, v := range productUUIDList {
					if v.ProductUUID == "" {
						continue
					}
					if _, ok := uuidMap[v.ProductUUID]; ok {
						continue
					}
					if strings.Count(v.ProductUUID, "-") > 1 {
						valueString = valueString + fmt.Sprintf("('%s'),", v.ProductUUID)
						dcUUIDList = append(dcUUIDList, v.ProductUUID)
						uuidMap[v.ProductUUID] = struct{}{}
					}
				}

				if len(dcUUIDList) == 0 {
					return nil
				}

				sql := `CREATE TEMPORARY TABLE tmp_dc_uuid (  
			id INT AUTO_INCREMENT PRIMARY KEY,  
			uuid VARCHAR(255) NOT NULL
		)`
				err = serviceDB.Exec(sql).Error
				if err != nil {
					l.ErrorE(err, "Failed to create temporary table")
					return err
				}

				valueString = strings.TrimSuffix(valueString, ",")
				sql = fmt.Sprintf("insert into tmp_dc_uuid(uuid) values %s", valueString)
				err = serviceDB.Exec(sql).Error
				if err != nil {
					l.ErrorE(err, "Failed to insert record")
					return err
				}

				sql = `SELECT t.uuid
			FROM tmp_dc_uuid t
			LEFT JOIN deployment_container m ON t.uuid = m.uuid
			WHERE m.uuid IS NULL
`
				troubleUUIDList := []string{}
				err = serviceDB.Raw(sql).Find(&troubleUUIDList).Error
				if err != nil {
					l.ErrorE(err, "left join failed")
					return err
				}

				err = serviceDB.Exec("drop table tmp_dc_uuid").Error
				if err != nil {
					l.ErrorE(err, "drop table failed")
					return err
				}

				allTroubleUUIDList = append(allTroubleUUIDList, troubleUUIDList...)
				return nil
			}).Error

		fmt.Println()
		fmt.Println()
		fmt.Println()

		for _, v := range allTroubleUUIDList {
			fmt.Println(v)
		}
		//l.Info("trouble uuid, %+v", allTroubleUUIDList)
	},
}

func init() {
	rootCmd.AddCommand(fixCreateDCFailedButReservedGpuCmd)
}
