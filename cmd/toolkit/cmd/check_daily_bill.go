package cmd

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cobra"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
	"time"
)

var (
	checkDailyBillUID       int
	checkDailyBillDateStart string
	checkDailyBillDateEnd   string
)

var checkDailyBillCmd = &cobra.Command{
	Use:   "check-daily-bill",
	Short: "Check daily bill",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("check-daily-bill")
		l.Info("start to check daily bill...")

		if checkDailyBillUID <= 0 {
			l.Error("uid should > 0")
			return
		}
		if checkDailyBillDateStart == "" {
			l.Error("date_start should not be empty")
			return
		}
		if checkDailyBillDateEnd == "" {
			l.<PERSON>rror("date_end should not be empty")
			return
		}
		dateStart, err := time.Parse(constant.FormatDateString, checkDailyBillDateStart)
		if err != nil {
			l.With<PERSON>rror(err).Error("parse date_start failed")
			return
		}
		dateEnd, err := time.Parse(constant.FormatDateString, checkDailyBillDateEnd)
		if err != nil {
			l.WithError(err).Error("parse date_end failed")
			return
		}

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		db := db_helper.GlobalDBConn()
		results := make(map[string]map[string][]string)
		dateFrom := dateStart
		for {
			if dateEnd.Sub(dateFrom).Hours() < 24 {
				break
			}
			dateTo := dateFrom.Add(time.Hour * 24)
			dateToKey := dateTo.Format(constant.FormatTimeString)
			results[dateToKey] = make(map[string][]string)

			var bills []model.Bill
			err = db.Table(model.TableNameBill).
				Where("uid = ?", checkDailyBillUID).
				Where("bill_type = ?", constant.BillTypeCharge).
				//Where("bill_sub_type not in (?)", excludeBillSubTypes).
				Where("confirm_at > ?", dateFrom.Format(constant.FormatTimeString)).
				Where("confirm_at <= ?", dateTo.Format(constant.FormatTimeString)).Find(&bills).Error
			if err != nil {
				l.WithField("err", err).WithField("date_from", dateFrom).WithField("date_to", dateTo).Error("get bill failed")
				return
			}

			var dailyBills []model.DailyBill
			err = db.Table(model.TableNameDailyBill).
				Where("uid = ?", checkDailyBillUID).
				Where("pay_at = ?", dateTo.Format(constant.FormatTimeString)).
				Find(&dailyBills).Error
			if err != nil {
				l.WithError(err).Error("get daily bill failed")
				return
			}

			billProductUUIDMap := make(map[string]*model.InstanceDailyBillStatistics)
			for _, bill := range bills {
				if bill.ChargeType.IsRentType() {
					continue
				}
				_, ok := billProductUUIDMap[bill.OrderUUID]
				if ok {
					billProductUUIDMap[bill.OrderUUID].PayAmount += bill.PayByBalance // 仅加余额支付的钱
				} else {
					rightTime := time.Date(dateTo.Year(), dateTo.Month(), dateTo.Day(), dateTo.Hour(), 0, 0, 0, dateTo.Location())
					ibds := &model.InstanceDailyBillStatistics{
						UID:         bill.UID,
						ProductUUID: bill.ProductUUID,
						OrderUUID:   bill.OrderUUID,
						ChargeType:  bill.ChargeType,
						BillType:    bill.Type,
						PayAmount:   bill.PayByBalance,
						PayAt:       rightTime,
						ProductType: bill.SubType.ToProductType(),
					}
					billProductUUIDMap[bill.OrderUUID] = ibds
				}
			}

			dailyBillProductUUIDMap := make(map[string]model.DailyBill)
			for _, dailyBill := range dailyBills {
				dailyBillProductUUIDMap[dailyBill.OrderUUID] = dailyBill
			}

			for _, instanceBillDailyStatistics := range billProductUUIDMap {
				if instanceBillDailyStatistics.PayAmount <= 0 {
					continue
				}
				if dailyBill, ok := dailyBillProductUUIDMap[instanceBillDailyStatistics.OrderUUID]; !ok {
					if _, ok := results[dateToKey]["OrderUUIDNotFound"]; !ok {
						results[dateToKey]["OrderUUIDNotFound"] = make([]string, 0)
					}
					results[dateToKey]["OrderUUIDNotFound"] = append(results[dateToKey]["orderUUIDNotFound"], instanceBillDailyStatistics.OrderUUID)
				} else if instanceBillDailyStatistics.PayAmount != dailyBill.PayAmount {
					if _, ok := results[dateToKey]["PayAmountNotEqual"]; !ok {
						results[dateToKey]["PayAmountNotEqual"] = make([]string, 0)
					}
					results[dateToKey]["PayAmountNotEqual"] = append(results[dateToKey]["PayAmountNotEqual"], fmt.Sprintf("OrderUUID[%s] PayAmount[%d] != DailyBill.PayAmount[%d]", instanceBillDailyStatistics.OrderUUID, instanceBillDailyStatistics.PayAmount, dailyBill.PayAmount))
				}
			}

			dateFrom = dateTo
		}

		b, _ := json.Marshal(results)
		l.Info(string(b))
	},
}

//func init() {
//	rootCmd.AddCommand(checkDailyBillCmd)
//	checkDailyBillCmd.PersistentFlags().IntVarP(&checkDailyBillUID, "uid", "", 0, "uid")
//	checkDailyBillCmd.PersistentFlags().StringVarP(&checkDailyBillDateStart, "date_from", "", "", "date_from")
//	checkDailyBillCmd.PersistentFlags().StringVarP(&checkDailyBillDateEnd, "date_to", "", "", "date_to")
//}
