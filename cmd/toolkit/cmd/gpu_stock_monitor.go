package cmd

import (
	"context"
	"github.com/spf13/cobra"
	"server/cmd/toolkit/pkg/monitor"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"
)

var monitorCmd = &cobra.Command{
	Use:   "gpu-stock-monitor",
	Short: "异常实例监控",
	Long: `port:7070
统计运行中,没占库存, /api/v1/monitor/running_not_reserve_gpu
占库存,不在运行中, /api/v1/monitor/reserved_gpu_not_running
`,
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("monitor")
		l.Info(">>>>>>>>>>>>>>> monitor start <<<<<<<<<<<<<<<<<<")

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			monitor := monitor.NewMonitor()
			monitor.Run(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(monitorCmd)
}
