package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"gorm.io/gorm"
	"gorm.io/sharding"
	"server/conf"
	"server/pkg/billing_center/model"
	"server/pkg/db_helper"
	"server/pkg/logger"
	"server/plugin/mysql_plugin"
)

var gormShardingTestCmd = &cobra.Command{
	Use: "gorm-sharding-practice",
	Run: func(cmd *cobra.Command, args []string) {
		l := logger.NewLogger("gorm-sharding-practice")
		l.Info("start to run gorm sharding practice...")

		globalConf := conf.GetGlobalGsConfig()
		dbConn, err := mysql_plugin.MySQLProvider(&mysql_plugin.MySQLOptions{
			URL: fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
				globalConf.MySQL.User,
				globalConf.MySQL.Password,
				globalConf.MySQL.Host,
				globalConf.MySQL.DBName,
			),
			Debug: globalConf.App.DebugLog,
			// Debug: false, // NOTE: touch 热更新有时会出现 data race
			Timer: nil,
		})
		if err != nil {
			l.Error("connect to db err: %v", err)
			return
		}
		db_helper.InitDatabaseConnection(dbConn, nil, l)

		dbConn.Use(sharding.Register(sharding.Config{
			ShardingKey:    "uid",
			NumberOfShards: 64,
			// 使用自增ID
			PrimaryKeyGenerator: sharding.PKCustom,
			PrimaryKeyGeneratorFn: func(tableIdx int64) int64 {
				return 0
			},
		}, "bill"))

		bill := model.Bill{
			UID:         51085,
			UserPhone:   "18840875509",
			UUID:        "UUID:166746600049874722",
			OrderUUID:   "OrderUUID:166746338923415591",
			RuntimeUUID: "RuntimeUUID:container-484411a80c-5a41dd97",
			ProductUUID: "ProductUUID:484411a80c-5a41dd97",
		}
		err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
			err = db_helper.InsertOne(db_helper.QueryDefinition{
				DBTransactionConnection: tx,
				ModelDefinition:         &model.Bill{},
				InsertPayload:           &bill,
			}).GetError()
			if err != nil {
				return
			}
			return nil
		})
		if err != nil {
			l.Error("create bill err: %v", err)
			return
		}
		l.Info("create bill: %+v", bill)

		bill = model.Bill{}
		err = db_helper.GetLast(db_helper.QueryDefinition{
			ModelDefinition: &model.Bill{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"uid":  51085,
					"uuid": "UUID:166746600049874722",
				},
			},
		}, &bill).GetError()
		if err != nil {
			l.Error("get first bill err: %v", err)
			return
		}
		l.Info("get first bill: %+v", bill)

		bill = model.Bill{}
		err = db_helper.GlobalDBConn().
			Table("bill_00").
			Where("id = 9").
			First(&bill).
			Error
		if err != nil {
			l.Error("gorm sharding specify table get first bill err: %v", err)
			return
		}
		l.Info("gorm sharding specify table get first bill: %+v", bill)
	},
}

//func init() {
//	rootCmd.AddCommand(gormShardingTestCmd)
//}
