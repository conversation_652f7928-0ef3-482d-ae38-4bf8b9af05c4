package pkg

import (
	"fmt"
	"github.com/levigross/grequests"
)

func ConfirmBill(token, billUUID string) (ok bool, err error) {
	var r *grequests.Response
	var res struct {
		Code string `json:"code"`
		Data bool   `json:"data"`
		Msg  string `json:"msg"`
	}

	r, err = request(GET, Api+"/bill/confirm?bill_uuid="+billUUID, token, nil)
	if err != nil {
		ErrLog("ConfirmBill: request failed")
		return
	}

	err = r.JSON(&res)
	if err != nil {
		ErrLog("ConfirmBill: parse json failed")
		return false, err
	}

	if res.Code != "Success" {
		ErrLog(IndentString(res))
		err = fmt.Errorf("code != Success")
	}

	ok = res.Data
	return

}
