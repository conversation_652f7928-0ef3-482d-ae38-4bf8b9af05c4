package model

import (
	"server/pkg-agent/agent_constant"
	instanceModel "server/pkg/instance/model"
	machineModel "server/pkg/machine/model"
	userModel "server/pkg/user/model"
)

type ClassicResponse struct {
	Code string      `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

type CaptchaResponse struct {
	Code string `json:"code"`
	Data struct {
		ID    string `json:"id"`
		Value string `json:"value"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type CaptchaRequest struct {
	CaptchaID    string `form:"captcha_id" json:"captcha_id"`
	CaptchaValue string `form:"captcha_value" json:"captcha_value"`
}

type LoginRequest struct {
	Phone    string `json:"phone" form:"phone" binding:"required"`
	Password string `json:"password" form:"password" binding:"required"`
	CaptchaRequest
}

type LoginResponse struct {
	Code string `json:"code"`
	Data struct {
		Ticket string `json:"ticket"` // 只取这一个
	} `json:"data"`
	Msg string `json:"msg"`
}

type PassportRequest struct {
	Ticket string `json:"ticket"`
}

type PassportResponse struct {
	Code string `json:"code"`
	Data struct {
		Token string `json:"token"` // 只取这一个
	} `json:"data"`
	Msg string `json:"msg"`
}

type DebugCreateInstanceRequest struct {
	InstanceUUID string `json:"instance_uuid"` // 操作的实例
	MachineID    string `json:"machine_id"`
	agent_constant.ContainerCreateParam
}

type InstanceInfo struct {
	// 本身信息与绑定端口
	instanceModel.Instance

	// 最新状态信息
	Summary string `json:"summary"`
	// TODO... add started_at and so on...

	// 所在机器信息
	Machine         *machineModel.Machine         `json:"machine"`
	MachineBaseInfo *machineModel.BaseMachineInfo `json:"machine_base_info"`
	MachineSkuInfo  []machineModel.SkuInfo        `json:"machine_sku_info"`

	// 当前使用的 GPU 信息, 格式待定
	GPUs []*machineModel.GPUType `json:"gpus"`
	// 用户信息
	User *userModel.User `json:"user"`
	// 简化的符合原型图的状态, 供前端显示...
	//SimpleStatus instanceModel.StatusType `json:"simple_status"`
}

type ListInstanceResponse struct {
	Code string `json:"code"`
	Data struct {
		List []*InstanceInfo `json:"list"`
	} `json:"data"`
	Msg string `json:"msg"`
}
