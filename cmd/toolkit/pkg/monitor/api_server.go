package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	h "server/pkg/http"
	"server/pkg/logger"
	"time"
)

func (m *Monitor) runApiServer(ctx context.Context, panic<PERSON>han chan<- error) {
	gin.SetMode(gin.DebugMode)
	r := gin.Default()
	r.Use(h.GetGinPanicHandler(logger.NewLogger("ApiServerGinHandler")))

	r.Use(cors.New(cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "AppVersion"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
		AllowAllOrigins:  true,
	}))
	m.initHandlers(r)

	srv := &http.Server{
		Addr:              fmt.Sprintf("0.0.0.0:%d", 7070),
		Handler:           r,
		ReadTimeout:       30 * time.Second,
		ReadHeaderTimeout: 30 * time.Second,
		WriteTimeout:      30 * time.Second,
	}
	go func() {
		for {
			select {
			case <-ctx.Done():
				ctxWithTimeout, _ := context.WithTimeout(context.Background(), 10*time.Second)
				shutdownErr := srv.Shutdown(ctxWithTimeout)
				if shutdownErr != nil {
					m.l.ErrorE(shutdownErr, "shut down server failed")
				}
				return
			}
		}
	}()

	panicChan <- srv.ListenAndServe()
	return
}

func (m *Monitor) initHandlers(r *gin.Engine) {
	api := r.Group("/api/v1")
	{
		api.GET("/monitor/running_not_reserve_gpu", func(c *gin.Context) {
			var list []troubleInstance
			j, err := os.ReadFile(runningNotReserveGpuFile)
			if err != nil {
				c.String(200, "file is not ready. err: %v", err)
				return
			}

			if len(j) == 0 {
				c.Status(http.StatusNotFound)
				return
			}

			err = json.Unmarshal(j, &list)
			if err != nil {
				c.String(200, "file is not ready. err: %v", err)
				return
			}
			c.JSON(200, list)
		})
		api.GET("/monitor/reserved_gpu_not_running", func(c *gin.Context) {
			var troubleInstanceList []reservedGpuInstance
			j, err := os.ReadFile(reservedGpuNotRunningFile)
			if err != nil {
				c.String(200, "file is not ready. err: %v", err)
				return
			}

			if len(j) == 0 {
				c.Status(http.StatusNotFound)
				return
			}

			err = json.Unmarshal(j, &troubleInstanceList)
			if err != nil {
				c.String(200, "file is not ready. err: %v", err)
				return
			}
			c.JSON(200, troubleInstanceList)
		})
	}

}
