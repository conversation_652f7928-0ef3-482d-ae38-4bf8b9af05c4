package cmd

import (
	"context"
	"os"
	"os/signal"
	"server/entrance/worker"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"
	"time"

	"github.com/spf13/cobra"
)

var coreWorkerCmd = &cobra.Command{
	Use:   "core-worker",
	Short: "core worker",
	Long:  `core worker`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("CoreWorker")
		logger.Info("ready to running...")
		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		// 根 context，在 signal_watcher.Run 中监听错误，遇到错误后 cancel context
		ctx, cancel := context.WithCancel(context.Background())

		var wg sync.WaitGroup
		wg.Add(1)
		go func(ctx context.Context) {
			// 这里 core worker 会阻塞，signal_watcher.Run cancel context 后，继续往下执行 wg.Done，然后协程退出
			worker.RunCoreWorker(ctx, panic<PERSON>han, logger)
			wg.Done()
		}(ctx)

		// 信号队列
		ch := make(chan os.Signal, 1)
		signal.Notify(ch)
		sig := signal_watcher.NewSignalWatcherByInject(panicChan, cancel, &wg, time.Second*3, ch, logger)
		sig.NeedAlert()
		sig.Run()
	},
}

var coreWorker2Cmd = &cobra.Command{
	Use:   "core-worker2",
	Short: "core worker2",
	Long:  `core worker2`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("CoreWorker2")
		logger.Info("ready to running...")
		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		// 根 context，在 signal_watcher.Run 中监听错误，遇到错误后 cancel context
		ctx, cancel := context.WithCancel(context.Background())

		var wg sync.WaitGroup
		wg.Add(1)
		go func(ctx context.Context) {
			// 这里 core worker 会阻塞，signal_watcher.Run cancel context 后，继续往下执行 wg.Done，然后协程退出
			worker.RunCoreWorker2(ctx, panicChan, logger)
			wg.Done()
		}(ctx)

		// 信号队列
		ch := make(chan os.Signal, 1)
		signal.Notify(ch)
		sig := signal_watcher.NewSignalWatcherByInject(panicChan, cancel, &wg, time.Second*3, ch, logger)
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(coreWorkerCmd)
	rootCmd.AddCommand(coreWorker2Cmd)
}
