/*
Copyright © 2021 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"context"
	"server/entrance/worker"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"

	"github.com/spf13/cobra"
)

// workerCmd represents the worker command
var workerCmd = &cobra.Command{
	Use:   "worker",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

// workerCmd represents the worker command
var worker2Cmd = &cobra.Command{
	Use:   "worker2",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker2")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker2(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

// workerCmd represents the worker command
var worker3Cmd = &cobra.Command{
	Use:   "worker3",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker3")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker3(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

// workerCmd represents the worker command
var worker4Cmd = &cobra.Command{
	Use:   "worker4",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker4")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker4(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

// workerCmd represents the worker command
var worker5Cmd = &cobra.Command{
	Use:   "worker5",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker5")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker5(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

// workerCmd represents the worker command
var worker6Cmd = &cobra.Command{
	Use:   "worker6",
	Short: "backend api server",
	Long:  `backend api server.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Worker5")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			worker.RunWorker6(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(workerCmd)
	rootCmd.AddCommand(worker2Cmd)
	rootCmd.AddCommand(worker3Cmd)
	rootCmd.AddCommand(worker4Cmd)
	rootCmd.AddCommand(worker5Cmd)
	rootCmd.AddCommand(worker6Cmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// workerCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// workerCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}
