/*
Copyright © 2021 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"context"
	"server/entrance/user_center"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"

	"github.com/spf13/cobra"
)

// apiCmd represents the api command
var userCenterCmd = &cobra.Command{
	Use:     "user-center",
	Aliases: []string{"user"},
	Short:   "backend user center grpc server",
	Long:    `backend user center grpc server, bind 8000 port.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("UserCenter")
		logger.Info("ready to running...")

		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			user_center.RunUserCenter(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(userCenterCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// apiCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// apiCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}
