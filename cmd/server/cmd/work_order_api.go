package cmd

import (
	"context"
	"github.com/spf13/cobra"
	"server/entrance/work_order"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"
)

var workOrderApiCmd = &cobra.Command{
	Use: "work-order-api",
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("WorkOrder")
		logger.Info("ready to running...")

		initConfigList(logger)

		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			work_order.RunWorkOrder(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.NeedAlert()
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(workOrderApiCmd)
}
