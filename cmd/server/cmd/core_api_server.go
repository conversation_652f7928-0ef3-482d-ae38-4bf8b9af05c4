package cmd

import (
	"context"
	"os"
	"os/signal"
	"server/entrance/core_api_server"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"
	"time"

	"github.com/spf13/cobra"
)

var coreApiPort int

var coreApiCmd = &cobra.Command{
	Use:   "core-api-server",
	Short: "core api server",
	Long:  `core api server, bind 8000 port.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("CoreApiServer")
		logger.Info("ready to running...")
		initConfigList(logger)

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		// 根 context，在 signal_watcher.Run 中监听错误，遇到错误后 cancel context
		ctx, cancel := context.WithCancel(context.Background())

		var wg sync.WaitGroup
		wg.Add(1)
		go func(ctx context.Context) {
			// 这里 http server 会阻塞，signal_watcher.Run cancel context 后，继续往下执行 wg.Done，然后协程退出
			core_api_server.RunCoreApiServer(ctx, panicChan, coreApiPort, logger)
			wg.Done()
		}(ctx)

		// 信号队列
		ch := make(chan os.Signal, 1)
		signal.Notify(ch)
		sig := signal_watcher.NewSignalWatcherByInject(panicChan, cancel, &wg, time.Second*3, ch, logger)
		sig.NeedAlert()
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(coreApiCmd)
	coreApiCmd.PersistentFlags().IntVarP(&coreApiPort, "port", "p", 8000, "change the default port")
}
