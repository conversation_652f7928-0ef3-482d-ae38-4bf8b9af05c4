/*
Copyright © 2021 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"context"
	"fmt"
	"github.com/spf13/cobra"
	"server/entrance/machine_bill"
	"server/pkg/signal_watcher"
	"sync"
)

// migrateCmd represents the migrate command
var machineBillCmd = &cobra.Command{
	Use:   "machine-bill",
	Short: "Synchronize machine-level bills",
	Long:  `Example: gs machine-bill`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("machine-bill called")

		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			machine_bill.RunSyncMachineBill(ctx, panicChan)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.Run()

	},
}

func init() {
	rootCmd.AddCommand(machineBillCmd)
}
