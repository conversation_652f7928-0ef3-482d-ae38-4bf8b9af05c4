apiVersion: apps/v1
kind: Deployment
metadata:
  name: poye-api-server
  namespace: kpl
spec:
  selector:
    matchLabels:
      app: poye-api-server
  replicas: 10
  template:
    metadata:
      labels:
        app: poye-api-server
    spec:
      imagePullSecrets:
        - name: "harbor-secret"
      serviceAccountName: kpl-serviceaccount
      nodeSelector:
        internal_service_node: "true"
      containers:
        - name: poye-api-server
          image: DOCKER_REGISTRY/poye-gs-v1:latest
          imagePullPolicy: Always
          env:
            - name: "GOGC"
              value: "150"
          volumeMounts:
            - mountPath: /mnt/kpl-pvc
              name: kpl-volume
            - name: config
              mountPath: /etc/gs/gs.yaml
              subPath: gs.yaml
            - name: hostingcloud-key
              mountPath: /etc/gs/hostingcloud-key
            - name: payment-cert
              mountPath: /etc/payment-cert
          command: ["gs"]
          args: ["api-server"]
          ports:
            - containerPort: 8000
              name: port-8000
            - containerPort: 8555
              name: port-8555
          livenessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 4
            failureThreshold: 5
            httpGet:
              path: /health
              port: port-8000
              scheme: HTTP
          resources:
            requests:
              cpu: "400m"
              memory: "512Mi"
            limits:
              cpu: "3000m"
              memory: "6Gi"
      volumes:
        - name: kpl-volume
          persistentVolumeClaim:
            claimName: kpl-pvc
            readOnly: false
        - name: config
          configMap:
            name: cfg-poye-gs
        - name: hostingcloud-key
          secret:
            secretName: hostingcloud-key
        - name: payment-cert
          secret:
            secretName: payment-cert
