apiVersion: apps/v1
kind: Deployment
metadata:
  name: poye-worker5
  namespace: kpl
spec:
  selector:
    matchLabels:
      app: poye-worker5
  replicas: 1
  template:
    metadata:
      labels:
        app: poye-worker5
    spec:
      imagePullSecrets:
        - name: "harbor-secret"
      serviceAccountName: kpl-serviceaccount
      nodeSelector:
        internal_service_node: "true"
      containers:
        - name: poye-worker5
          image: DOCKER_REGISTRY/poye-gs-v1:latest
          imagePullPolicy: Always
          env:
            - name: "GOGC"
              value: "150"
          volumeMounts:
            - mountPath: /mnt/kpl-pvc
              name: kpl-volume
            - name: config
              mountPath: /etc/gs/gs.yaml
              subPath: gs.yaml
            - name: payment-cert
              mountPath: /etc/payment-cert
          command: ["gs"]
          args: ["worker5"]
          ports:
            - containerPort: 8000
              name: port-8000
            - containerPort: 8555
              name: port-8555
          livenessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 4
            failureThreshold: 5
            exec:
              command: ["gs", "health"]
          resources:
            requests:
              cpu: "2000m"
              memory: "2Gi"
            limits:
              cpu: "4000m"
              memory: "6Gi"
      volumes:
        - name: kpl-volume
          persistentVolumeClaim:
            claimName: kpl-pvc
            readOnly: false
        - name: config
          configMap:
            name: cfg-poye-gs
        - name: payment-cert
          secret:
            secretName: payment-cert
