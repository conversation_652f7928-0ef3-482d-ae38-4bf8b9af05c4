apiVersion: v1
kind: PersistentVolume
metadata:
  name: kpl-pv
  labels:
    pv: kpl-pv
spec:
  hostPath:
    path: /gpuhub-pvc
  accessModes:
    - ReadWriteMany
  capacity:
    storage: 500Gi

---
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: kpl-pvc
  namespace: kpl
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 500Gi
  selector:
    matchLabels:
      pv: kpl-pv
