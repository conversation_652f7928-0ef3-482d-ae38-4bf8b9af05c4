apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: poye-core-worker2
  namespace: kpl
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: poye-core-worker2
    spec:
      imagePullSecrets:
        - name: "harbor-secret"
      serviceAccountName: kpl-serviceaccount
      nodeSelector:
        internal_service_node: "true"
        bigmem: "true"
      containers:
        - name: poye-core-worker2
          image: DOCKER_REGISTRY/poye-gs-v1:latest
          imagePullPolicy: Always
          env:
            - name: "GOGC"
              value: "100"
            - name: GOMEMLIMIT
              valueFrom:
                resourceFieldRef:
                  resource: limits.memory
          volumeMounts:
            - mountPath: /mnt/kpl-pvc
              name: kpl-volume
            - name: config
              mountPath: /etc/gs/gs.yaml
              subPath: gs.yaml
            - name: payment-cert
              mountPath: /etc/payment-cert
          command: ["gs"]
          args: ["core-worker2"]
          ports:
            - containerPort: 8000
              name: port-8000
            - containerPort: 8555
              name: port-8555
          livenessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 4
            failureThreshold: 5
            exec:
              command: ["gs", "health"]
          resources:
            requests:
              cpu: "4000m"
              memory: "6Gi"
            limits:
              cpu: "6000m"
              memory: "8Gi"
      volumes:
        - name: kpl-volume
          persistentVolumeClaim:
            claimName: kpl-pvc
            readOnly: false
        - name: config
          configMap:
            name: cfg-poye-gs
        - name: payment-cert
          secret:
            secretName: payment-cert
