server {
    listen 80;
    listen 443 ssl;
    server_name *.autodl.com;
    ssl_certificate   /etc/nginx/cert.autodl/cert.pem;
    ssl_certificate_key  /etc/nginx/cert.autodl/cert.key;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;

    location / {
      resolver kube-dns.kube-system.svc.cluster.local valid=5s;
      proxy_pass http://poye--frontend.kpl.svc.cluster.local:80$request_uri;
    }

    location ^~ /api/v1 {
        resolver kube-dns.kube-system.svc.cluster.local valid=5s;
        proxy_pass http://poye-api-server.kpl.svc.cluster.local:8000$request_uri;
        proxy_set_header Host $host;
        proxy_connect_timeout 10s;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_set_header X-Real-IP       $remote_addr;
        proxy_set_header X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ^~ /admin/v1 {
        resolver kube-dns.kube-system.svc.cluster.local valid=5s;
        proxy_pass http://poye-api-server.kpl.svc.cluster.local:8000$request_uri;
        proxy_set_header Host $host;
        proxy_connect_timeout 10s;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_set_header X-Real-IP       $remote_addr;
        proxy_set_header X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ^~ /agent/v1 {
        resolver kube-dns.kube-system.svc.cluster.local valid=5s;
        proxy_pass http://poye-agent-server.kpl.svc.cluster.local:8000$request_uri;
        proxy_set_header Host $host;
        proxy_connect_timeout 10s;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_set_header X-Real-IP       $remote_addr;
        proxy_set_header X-Forwarded-For  $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location = /basic_status {
        stub_status;
    }

    error_page 502 /502.html;
    location = /502.html {
      root /etc/nginx;
    }
    location = /502.svg {
      root /etc/nginx;
    }
 }
