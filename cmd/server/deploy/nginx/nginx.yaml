apiVersion: apps/v1
kind: Deployment
metadata:
  name: poye--nginx-v2
  namespace: kpl
spec:
  selector:
    matchLabels:
      app: poye--nginx-v2
  replicas: 2
  template:
    metadata:
      labels:
        app: poye--nginx-v2
    spec:
      imagePullSecrets:
      - name: "harbor-secret"
      nodeSelector:
        kpl: "true"
        internal_service_node: "true"
      containers:
      - name: poye--nginx-v2
        image: DOCKER_REGISTRY/poye--nginx:latest
        imagePullPolicy: Always
        ports:
          - containerPort: 80
            name: port-80
          - containerPort: 443
            name: port-443
        livenessProbe:
          initialDelaySeconds: 10
          tcpSocket:
            port: port-80
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "500m"
            memory: "1024Mi"
