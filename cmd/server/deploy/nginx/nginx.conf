user  nginx;
worker_processes  2;
worker_rlimit_nofile  65535;

error_log  /var/log/nginx/error.log debug;
pid        /var/run/nginx.pid;


events {
    worker_connections  4096;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr $request $remote_user [$time_local] [cost: $request_time] '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    client_max_body_size  1024m;
    client_body_buffer_size 16k;
    #tcp_nopush     on;

	set_real_ip_from 0.0.0.0/0;
    real_ip_header  X-Forwarded-For;
    real_ip_recursive on;
    keepalive_timeout  65;

    gzip on;
    gzip_static on;
    gzip_http_version 1.1;
    gzip_comp_level 4;
    gzip_min_length 1024;
    gzip_vary on;
    gzip_types text/plain text/javascript application/x-javascript text/css text/xml application/xml application/xml+rss application/javascript;

    include /etc/nginx/conf.d/*.conf;
}
