GOPROXY=export GO111MODULE=on && export GOPRIVATE=gitlab-gpuhub.autodl.com && export GOPROXY=https://goproxy.cn
GOCMD=$(GOPROXY) && go
GORUN=$(GOCMD) run
DOCKER=docker
DockerImageName=hub.kce.ksyun.com/gpuhub/agent:latest
DockerTestImageName=hub.kce.ksyun.com/gpuhub-test/agent:latest

FtDockerImageName=hub.kce.ksyun.com/gpuhub/ft-agent:latest
FtDockerTestImageName=hub.kce.ksyun.com/gpuhub-test/ft-agent:latest
FtDockerImageNameArm64=hub.kce.ksyun.com/gpuhub/ft-agent_arm64:latest
FtDockerTestImageNameArm64=hub.kce.ksyun.com/gpuhub-test/ft-agent_arm64:latest

now:=$(shell date +%Y%m%d%H%M%S)
formatNow=$(shell date +%Y-%m-%d/%H:%M:%S)
ldflags="-X 'server/pkg/libs.BuiltTime=${formatNow}' -X 'server/pkg/libs.GitCommit=`git describe --all --long`' -X 'server/pkg/libs.GoVersion=`go version`'"

# 动态获取的项目根目录的绝对路径
ROOT_PATH:=$(abspath $(dir $(abspath $(firstword $(MAKEFILE_LIST))))../../)

.PHONY: build docker arm_build

all: build arm_build
run_ga: start_test_server build_and_start_ga

# ################ ga build ######################
do_build:
	-@rm ./ga
	$(GOCMD) build -o ga -ldflags ${ldflags} .
	chmod 777 ./ga

build:
	-@rm ./ga ./ga_x86_64
	$(DOCKER) run --rm -w /code/cmd/agent -v /tmp/go_mod:/root/go/pkg/mod -v ${ROOT_PATH}:/code registry.cn-beijing.aliyuncs.com/work77/golang:1.24.0-ubuntu20.04 sh -c "make do_build"
	-@upx -9 ga
	mv ./ga ./ga_x86_64

arm_build:
	-@rm ./ga ./ga_arm64
	$(DOCKER) run --rm -w /code/cmd/agent -v /tmp/go_mod:/root/go/pkg/mod -v ${ROOT_PATH}:/code registry.cn-beijing.aliyuncs.com/work77/golang:1.24.0-ubuntu22.04-arm64v8 sh -c "make do_build"
	-@upx -9 ga
	mv ./ga ./ga_arm64

start_test_server:
	cd test && go run *.go

build_and_start_ga: build start_ga
start_ga:
	sudo ./ga run -u=192.168.1.126:33000 -r="nanjing-A"

docker: build do_build_docker push_docker_image
test_docker:build build_and_push_test_docker
dev_docker: build build_and_push_dev_docker

do_build_docker:
	$(shell $(FrpcNotExist) && echo "frpc not exist, download from github" && wget https://github.com/ningfdx/moby/releases/download/v20.10.7-deviceu/frpc)
	$(shell $(SupervisorNotExist) && echo "supervisord not exist, please download from https://github.com/ochinchina/supervisord" && exit)

	$(DOCKER) build -f ./Dockerfile -t $(DockerImageName) .

build_and_push_test_docker:
	$(shell $(FrpcNotExist) && echo "frpc not exist, download from github" && wget https://github.com/ningfdx/moby/releases/download/v20.10.7-deviceu/frpc)
	$(shell $(SupervisorNotExist) && echo "supervisord not exist, please download from https://github.com/ochinchina/supervisord" && exit)
	$(shell $(AutoPanelNotExist) && echo "autopanel not exist, please <NAME_EMAIL>:poyekhali/apanel.git" && exit)

	$(DOCKER) build -f ./Dockerfile -t $(DockerTestImageName) .
	$(DOCKER) push $(DockerTestImageName)

build_and_push_dev_docker:
	$(shell $(FrpcNotExist) && echo "frpc not exist, download from github" && wget https://github.com/ningfdx/moby/releases/download/v20.10.7-deviceu/frpc)
	$(shell $(SupervisorNotExist) && echo "supervisord not exist, please download from https://github.com/ochinchina/supervisord" && exit)
	$(shell $(AutoPanelNotExist) && echo "autopanel not exist, please <NAME_EMAIL>:poyekhali/apanel.git" && exit)

	$(DOCKER) build -f ./Dockerfile -t 192.168.1.126:5000/seetaas/agent:latest .
	$(DOCKER) push 192.168.1.126:5000/seetaas/agent:latest

push_docker_image:
	$(DOCKER) push $(DockerImageName)

# ################# ft docker #################
ft_docker: build ft_do_build_docker ft_push_docker_image
ft_test_docker: build ft_test_do_build_docker ft_test_push_docker_image

ft_do_build_docker:
	$(DOCKER) build -f ./ft/Dockerfile -t $(FtDockerImageName) .

ft_push_docker_image:
	$(DOCKER) push $(FtDockerImageName)


ft_test_do_build_docker:
	$(DOCKER) build -f ./ft/Dockerfile -t $(FtDockerTestImageName) .

ft_test_push_docker_image:
	$(DOCKER) push $(FtDockerTestImageName)

# ################ ft docker arm ##############
arm_ft_docker: arm_build arm_ft_do_build_docker arm_ft_push_docker_image
arm_ft_test_docker: arm_build arm_ft_test_do_build_docker arm_ft_test_push_docker_image

arm_ft_do_build_docker:
	$(DOCKER) build -f ./ft/Dockerfile_arm64 -t $(FtDockerImageNameArm64) .

arm_ft_push_docker_image:
	$(DOCKER) push $(FtDockerImageNameArm64)


arm_ft_test_do_build_docker:
	$(DOCKER) build -f ./ft/Dockerfile_arm64 -t $(FtDockerTestImageNameArm64) .

arm_ft_test_push_docker_image:
	$(DOCKER) push $(FtDockerTestImageNameArm64)



FrpcPath=./frpc_x86_64
SupervisorPath=./supervisord_x86_64
AutoPanelPath=./autopanel_x86_64
FrpcNotExist=false
AutoPanelNotExist=false
SupervisorNotExist=false
ifeq ("$(wildcard $(FrpcPath))", "")
FrpcNotExist=true
endif
ifeq ("$(wildcard $(SupervisorPath))", "")
SupervisorNotExist=true
endif
ifeq ("$(wildcard $(AutoPanelPath))", "")
AutoPanelNotExist=true
endif
