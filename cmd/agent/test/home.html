<!DOCTYPE html>
<html lang="en">
<head>
    <title>Chat Example</title>
    <script type="text/javascript">
        window.onload = function () {
            var conn;
            var msg = document.getElementById("msg");
            var log = document.getElementById("log");

            var rand = Date.parse(new Date());

            function appendLog(item) {
                var doScroll = log.scrollTop > log.scrollHeight - log.clientHeight - 1;
                log.appendChild(item);
                if (doScroll) {
                    log.scrollTop = log.scrollHeight - log.clientHeight;
                }
            }

            document.getElementById("create").onclick = function () {
                if (!conn) {
                    return false;
                }

                msg.value = '{\n' +
                    '    "Type": "ContainerCreateType",\n' +
                    '    "Payload": "{\\n \\"container_id\\": \\"' + rand +
                    '\\",\\n \\"name\\": \\"boot-test-' + rand +
                    '\\",\\n \\"image\\": \\"docker.io/library/ubuntu:16.04\\",\\n\\"gpuuuid_list\\": null,\\n \\"gpu_capabilities\\": null,\\n \\"cpu_limit\\": 1,\\n \\"mem_limit_in_mb\\": 1024,\\n \\"pre_cmd\\": [\\n  \\"sleep\\",\\n  \\"1000\\"\\n ],\\n \\"working_dir\\": \\"/\\",\\n \\"labels\\": {\\n  \\"for-test\\": \\"true\\"\\n },\\n \\"env\\": [\\n  \\"nfd=true\\",\\n  \\"test=true\\"\\n ],\\n \\"root_password\\": \\"\\",\\n \\"max_writable_size_in_byte\\": 200000000,\\n \\"max_local_disk_size_in_byte\\": 200000000,\\n \\"stop_timeout\\": null,\\n \\"shm_size\\": 0,\\n \\"ProxtHost\\": \\"ProxtHost\\",\\n \\"ProxyPort\\": \\"ProxyPort\\",\\n \\"ProxyToken\\": \\"ProxyToken\\",\\n \\"jupyter_token\\": \\"JupyterToken\\",\\n \\"jupyter_port\\": 30001,\\n \\"tensorboard_port\\": 30002,\\n \\"ssh_port\\": 30003\\n}"\n' +
                    '}'
                return false;
            };

            document.getElementById("start").onclick = function () {
                if (!conn) {
                    return false;
                }
                msg.value = '{\n' +
                    '    "Type": "ContainerStartType",\n' +
                    '    "Payload": "{\\n \\"container_id\\": \\"' + rand +
                    '\\",\\n \\"name\\": \\"boot-test-' + rand +
                    '\\",\\n \\"image\\": \\"docker.io/library/ubuntu:16.04\\",\\n\\"gpuuuid_list\\": null,\\n \\"gpu_capabilities\\": null,\\n \\"cpu_limit\\": 1,\\n \\"mem_limit_in_mb\\": 1024,\\n \\"pre_cmd\\": [\\n  \\"sleep\\",\\n  \\"1000\\"\\n ],\\n \\"working_dir\\": \\"/\\",\\n \\"labels\\": {\\n  \\"for-test\\": \\"true\\"\\n },\\n \\"env\\": [\\n  \\"nfd=true\\",\\n  \\"test=true\\"\\n ],\\n \\"root_password\\": \\"\\",\\n \\"max_writable_size_in_byte\\": 200000000,\\n \\"max_local_disk_size_in_byte\\": 200000000,\\n \\"stop_timeout\\": null,\\n \\"shm_size\\": 0,\\n \\"ProxtHost\\": \\"ProxtHost\\",\\n \\"ProxyPort\\": \\"ProxyPort\\",\\n \\"ProxyToken\\": \\"ProxyToken\\",\\n \\"jupyter_token\\": \\"JupyterToken\\",\\n \\"jupyter_port\\": 30001,\\n \\"tensorboard_port\\": 30002,\\n \\"ssh_port\\": 30003\\n}"\n' +
                    '}'
                return false;
            };

            document.getElementById("stop").onclick = function () {
                if (!conn) {
                    return false;
                }
                msg.value = '{\n' +
                    '    "Type": "ContainerStopType",\n' +
                    '    "Payload": "{\\n \\"container_id\\": \\"' + rand +
                    '\\",\\n \\"name\\": \\"boot-test-' + rand +
                    '\\"}"\n' +
                    '}'
                return false;
            };

            document.getElementById("remove").onclick = function () {
                if (!conn) {
                    return false;
                }
                msg.value = '{\n' +
                    '    "Type": "ContainerRemoveType",\n' +
                    '    "Payload": "{\\n \\"container_id\\": \\"' + rand +
                    '\\",\\n \\"name\\": \\"boot-test-' + rand +
                    '\\"}"\n' +
                    '}'
                return false;
            };

            document.getElementById("form").onsubmit = function () {
                if (!conn) {
                    return false;
                }
                if (!msg.value) {
                    return false;
                }
                console.log(msg.value)
                conn.send(msg.value);
                msg.value = "";
                return false;
            };

            if (window["WebSocket"]) {
                conn = new WebSocket("ws://" + document.location.host + "/agent/v1/connect");
                conn.onclose = function (evt) {
                    var item = document.createElement("div");
                    item.innerHTML = "<b>Connection closed.</b>";
                    appendLog(item);
                };
                conn.onmessage = function (evt) {
                    var messages = evt.data.split('\n');
                    for (var i = 0; i < messages.length; i++) {
                        var item = document.createElement("div");
                        item.innerText = messages[i];
                        appendLog(item);
                    }
                };
            } else {
                var item = document.createElement("div");
                item.innerHTML = "<b>Your browser does not support WebSockets.</b>";
                appendLog(item);
            }
        };
    </script>
    <style type="text/css">
        html {
            overflow: hidden;
        }

        body {
            overflow: hidden;
            padding: 0;
            margin: 0;
            width: 100%;
            height: 100%;
            background: gray;
        }

        #log {
            background: white;
            margin: 0;
            padding: 0.5em 0.5em 0.5em 0.5em;
            position: absolute;
            top: 0.5em;
            left: 0.5em;
            right: 0.5em;
            bottom: 10em;
            overflow: auto;
        }

        #form {
            padding: 0 0.5em 0 0.5em;
            margin: 0;
            position: absolute;
            bottom: 1em;
            left: 0px;
            width: 100%;
            overflow: hidden;
        }

        #buts {
            position: absolute;
            bottom: 8em;
        }

    </style>
</head>
<body>
<div>{"type": "ContainerCreateType", "payload": ""}</div>
<div id="log"></div>
<div id="buts">
    <button id="create" class="but">创建容器</button>
    <button id="start" class="but">启动容器</button>
    <button id="stop" class="but">停止容器</button>
    <button id="remove" class="but">释放容器</button>
</div>
<form id="form">
    <input type="submit" value="Send"/>
    <textarea id="msg" cols="30" rows="5" autofocus></textarea>
</form>
</body>
</html>
