/*
Copyright © 2021 seetaas

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/
package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"net/http"
	_ "net/http/pprof"
	"os"
	"server/conf"
	"server/pkg/libs"
	"server/pkg/logger"

	"github.com/mitchellh/go-homedir"
	"github.com/spf13/viper"
	"strings"
)

var cfgFile string
var version bool

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:     "ga",
	Version: libs.LatestVersion,
	Short:   "ga(gpu agent) is a set of platform agent tool",
	Long:    `ga(gpu agent) is a set of platform agent tool.`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	// PreRun: func(cmd *cobra.Command, args []string) {
	//
	// },
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func init() {
	go func() {
		http.ListenAndServe("0.0.0.0:8555", nil)
	}()
	cobra.OnInitialize(initConfig)

	// Here you will define your flags and configuration settings.
	// Cobra supports persistent flags, which, if defined here,
	// will be global for your application.

	// rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is $HOME/.agent.yaml)")

	// Cobra also supports local flags, which will only run
	// when this action is called directly.
	// rootCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")

	rootCmd.SetVersionTemplate("GA: " + libs.VersionTemplate)
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	l := logger.NewLogger("initConfig")

	if cfgFile != "" {
		// Use config file from the flag.
		viper.SetConfigFile(cfgFile)
	} else {
		// Find home directory.
		home, err := homedir.Dir()
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}

		// search config in home directory with name ".agent" (without extension).
		// viper.AddConfigPath(home)
		// viper.SetConfigName(".agent")

		// search config in home directory with name "gs.yaml" (without extension).
		if pwd, err := os.Getwd(); err == nil {
			confPath := strings.Replace(pwd, "/cmd/agent", "/conf", -1)
			l.Info("Add config file search path %s", confPath)
			viper.AddConfigPath(confPath)
		}
		viper.AddConfigPath("/etc/gs")
		viper.AddConfigPath(home)
		viper.SetConfigName("gs")
		viper.SetConfigType("yaml")
	}

	viper.AutomaticEnv() // read in environment variables that match

	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		conf.SetGlobalGsConfig()
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	}
}
