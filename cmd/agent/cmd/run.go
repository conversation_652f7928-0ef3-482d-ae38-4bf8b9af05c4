/*
Copyright © 2021 NAME HERE <EMAIL ADDRESS>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package cmd

import (
	"context"
	"github.com/spf13/cobra"
	"server/entrance/agent/guard"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/agent_guard"
	"server/pkg/libs"
	"server/pkg/logger"
	"server/pkg/signal_watcher"
	"sync"
)

var (
	url                string
	token              string
	region             string
	verbose            bool
	isTestEnv          bool
	localRedisHost     string
	currentVersion     string
	cpuCoreNumsPerUnit int  // 每个虚拟化的gpu多少个cpu核心
	maxCpuUnitNum      int  // 当使用cpu虚拟gpu时, 最多虚拟化出多少个gpu
	ossFileChecker     bool // 当前agent是否启用 ossFileChecker
)

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run",
	Short: "Connect with gs(gpu server)",
	Long:  `Connect with gs(gpu server), listen options from gs, report machine status to gs.`,
	Run: func(cmd *cobra.Command, args []string) {
		logger := logger.NewLogger("Agent")
		logger.Info("[Version] %s", libs.LatestVersion)
		if currentVersion != "v1" {
			agent_constant.CurrentRuntimeVersion = agent_constant.RuntimeVersion(currentVersion)
		}
		logger.Info("[current runtime version] %s, %s", currentVersion, agent_constant.CurrentRuntimeVersion)
		logger.Info("url: %s, token: %s, region: %s", url, libs.Mask(token), region)
		logger.Info("ready to running...")
		if verbose {
			agent_constant.VerboseMode = verbose
			logger.Info("open verbose mode")
		}

		if isTestEnv {
			logger.Info("use test env mode...")
			agent_guard.IsTestEnv = isTestEnv
		}

		// 必须填入地区
		if len(region) == 0 {
			logger.Error("Start gpu agent failed... No region sign... exit.")
			return
		}

		// 用于监听严重错误, 需要重启服务的信号, 缓冲区设置较长的原因是有可能同时发生n个错误, 避免chan阻塞
		panicChan := make(chan error, 10)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup

		wg.Add(1)
		go func(ctx context.Context) {
			guard.RunGuard(ctx, panicChan, url, token, region, localRedisHost, cpuCoreNumsPerUnit, maxCpuUnitNum, ossFileChecker)
			wg.Done()
		}(ctx)

		sig := signal_watcher.NewSignalWatcher(panicChan, cancel, &wg)
		sig.Run()
	},
}

func init() {
	rootCmd.AddCommand(runCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	runCmd.PersistentFlags().StringVarP(&url, "url", "u", "*************:33001", "gs url like *************:33001")
	runCmd.PersistentFlags().StringVarP(&token, "token", "t", "seetatech666", "gs url url token")
	runCmd.PersistentFlags().StringVarP(&region, "region", "r", "", "gs region")                                                       // 有些 gpu 机器特别地在 yaml 设置不带地区网盘, 所以默认值必须为空.
	runCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "w", false, "verbose")                                                      // 输出详细信息
	runCmd.PersistentFlags().BoolVar(&isTestEnv, "test", false, "test")                                                                // 输出详细信息
	runCmd.PersistentFlags().StringVarP(&localRedisHost, "local_redis", "", "", "local redis for diff cache, e.g. 127.0.0.1:6379")     //
	runCmd.PersistentFlags().StringVarP(&currentVersion, "current_version", "", "v1", "currentVersion: v1 v2 is valid, default is v1") //
	runCmd.PersistentFlags().IntVar(&cpuCoreNumsPerUnit, "cpu_core_num_per_unit", 0, "A value greater than 0 represents the CPU machine, indicating how many CPU cores are in a rented unit. The default is 0, representing the GPU machine")
	runCmd.PersistentFlags().IntVar(&maxCpuUnitNum, "max_cpu_unit_num", 0, "When using a CPU to virtualize a GPU, how many gpus are virtualized at most. Valid only if the 'cpu_core_num_per_unit' is not 0")
	runCmd.PersistentFlags().BoolVar(&ossFileChecker, "oss-file-checker", false, "")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// runCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")
}
