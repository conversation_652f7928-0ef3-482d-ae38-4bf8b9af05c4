package model

import (
	"gorm.io/gorm"
	"time"
)

const TableNameWorkOrderMachineType = "work_order_machine_type"

type WorkOrderMachineType struct {
	ID           int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt    time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	Name         string         `gorm:"type:varchar(255);column:name;" json:"name"`
	CreateUserId int            `gorm:"type:int;column:create_user_id" json:"create_user_id"`
	ProviderId   int            `gorm:"type:int;column:provider_id" json:"provider_id"` // 供应商id
	Remark       string         `gorm:"column:remark;type:text" json:"remark"`
}

func (w *WorkOrderMachineType) TableName() string {
	return TableNameWorkOrderMachineType
}

func (w *WorkOrderMachineType) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderMachineType{})
}

type WorkOrderMachineTypeMaterial struct {
	ID            int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt     time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	MaterialID    int            `gorm:"type:int;column:material_id" json:"material_id"`       // 物料id
	MaterialCount int64          `gorm:"type:int;column:material_count" json:"material_count"` // 物料数量
	MachineTypeId int            `gorm:"type:int;column:machine_type_id" json:"machine_type_id"`
}

const TableNameWorkOrderMachineTypeMaterial = "work_order_machine_type_material"

func (w *WorkOrderMachineTypeMaterial) TableName() string {
	return TableNameWorkOrderMachineTypeMaterial
}

func (w *WorkOrderMachineTypeMaterial) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderMachineTypeMaterial{})
}
