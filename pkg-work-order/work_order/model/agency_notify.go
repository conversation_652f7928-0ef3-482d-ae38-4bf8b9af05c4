package model

import (
	"database/sql"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameAgencyNotify = "work_order_agency_notify"

type AgencyNotify struct { // 代办通知
	ID              int                       `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt       time.Time                 `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt       time.Time                 `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt       gorm.DeletedAt            `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	MachineId       string                    `gorm:"type:varchar(255);column:machine_id" json:"machine_id"`       // 主机id
	NotifyContent   string                    `gorm:"type:text;column:notify_content" json:"notify_content"`       // 发送内容
	NotifyStatus    constant.NotifyStatusType `gorm:"type:varchar(255);column:notify_status" json:"notify_status"` // 状态
	SubNotifyStatus datatypes.JSON            `gorm:"type:json;column:sub_notify_status" json:"sub_notify_status"` // 记录每个通知用户的状态 userId,status,nextNotifyTime
	NotifyTime      sql.NullTime              `gorm:"type:datetime;column:notify_time" json:"notify_time"`
	UserId          int                       `gorm:"type:int;column:user_id" json:"user_id"` // 创建用户id
	TenantId        int                       `gorm:"type:int;column:tenant_id" json:"tenant_id"`
	NotifyUser      string                    `gorm:"type:varchar(255);column:notify_user" json:"notify_user"`
	SendRetryCount  int                       `gorm:"type:int;column:send_retry_count" json:"send_retry_counts"` // 重试次数
}

func (w *AgencyNotify) TableName() string {
	return TableNameAgencyNotify
}

func (w *AgencyNotify) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&AgencyNotify{})
}

func (w *AgencyNotify) AgencyNotifyCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		InsertPayload:           w,
	}).GetError()
}

func (w *AgencyNotify) AgencyNotifyUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		Filters:                 *filter,
	}, um).GetError()
}

func (w *AgencyNotify) AgencyNotifyGetAll(filter db_helper.QueryFilters) (list []AgencyNotify, err error) {
	list = []AgencyNotify{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &list).GetError()
	return
}

func (w *AgencyNotify) AgencyNotifyGetFirst(filter *db_helper.QueryFilters) (err error) {
	err = db_helper.GetFirst(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         *filter,
	}, &w).GetError()
	return
}
