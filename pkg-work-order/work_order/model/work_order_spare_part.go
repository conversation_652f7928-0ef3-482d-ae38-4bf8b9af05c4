package model

import (
	"gorm.io/gorm"
	"server/pkg/constant"
	"time"
)

// TableNameWorkOrderSparePartTable 备件工单
const TableNameWorkOrderSparePartTable = "work_order_spare_part"

type WorkOrderSparePart struct {
	ID             int                               `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	MachineID      string                            `gorm:"type:varchar(255);column:machine_id" json:"machine_id"`
	ProviderID     int                               `gorm:"type:int;column:provider_id" json:"provider_id"`            // 供应商id（创建租户id）
	CustomID       int                               `gorm:"type:int;column:custom_id" json:"custom_id"`                // 客户id
	MachineRoom    string                            `gorm:"type:varchar(255);column:machine_room" json:"machine_room"` // 机房
	Remark         string                            `gorm:"column:remark;type:text" json:"remark"`
	MaterialID     int                               `gorm:"type:int;column:material_id" json:"material_id"`       // 物料id
	MaterialCount  int64                             `gorm:"type:int;column:material_count" json:"material_count"` // 物料数量
	CreatedAt      time.Time                         `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt      time.Time                         `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt      gorm.DeletedAt                    `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	DispatchBillID int                               `gorm:"type:int;column:dispatch_bill_id" json:"dispatch_bill_id"` // 绑定发货单id
	CreateUserId   int                               `gorm:"type:int;column:create_user_id" json:"create_user_id"`     // 创建用户，对应work_order_operate_user表的id
	Status         constant.WorkOrderSparePartStatus `gorm:"type:varchar(255);column:status" json:"status"`
	DispatchTime   *time.Time                        `gorm:"type:datetime;column:dispatch_time" json:"dispatch_time"`
}

func (w *WorkOrderSparePart) TableName() string {
	return TableNameWorkOrderSparePartTable
}

func (w *WorkOrderSparePart) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderSparePart{})
}

// TableNameDispatchOrderSparePartTable 备件发货单
const TableNameDispatchOrderSparePartTable = "dispatch_order_spare_part"

type DispatchOrderSparePart struct { // 备件发货单
	ID               int                                   `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	ProviderID       int                                   `gorm:"type:int;column:provider_id" json:"provider_id"`                      // 供应商id
	CustomID         int                                   `gorm:"type:int;column:custom_id" json:"custom_id"`                          // 客户id
	MachineRoom      string                                `gorm:"type:varchar(255);column:machine_room" json:"machine_room"`           // 机房
	RecipientName    string                                `gorm:"type:varchar(255);column:recipient_name" json:"recipient_name"`       // 收件人
	RecipientPhone   string                                `gorm:"type:varchar(255);column:recipient_phone" json:"recipient_phone"`     // 收件人电话
	RecipientAddress string                                `gorm:"type:varchar(255);column:recipient_address" json:"recipient_address"` // 收件人地址
	ExpressNumber    string                                `gorm:"type:varchar(255);column:express_number" json:"express_number"`
	CreatedAt        time.Time                             `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt        time.Time                             `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt        gorm.DeletedAt                        `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	CreateUserId     int                                   `gorm:"type:int;column:create_user_id" json:"create_user_id"` // 创建用户，对应work_order_operate_user表的id
	Status           constant.DispatchOrderSparePartStatus `gorm:"type:varchar(255);column:status" json:"status"`
	DispatchTime     *time.Time                            `gorm:"type:datetime;column:dispatch_time" json:"dispatch_time"`
}

func (w *DispatchOrderSparePart) TableName() string {
	return TableNameDispatchOrderSparePartTable
}

func (w *DispatchOrderSparePart) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&DispatchOrderSparePart{})
}

// TableNameDispatchOrderSparePartMaterialTable 备件发货单-材料关系表
const TableNameDispatchOrderSparePartMaterialTable = "dispatch_order_spare_part_material"

type DispatchOrderSparePartMaterial struct {
	ID                       int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt                time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt                time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt                gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	MaterialID               int            `gorm:"type:int;column:material_id" json:"material_id"`       // 物料id
	MaterialCount            int64          `gorm:"type:int;column:material_count" json:"material_count"` // 物料数量
	DispatchOrderSparePartID int            `gorm:"type:int;column:dispatch_order_spare_part_id" json:"dispatch_order_spare_part_id"`
}

func (w *DispatchOrderSparePartMaterial) TableName() string {
	return TableNameDispatchOrderSparePartMaterialTable
}

func (w *DispatchOrderSparePartMaterial) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&DispatchOrderSparePartMaterial{})
}

// ChangeTransition 备件发货单status -->备件工单status
func ChangeTransition(status constant.DispatchOrderSparePartStatus) constant.WorkOrderSparePartStatus {
	switch status {
	case constant.DispatchOrderSparePartStocking:
		return constant.WorkOrderSparePartStocking
	case constant.DispatchOrderSparePartShipped:
		return constant.WorkOrderSparePartShipped
	case constant.DispatchOrderSparePartClose:
		return constant.WorkOrderSparePartClose
	}
	return constant.WaitDeal
}
