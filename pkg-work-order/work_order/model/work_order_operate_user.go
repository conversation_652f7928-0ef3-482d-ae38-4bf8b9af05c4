package model

import (
	"server/pkg/constant"
	"time"

	"gorm.io/gorm"
)

const TableNameWorkOrderOperateUser = "work_order_operate_user"

type WorkOrderOperateUser struct {
	ID            int                          `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	UID           int                          `gorm:"type:int;column:uid;index" json:"uid"` // 和autodl一致
	Username      string                       `gorm:"type:varchar(255);column:username" json:"username"`
	BackstageRole constant.BackstageRole       `gorm:"column:backstage_role;type:varchar(255)" json:"backstage_role"` // 和autodl一致
	CreatedAt     time.Time                    `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt     time.Time                    `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt     gorm.DeletedAt               `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	TenantID      int                          `gorm:"type:int;column:tenant_id" json:"tenant_id"`
	UUID          string                       `gorm:"type:varchar(255);column:uuid" json:"uuid"`           // 和autodl一致
	UserRole      constant.WorkOrderUserRole   `gorm:"column:user_role;type:varchar(255)" json:"user_role"` // 租户下的user角色
	UserStatus    constant.WorkOrderUserStatus `gorm:"column:user_status;type:varchar(100)" json:"user_status"`
	FeishuBotUrl  string                       `gorm:"column:feishu_bot_url;type:varchar(255)" json:"feishu_bot_url"` // 飞书机器人地址
}

func (w *WorkOrderOperateUser) TableName() string {
	return TableNameWorkOrderOperateUser
}

func (w *WorkOrderOperateUser) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderOperateUser{})
}
