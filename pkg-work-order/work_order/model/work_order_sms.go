package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

const TableNameWorkOrderSms = "work_order_sms"

type WorkOrderSms struct {
	ID        int            `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt time.Time      `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt time.Time      `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"type:datetime;column:deleted_at;index" json:"-"`

	InstanceUuid       string                 `gorm:"type:varchar(255);column:instance_uuid" json:"instance_uuid"` // 实例id
	SendUserId         int                    `gorm:"type:int;column:send_user_id" json:"send_user_id"`            // 发送人id
	TenantId           int                    `gorm:"type:int;column:tenant_id" json:"tenant_id"`
	Phone              string                 `gorm:"type:varchar(255);column:phone" json:"phone"` // 手机号
	PhoneArea          string                 `gorm:"type:varchar(255);column:phone_area" json:"phone_area"`
	MachineId          string                 `gorm:"type:varchar(255);column:machine_id" json:"machine_id"`                     // 主机id
	TemplateId         int                    `gorm:"type:int;column:template_id" json:"template_id"`                            // 模板id
	SmsContent         string                 `gorm:"type:text;column:sms_content" json:"sms_content"`                           // 发送内容（替换后的内容）
	SmsParamsInfo      datatypes.JSON         `gorm:"type:json;column:sms_params_info" json:"sms_params_info"`                   // 模板参数（已赋值）
	SendChannel        string                 `gorm:"type:varchar(255);column:send_channel" json:"send_channel"`                 // 发送渠道（短信、微信）
	SmsStatus          constant.SmsStatusType `gorm:"type:varchar(255);column:sms_status" json:"sms_status"`                     // 状态
	IsSendFeishuNotify bool                   `gorm:"type:tinyint(1);column:is_send_feishu_notify" json:"is_send_feishu_notify"` // 是否发送飞书通知
	SendBasis          constant.SendBasisType `gorm:"varchar(255);column:send_basis" json:"send_basis"`                          // 发送依据（实例ID、注册手机号）
	FeishuNotifyUser   string                 `gorm:"varchar(255);column:feishu_notify_user" json:"feishu_notify_user"`          // 飞书通知用户（多选）
}

func (w *WorkOrderSms) TableName() string {
	return TableNameWorkOrderSms
}

func (w *WorkOrderSms) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&WorkOrderSms{})
}

func (w *WorkOrderSms) WorkOrderSmsCreate(tx *gorm.DB) error {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		InsertPayload:           w,
	}).GetError()
}

func (w *WorkOrderSms) GetSMSTypeByCode(code string) string {
	var SMSType string
	switch code {
	case "SMS_482880469":
		SMSType = constant.Register
	case "SMS_483325026":
		SMSType = constant.ResetPassword
	case "SMS_483280025":
		SMSType = constant.ChangePhone
	case "SMS_463225921":
		SMSType = constant.Abroad
	case "SMS_483305035":
		SMSType = constant.InstanceReleaseWarning
	case "SMS_482895408":
		SMSType = constant.InstanceExpireWarning
	case "SMS_483345027":
		SMSType = constant.BalanceWarning
	case "SMS_483215134":
		SMSType = constant.BankRemitAccept
	case "SMS_482955492":
		SMSType = constant.InstanceCreateRunning
	case "SMS_483170125":
		SMSType = constant.MachineStoppedErr
	case "SMS_483335067":
		SMSType = constant.SubUserUpdatePassword
	case "SMS_483285046":
		SMSType = constant.SubUserBind
	case "SMS_482820471":
		SMSType = constant.RegionRelease
	}
	return SMSType
}

func (w *WorkOrderSms) WorkOrderSmsUpdate(tx *gorm.DB, filter *db_helper.QueryFilters, um map[string]interface{}) (err error) {
	um["updated_at"] = time.Now()
	return db_helper.UpdateOne(db_helper.QueryDefinition{
		DBTransactionConnection: tx,
		ModelDefinition:         w,
		Filters:                 *filter,
	}, um).GetError()
}

func (w *WorkOrderSms) WorkOrderSmsGetAll(filter db_helper.QueryFilters) (list []WorkOrderSms, err error) {
	list = []WorkOrderSms{}
	err = db_helper.GetAll(db_helper.QueryDefinition{
		ModelDefinition: w,
		Filters:         filter,
	}, &list).GetError()
	return
}
