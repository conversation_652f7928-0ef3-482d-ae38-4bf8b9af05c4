package model

import (
	"database/sql"
	"gorm.io/datatypes"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

type CreateWorkOrderReq struct {
	MachineId    string `json:"machine_id"`
	Description  string `json:"description"`
	AssignUserId int    `json:"assign_user_id"`
	FaultLevel   string `json:"fault_level"`
	FaultType    string `json:"fault_type"`
}

type CreateMachineMonitorParams struct {
	MachineId             string `json:"machine_id"`
	MachineName           string `json:"machine_name"`
	RegionSign            string `json:"region_sign"`
	RegionName            string `json:"region_name"`
	FaultType             string `json:"fault_type"`
	OriginalMachineOnline int    `json:"original_machine_online"`
}

type CreateWorkOrderOperateUserParams struct {
	Uid           int    `json:"uid"`
	UserName      string `json:"user_name"`
	BackstageRole string `json:"backstage_role"`
}

type WorkOrderListSearchReq struct {
	MachineId     string             `json:"machine_id"`
	MachineName   string             `json:"machine_name"`
	Online        constant.OnOffLine `json:"online"`
	RegionSign    string             `json:"region_sign"`
	Status        string             `json:"status"`
	FaultLevel    string             `json:"fault_level"`
	FaultType     string             `json:"fault_type"`
	AssignUserId  int                `json:"assign_user_id"`
	Description   string             `json:"description"`
	GpuTypeId     int                `json:"gpu_type_id"`
	MachineIdList []string           `json:"-"`
	IsSearch      bool               `json:"-"`
}

type GetWorkOrderListReq struct {
	WorkOrderListSearchReq
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderListData `json:"list"`
}

type GetWorkOrderListData struct {
	ID                       int       `json:"id"`
	MachineId                string    `json:"machine_id"`
	MachineName              string    `json:"machine_name"`
	MachineStatus            int       `json:"machine_status"`
	GpuType                  string    `json:"gpu_type"`
	MachineGpuNumber         int64     `json:"machine_gpu_number"`
	MachineGpuUsed           int64     `json:"machine_gpu_used"`
	MachineGpuOnly10         int64     `json:"machine_gpu_only_10"`
	MachineAlias             string    `json:"machine_alias"`
	RegionSign               string    `json:"region_sign"`
	RegionName               string    `json:"region_name"`
	WorkOrderStatus          string    `json:"work_order_status"`
	FaultLevel               string    `json:"fault_level"`
	FaultType                string    `json:"fault_type"`
	HistoryFaultCount        int64     `json:"history_fault_count"`
	Description              string    `json:"description"`
	RecordCount              int64     `json:"record_count"`
	UserId                   int       `json:"user_id"`
	UserName                 string    `json:"user_name"`
	AssignUserId             int       `json:"assign_user_id"`
	AssignUserName           string    `json:"assign_user_name"`
	OnLineBackup             int       `json:"on_line_backup"`
	WorkOrderUUID            string    `json:"work_order_uuid"`
	CreatedAt                time.Time `json:"created_at"`
	UpdatedAt                time.Time `json:"updated_at"`
	SubscribeFinishUser      []int     `json:"subscribe_finish_user"`
	SubscribeMachineIdleUser []int     `json:"subscribe_machine_idle_user"`
}

type GetCSWorkOrderListData struct {
	ID                int            `json:"id"`
	MachineId         string         `json:"machine_id"`
	InstanceUUID      string         `json:"instance_uuid"`
	MachineName       string         `json:"machine_name"`
	MachineStatus     int            `json:"machine_status"`
	GpuType           string         `json:"gpu_type"`
	MachineGpuNumber  int64          `json:"machine_gpu_number"`
	MachineGpuUsed    int64          `json:"machine_gpu_used"`
	MachineGpuOnly10  int64          `json:"machine_gpu_only_10"`
	MachineAlias      string         `json:"machine_alias"`
	RegionSign        string         `json:"region_sign"`
	RegionName        string         `json:"region_name"`
	WorkOrderStatus   string         `json:"work_order_status"`
	HistoryFaultCount int64          `json:"history_fault_count"`
	Description       string         `json:"description"`
	AttachmentEntity  AttachmentInfo `json:"attachment_entity"`
	Tag               string         `json:"tag"`
	UserId            int            `json:"user_id"`
	UserName          string         `json:"user_name"`
	AssignUserId      int            `json:"assign_user_id"`
	AssignUserName    string         `json:"assign_user_name"`
	OnLineBackup      int            `json:"on_line_backup"`
	WorkOrderUUID     string         `json:"work_order_uuid"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	RecordCount       int64          `json:"record_count"`
}

type FileData struct {
	FileName string `json:"file_name"`
	FileUuid string `json:"file_uuid"`
}

type CreateWorkOrderRecordReq struct {
	Content       string     `json:"content"`
	WorkOrderUUID string     `json:"work_order_uuid"`
	Attachment    []FileData `json:"attachment"`
	CustomId      int        `json:"custom_id"`
}

type UpdateWorkerOrderStatusReq struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	Status        string `json:"status"`
}

type UpdateWorkOrderReq struct {
	WorkOrderUUID      string `json:"work_order_uuid"`
	MachineId          string `json:"machine_id"`
	Description        string `json:"description"`
	AssignUserId       int    `json:"assign_user_id"`
	FaultLevel         string `json:"fault_level"`
	FaultType          string `json:"fault_type"`
	IsChangeAssignUser bool   `json:"is_change_assign_user"`
	CustomId           int    `json:"custom_id"`
}

type UpdateWordOrderAssignReq struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	AssignUserId  int    `json:"assign_user_id"`
}

type DisposeMachineMonitorReq struct {
	Id            int    `json:"id"`
	ProcessResult string `json:"process_result"`
}

type MachineMonitorListSearchReq struct {
	MachineId   string `json:"machine_id"`
	MachineName string `json:"machine_name"`
	RegionSign  string `json:"region_sign"`
	Status      string `json:"status"`
	FaultType   string `json:"fault_type"`
}

type GetMachineMonitorListReq struct {
	MachineMonitorListSearchReq
	CustomID int `json:"custom_id"`
	db_helper.GetPagedRangeRequest
}

type MachineMonitorSearchReq struct {
	MachineId string `json:"machine_id"`
	Status    string `json:"status"`
	FaultType string `json:"fault_type"`
	CustomID  int    `json:"custom_id"`
}

type GetMachineMonitorListResp struct {
	*db_helper.PagedData
	List []GetMachineMonitorListData `json:"list"`
}

type GetMachineMonitorListData struct {
	Id                    int       `json:"id"`
	MachineId             string    `json:"machine_id"`
	MachineName           string    `json:"machine_name"`
	MachineStatus         int       `json:"machine_status"`
	MachineAlias          string    `json:"machine_alias"`
	OnLineBackup          int       `json:"on_line_backup"`
	RegionSign            string    `json:"region_sign"`
	RegionName            string    `json:"region_name"`
	Status                string    `json:"status"`
	FaultType             string    `json:"fault_type"`
	ProcessResult         string    `json:"process_result"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
	MachineOriginalOnline int       `json:"machine_original_online"`
}

type GetWorkOrderDetailReq struct {
	WorkOrderUuid string `json:"work_order_uuid"`
}

type GetWorkOrderDealProcessData struct {
	ID             int        `json:"id"`
	CreatedAt      time.Time  `json:"created_at"`
	UserID         int        `json:"user_id"`
	UserName       string     `json:"user_name"`
	Operate        string     `json:"operate"`
	AssignUserId   int        `json:"assign_user_id"`
	AssignUserName string     `json:"assign_user_name"`
	RecordId       int        `json:"record_id"`
	Record         string     `json:"record"`
	Attachment     []FileData `json:"attachment"`
	Status         string     `json:"status"`
}

type GetWorkOrderDetailResp struct {
	WorkOrderUUID   string                        `json:"work_order_uuid"`
	MachineId       string                        `json:"machine_id"`
	Description     string                        `json:"description"`
	DealProcessList []GetWorkOrderDealProcessData `json:"deal_process_list"`
}

type GetWorkOrderUserListData struct {
	ID       int    `json:"id"`
	Uid      int    `json:"uid"`
	UserName string `json:"user_name"`
}

type GetWorkOrderUserListResp struct {
	List []GetWorkOrderUserListData `json:"list"`
}

type UpdateFinishSubscribeUserReq struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	//SubscribeUser []int  `json:"subscribe_user"`
	IsSubscribe bool `json:"is_subscribe"`
	UserId      int  `json:"user_id"`
}

type UpdateMachineIdleUserReq struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	SubscribeUser []int  `json:"subscribe_user"`
	IsSubscribe   bool   `json:"is_subscribe"`
	UserId        int    `json:"user_id"`
}

type CreateMachineMonitorReq struct {
	TenantID  int    `json:"tenant_id"`
	MachineId string `json:"machine_id"`
	FaultType string `json:"fault_type"`
}

type CreateWorkOrderTenantReq struct {
	Name       string `json:"name"`
	TenantSign string `json:"tenant_sign"`
	Role       string `json:"role"`
	Remark     string `json:"remark"`
}

type CreateWorkOrderUserReq struct {
	TenantID     int    `json:"tenant_id"`
	Username     string `json:"username"`
	UUID         string `json:"uuid"`
	UserRole     string `json:"user_role"`
	FeishuBotUrl string `json:"feishu_bot_url"` // 飞书机器人地址
}

type GetWorkOrderTenantListReq struct {
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderTenantListData struct {
	Id         int       `json:"id"`
	Name       string    `json:"name"`
	Role       string    `json:"role"`
	TenantSign string    `json:"tenant_sign"`
	CreatedAt  time.Time `json:"created_at"`
	Remark     string    `json:"remark"`
}

type GetWorkOrderTenantListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderTenantListData `json:"list"`
}

type GetWorkOrderTenantUserListReq struct {
	ProviderId int    `json:"provider_id"`
	CustomId   int    `json:"custom_id"`
	Status     string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderTenantUserListData struct {
	ID         int       `json:"id"`
	TenantID   int       `json:"tenant_id"`
	TenantName string    `json:"tenant_name"`
	Username   string    `json:"username"`
	UUID       string    `json:"uuid"`
	UserRole   string    `json:"user_role"`
	UserStatus string    `json:"user_status"`
	CreatedAt  time.Time `json:"created_at"`
}

type GetWorkOrderTenantUserListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderTenantUserListData `json:"list"`
}

type CreateWorkOrderMaterialReq struct {
	ProviderID int    `json:"provider_id"`
	Name       string `json:"name"`
	Sort       string `json:"sort"`
	Source     string `json:"source"`
	Remark     string `json:"remark"`
}

type UpdateWorkOrderMaterialReq struct {
	MaterialID int    `json:"material_id"`
	Name       string `json:"name"`
	Sort       string `json:"sort"`
	Source     string `json:"source"`
	Remark     string `json:"remark"`
}

type GetWorkOrderMaterialListReq struct {
	ProviderID int    `json:"provider_id"`
	Name       string `json:"name"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderMaterialListData struct {
	ID             int       `json:"id"`
	Name           string    `json:"name"`
	Sort           string    `json:"sort"`
	Source         string    `json:"source"`
	Remark         string    `json:"remark"`
	CreateUserName string    `json:"create_user_name"`
	CreatedAt      time.Time `json:"created_at"`
}

type GetWorkOrderMaterialListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderMaterialListData `json:"list"`
}

type MaterialData struct {
	MaterialID int `json:"material_id"`
	Count      int `json:"count"`
}

type CreateWorkOrderMachineTypeReq struct {
	Name         string         `json:"name"`
	MaterialList []MaterialData `json:"material_list"`
	Remark       string         `json:"remark"`
	ProviderID   int            `json:"provider_id"`
}

type MaterialUpdate struct {
	MaterialID int    `json:"material_id"`
	Count      int    `json:"count"`
	Operate    string `json:"operate"` // update add delete
}

type UpdateWorkOrderMachineTypeReq struct {
	MachineTypeID int              `json:"machine_type_id"`
	Name          string           `json:"name"`
	Remark        string           `json:"remark"`
	MaterialList  []MaterialUpdate `json:"material_list"`
}

type GetWorkOrderMachineTypeListReq struct {
	ProviderID int    `json:"provider_id"`
	Name       string `json:"name"`
	db_helper.GetPagedRangeRequest
}

type MaterialInfo struct {
	MaterialId   int    `json:"material_id"`
	MaterialSort string `json:"material_sort"`
	MaterialName string `json:"material_name"`
	Count        int    `json:"count"`
}

type GetWorkOrderMachineTypeListData struct {
	ID             int            `json:"id"`
	Name           string         `json:"name"`
	MaterialInfo   []MaterialInfo `json:"material_info"`
	Remark         string         `json:"remark"`
	CreateUserName string         `json:"create_user_name"`
	MachineNumber  int64          `json:"machine_number"`
	CreatedAt      time.Time      `json:"created_at"`
}

type GetWorkOrderMachineTypeListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderMachineTypeListData `json:"list"`
}

type GetWorkOrderMachineListReq struct {
	ProviderID int    `json:"provider_id"`
	Status     string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderProviderMachineListData struct {
	ID              int        `json:"id"`
	MachineID       string     `json:"machine_id"`
	MachineTypeID   int        `json:"machine_type_id"`
	MachineTypeName string     `json:"machine_type_name"`
	CreateUserName  string     `json:"create_user_name"`
	Status          string     `json:"status"`
	CustomID        int        `json:"custom_id"`
	CustomName      string     `json:"custom_name"`
	CreatedAt       time.Time  `json:"created_at"`
	Remark          string     `json:"remark"`
	DeliverTime     *time.Time `json:"deliver_time"`
}

type GetWorkOrderProviderMachineListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderProviderMachineListData `json:"list"`
}

type GetWorkOrderCustomMachineListReq struct {
	CustomID int `json:"custom_id"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderCustomMachineData struct {
	ID                  int            `json:"id"`
	MachineID           string         `json:"machine_id"`
	CustomBindMachineID string         `json:"custom_bind_machine_id"`
	ProviderName        string         `json:"provider_name"`
	MaterialInfo        []MaterialInfo `json:"material_info"`
	MachineRoom         string         `json:"machine_room"` // 机房
	CabinetPosition     string         `json:"cabinet_position"`
	PublicIp            string         `json:"public_ip"` // 公网ip
	PrivateIp           string         `json:"private_ip"`
	IPMI                string         `json:"ipmi"`
	Purpose             string         `json:"purpose"` // 用途
	DeliverTime         *time.Time     `json:"deliver_time"`
	Remark              string         `json:"remark"`
}

type GetWorkOrderCustomMachineListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderCustomMachineData `json:"list"`
}

type CreateWorkOrderMachineReq struct {
	ProviderID     int    `json:"provider_id"`
	MachineTypeID  int    `json:"machine_type_id"`
	Number         int    `json:"number"`
	StartMachineId string `json:"start_machine_id"` // 机器id起始值
	CustomID       int    `json:"custom_id"`
	Remark         string `json:"remark"`
}

type CreateWorkOrderTenantRelationshipReq struct {
	ProviderID int    `json:"provider_id"`
	CustomName string `json:"custom_name"`
}

type GetWorkOrderTenantRelationshipReq struct {
	ProviderID int `json:"provider_id"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderTenantRelationList struct {
	CustomID   int       `json:"custom_id"`
	CustomName string    `json:"custom_name"`
	Remark     string    `json:"remark"`
	CustomSign string    `json:"custom_sign"`
	CreatedAt  time.Time `json:"created_at"`
}

type GetWorkOrderTenantRelationshipResp struct {
	*db_helper.PagedData
	List []GetWorkOrderTenantRelationList `json:"list"`
}

type UpdateWorkOrderMachineStatusReq struct {
	IDs    []int  `json:"ids"`
	Status string `json:"status"`
}

type UpdateWorkOrderMachineCustomReq struct {
	IDs      []int `json:"ids"`
	CustomID int   `json:"custom_id"`
}

type GetWorkOrderMachineTypeInfoReq struct {
	ProviderID int `json:"provider_id"`
}

type GetWorkOrderMachineTypeInfo struct {
	MachineTypeID   int    `json:"machine_type_id"`
	MachineTypeName string `json:"machine_type_name"`
}

type GetWorkOrderMachineTypeInfoResp struct {
	List []GetWorkOrderMachineTypeInfo `json:"list"`
}

type GetWorkOrderCustomInfoReq struct {
	ProviderID int    `json:"provider_id"`
	Name       string `json:"name"`
}

type GetWorkOrderMaterialInfoReq struct {
	MachineId string `json:"machine_id"`
}

type GetWorkOrderMaterialInfoResp struct {
	MaterialInfo []MaterialInfo `json:"material_info"`
}

type GetWorkOrderCustomInfo struct {
	CustomID   int    `json:"custom_id"`
	CustomName string `json:"custom_name"`
	TenantSign string `json:"tenant_sign"`
}

type GetWorkOrderCustomInfoResp struct {
	List []GetWorkOrderCustomInfo `json:"list"`
}

type GetWorkOrderCustomListReq struct {
	Name string `json:"name"`
}
type GetWorkOrderCustomListResp struct {
	List []WorkOrderTenant `json:"list"`
}

type GetWorkOrderProviderInfoReq struct {
	TenantID int `json:"tenant_id"`
}

type GetWorkOrderProviderInfo struct {
	ProviderID   int    `json:"custom_id"`
	ProviderName string `json:"custom_name"`
}

type GetWorkOrderProviderInfoResp struct {
	List []GetWorkOrderProviderInfo `json:"list"`
}

type WorkOrderMachineImportData struct {
	MachineID           string `json:"machine_id"`
	CustomBindMachineID string `json:"custom_bind_machine_id"`
	MachineRoom         string `json:"machine_room"`
	CabinetPosition     string `json:"cabinet_position"`
	PublicIp            string `json:"public_ip"` // 公网ip
	PrivateIp           string `json:"private_ip"`
	AccountPassword1    string `json:"account_password1"` // 账号1
	AccountPassword2    string `json:"account_password2"`
	IPMI                string `json:"ipmi"`
	IPMIAccountPassword string `json:"ipmi_account_password"`
	Purpose             string `json:"purpose"` // 用途
	Remark              string `json:"remark"`
}

type WorkOrderMachineImportReq struct {
	List     []WorkOrderMachineImportData `json:"list"`
	CustomID int                          `json:"custom_id"`
}

type UpdateWorkOrderMachineReq struct {
	ID                  int    `json:"id"`
	MachineID           string `json:"machine_id"`
	CustomBindMachineID string `json:"custom_bind_machine_id"`
	MachineRoom         string `json:"machine_room"` // 机房
	CabinetPosition     string `json:"cabinet_position"`
	PublicIp            string `json:"public_ip"` // 公网ip
	PrivateIp           string `json:"private_ip"`
	IPMI                string `json:"ipmi"`
	IPMIAccountPassword string `json:"ipmi_account_password"`
	Purpose             string `json:"purpose"`           // 用途
	AccountPassword1    string `json:"account_password1"` // 账号1
	AccountPassword2    string `json:"account_password2"`
	Remark              string `json:"remark"`
}

type UpdateWorkOrderMachineByProviderReq struct {
	MachineID     string `json:"machine_id"`
	MachineTypeID int    `json:"machine_type_id"`
	CustomID      int    `json:"custom_id"`
	Remark        string `json:"remark"`
}

type CreateWorkOrderSparePartReq struct {
	ProviderID  int    `json:"provider_id"`
	MachineID   string `json:"machine_id"`
	MachineRoom string `json:"machine_room"` // 机房
	CustomID    int    `json:"custom_id"`
	Remark      string `json:"remark"`
	MaterialID  int    `json:"material_id"`
	Count       int64  `json:"count"`
}

type UpdateWorkOrderSparePartByProviderReq struct {
	ID          int    `json:"id"`
	MachineID   string `json:"machine_id"`
	MachineRoom string `json:"machine_room"` // 机房
	CustomID    int    `json:"custom_id"`
	Remark      string `json:"remark"`
	MaterialID  int    `json:"material_id"`
	Count       int64  `json:"count"`
}

type CreateDispatchOrderSparePartReq struct {
	ProviderID       int            `json:"provider_id"`
	CustomID         int            `json:"custom_id"`
	MachineRoom      string         `json:"machine_room"` // 机房
	RecipientName    string         `json:"recipient_name"`
	RecipientPhone   string         `json:"recipient_phone"`   // 收件人电话
	RecipientAddress string         `json:"recipient_address"` // 收件人地址
	ExpressNumber    string         `json:"express_number"`
	MaterialList     []MaterialData `json:"material_list"`
}

type UpdateDispatchOrderUpdateByProviderReq struct {
	ID                 int              `json:"id"`
	CustomID           int              `json:"custom_id"`
	MachineRoom        string           `json:"machine_room"` // 机房
	RecipientName      string           `json:"recipient_name"`
	RecipientPhone     string           `json:"recipient_phone"`   // 收件人电话
	RecipientAddress   string           `json:"recipient_address"` // 收件人地址
	ExpressNumber      string           `json:"express_number"`
	MaterialUpdateList []MaterialUpdate `json:"material_update_list"`
}

type GetWorkOrderSparePartProviderListReq struct {
	ProviderID int    `json:"provider_id"`
	Status     string `json:"status"`

	db_helper.GetPagedRangeRequest
}

type GetWorkOrderSparePartList struct {
	ID              int        `json:"id"`
	MaterialID      int        `json:"material_id"`
	MaterialName    string     `json:"material_name"`
	MaterialCount   int64      `json:"material_count"`
	MachineID       string     `json:"machine_id"`
	MachineTypeName string     `json:"machine_type_name"`
	CustomID        int        `json:"custom_id"`
	CustomName      string     `json:"custom_name"`
	MachineRoom     string     `json:"machine_room"` // 机房
	CreateUserName  string     `json:"create_user_name"`
	Status          string     `json:"status"`
	DispatchBillID  int        `json:"dispatch_bill_id"`
	DeliveryTime    *time.Time `json:"delivery_time"`
	CreatedAt       time.Time  `json:"created_at"`
	Remark          string     `json:"remark"`
}

type GetWorkOrderSparePartProviderListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderSparePartList `json:"list"`
}

type GetWorkOrderSparePartCustomListReq struct {
	CustomID int    `json:"Custom_id"`
	Status   string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderSparePartCustomList struct {
	ID             int       `json:"id"`
	MaterialName   string    `json:"material_name"`
	MaterialCount  int64     `json:"material_count"`
	MachineID      string    `json:"machine_id"`
	ProviderName   string    `json:"provider_name"`
	Status         string    `json:"status"`
	CreatedAt      time.Time `json:"created_at"`
	Remark         string    `json:"remark"`
	DispatchBillID int       `json:"dispatch_bill_id"`
	ExpressNumber  string    `json:"express_number"`
}

type GetWorkOrderSparePartCustomListResp struct {
	*db_helper.PagedData
	List []GetWorkOrderSparePartCustomList `json:"list"`
}

type GetDispatchOrderSparePartCustomListReq struct {
	ProviderID int    `json:"provider_id"`
	Status     string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetDispatchOrderSparePartCustomList struct {
	Id               int            `json:"id"`
	MaterialInfo     []MaterialInfo `json:"material_info"`
	Status           string         `json:"status"`
	MachineRoom      string         `json:"machine_room"` // 机房
	RecipientName    string         `json:"recipient_name"`
	RecipientPhone   string         `json:"recipient_phone"`   // 收件人电话
	RecipientAddress string         `json:"recipient_address"` // 收件人地址
	ExpressNumber    string         `json:"express_number"`
	CustomName       string         `json:"custom_name"`
	CreateUserName   string         `json:"create_user_name"`
	CreatedAt        time.Time      `json:"created_at"`
	DeliveryTime     *time.Time     `json:"delivery_time"`
	CustomID         int            `json:"custom_id"`
}

type GetDispatchOrderSparePartCustomListResp struct {
	*db_helper.PagedData
	List []GetDispatchOrderSparePartCustomList `json:"list"`
}

type BindDispatchOrderIDReq struct {
	WorkOrderSparePartIDs []int `json:"work_order_spare_part_ids"`
	DispatchOrderID       int   `json:"dispatch_order_id"`
	ProviderID            int   `json:"provider_id"`
}

type BindExpressNumberReq struct {
	DispatchOrderSparePartID int    `json:"dispatch_order_spare_part_id"`
	ExpressNumber            string `json:"express_number"`
}

type UpdateDispatchOrderSparePartStatusReq struct {
	DispatchOrderSparePartIDs []int  `json:"dispatch_order_spare_part_ids"`
	Status                    string `json:"status"`
}

type UpdateWorkOrderSparePartStatusReq struct {
	WorkOrderSparePartID int    `json:"work_order_spare_part_id"`
	Status               string `json:"status"`
}

type CreateWorkOrderRepairReq struct {
	MachineID    string         `json:"machine_id"`
	MachineRoom  string         `json:"machine_room"`
	CustomerID   int            `json:"customer_id"`
	ProviderID   int            `json:"provider_id"`
	Remark       string         `json:"remark"`
	MaterialInfo []MaterialData `json:"material_info"`
}

type GetWorkOrderCustomRepairReq struct {
	CustomID int    `json:"custom_id"`
	Status   string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderCustomRepairData struct {
	ID                    int            `json:"id"`
	MaterialInfo          []MaterialInfo `json:"material_info"`
	MachineID             string         `json:"machine_id"`
	MachineRoom           string         `json:"machine_room"`
	ProviderName          string         `json:"provider_name"`
	DispatchOrderRepairID int            `json:"dispatch_order_repair_id"`
	Status                string         `json:"status"`
	DeliveryTime          *time.Time     `json:"delivery_time"`
	CreatedAt             time.Time      `json:"created_at"`
	Remark                string         `json:"remark"`
}

type GetWorkOrderCustomRepairResp struct {
	*db_helper.PagedData
	List []GetWorkOrderCustomRepairData `json:"list"`
}

type GetWorkOrderProviderRepairReq struct {
	ProviderID int    `json:"provider_id"`
	Status     string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderProviderRepairData struct {
	ID                    int            `json:"id"`
	MaterialInfo          []MaterialInfo `json:"material_info"`
	MachineID             string         `json:"machine_id"`
	MachineRoom           string         `json:"machine_room"`
	CustomName            string         `json:"custom_name"`
	DispatchOrderRepairID int            `json:"dispatch_order_repair_id"`
	Status                string         `json:"status"`
	CreatedAt             time.Time      `json:"created_at"`
	Remark                string         `json:"remark"`
	DeliveryTime          *time.Time     `json:"delivery_time"`
	ExpressNumber         string         `json:"express_number"`
}

type GetWorkOrderProviderRepairResp struct {
	*db_helper.PagedData
	List []GetWorkOrderProviderRepairData `json:"list"`
}

type CreateDispatchOrderRepairReq struct {
	ProviderID         int    `json:"provider_id"`
	CustomID           int    `json:"custom_id"`
	MachineRoom        string `json:"machine_room"`
	RecipientName      string `json:"recipient_name"`
	RecipientPhone     string `json:"recipient_phone"`
	RecipientAddress   string `json:"recipient_address"`
	ExpressNumber      string `json:"express_number"`
	WorkOrderRepairIDs []int  `json:"work_order_repair_ids"`
}

type GetDispatchOrderCustomRepairListReq struct {
	CustomID int    `json:"custom_id"`
	Status   string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetDispatchOrderCustomRepairList struct {
	Id               int            `json:"id"`
	MaterialInfo     []MaterialInfo `json:"material_info"`
	Status           string         `json:"status"`
	ProviderName     string         `json:"provider_name"`
	MachineRoom      string         `json:"machine_room"`
	RecipientName    string         `json:"recipient_name"`
	RecipientPhone   string         `json:"recipient_phone"`
	RecipientAddress string         `json:"recipient_address"`
	ExpressNumber    string         `json:"express_number"`
	DispatchTime     *time.Time     `json:"dispatch_time"`
	CreateUserName   string         `json:"create_user_name"`
	CreateAt         time.Time      `json:"create_at"`
	Remark           string         `json:"remark"`
}

type GetDispatchOrderCustomRepairListResp struct {
	*db_helper.PagedData
	List []GetDispatchOrderCustomRepairList `json:"list"`
}

type GetDispatchOrderProviderRepairListReq struct {
	ProviderID int    `json:"provider_id"`
	Status     string `json:"status"`
	db_helper.GetPagedRangeRequest
}

type GetDispatchOrderProviderRepairList struct {
	Id               int            `json:"id"`
	MaterialInfo     []MaterialInfo `json:"material_info"`
	Status           string         `json:"status"`
	CustomName       string         `json:"custom_name"`
	MachineRoom      string         `json:"machine_room"`
	RecipientName    string         `json:"recipient_name"`
	RecipientPhone   string         `json:"recipient_phone"`
	RecipientAddress string         `json:"recipient_address"`
	ExpressNumber    string         `json:"express_number"`
	DispatchTime     *time.Time     `json:"dispatch_time"`
	CreateUserName   string         `json:"create_user_name"`
	CreateAt         time.Time      `json:"create_at"`
	Remark           string         `json:"remark"`
}

type GetDispatchOrderProviderRepairListResp struct {
	*db_helper.PagedData
	List []GetDispatchOrderProviderRepairList `json:"list"`
}

type GetWorkOrderRepairNoBindDispatchListReq struct {
	CustomId int `json:"custom_id"`
}

type GetWorkOrderRepairNoBindDispatch struct {
	Id           int            `json:"id"`
	MaterialInfo []MaterialInfo `json:"material_info"`
	MachineId    string         `json:"machine_id"`
	ProviderName string         `json:"provider_name"`
}

type GetWorkOrderRepairNoBindDispatchListResp struct {
	List []GetWorkOrderRepairNoBindDispatch `json:"list"`
}

type UpdateDispatchOrderRepairStatusReq struct {
	DispatchOrderRepairIDs []int  `json:"dispatch_order_repair_ids"`
	Status                 string `json:"status"`
}

type UpdateWorkOrderRepairStatusReq struct {
	WorkOrderRepairID int    `json:"work_order_repair_id"`
	Status            string `json:"status"`
}

type RepairBindExpressNumberReq struct {
	ExpressNumber         string `json:"express_number"`
	DispatchOrderRepairId int    `json:"dispatch_order_repair_id"`
}

type GetTenantSwitchData struct {
	TenantId   int    `json:"tenant_id"`
	TenantName string `json:"tenant_name"`
	TenantRole string `json:"tenant_role"`
	TenantSign string `json:"tenant_sign"`
}

type GetTenantSwitchList struct {
	List []GetTenantSwitchData `json:"list"`
}

type SwitchTenantReq struct {
	TenantId int `json:"tenant_id"`
}

type SwitchTenantResp struct {
	Token string `json:"token"`
}

type GetWorkOrderMachineDetailReq struct {
	MachineID string `json:"machine_id"`
}

type GetWorkOrderMachineDetailResp struct {
	MachineID    string         `json:"machine_id"`
	ProviderID   int            `json:"provider_id"`
	ProviderName string         `json:"provider_name"`
	MaterialInfo []MaterialInfo `json:"material_info"`
}

type GetUserRoleDetailResp struct {
	ID            int    `json:"id"`
	BackstageRole string `json:"backstage_role"`
}

type CreateCustomerWorkOrderReq struct {
	InstanceUUID string     `json:"instance_uuid"`
	AssignUserId int        `json:"assign_user_id"`
	Description  string     `json:"description"`
	Attachment   []FileData `json:"attachment"`
	Tags         []string   `json:"tags"`
}

type CustomerWorkOrderSearchReq struct {
	MachineId     string             `json:"machine_id"`
	MachineName   string             `json:"machine_name"`
	InstanceUUID  string             `json:"instance_uuid"`
	Status        string             `json:"status"`
	Tag           string             `json:"tag"`
	UserId        int                `json:"user_id"`
	AssignUserId  int                `json:"assign_user_id"`
	Online        constant.OnOffLine `json:"online"`
	GpuTypeId     int                `json:"gpu_type_id"`
	MachineIdList []string           `json:"-"`
	IsSearch      bool               `json:"-"`
}

type GetCustomerWorkOrderListReq struct {
	CustomerWorkOrderSearchReq
	db_helper.GetPagedRangeRequest
}

type GetCSWorkOrderDetailReq struct {
	WorkOrderUuid string `json:"work_order_uuid"`
}

type GetCSWorkOrderDetailResp struct {
	WorkOrderUUID   string                          `json:"work_order_uuid"`
	MachineId       string                          `json:"machine_id"`
	InstanceUUID    constant.InstanceUUIDType       `json:"instance_uuid"`
	Description     string                          `json:"description"`
	Attachment      []FileData                      `json:"attachment"` // 工单附件
	DealProcessList []GetCSWorkOrderDealProcessData `json:"deal_process_list"`
	Tag             string                          `json:"tag"`
}

type GetCSWorkOrderDealProcessData struct {
	ID             int        `json:"id"`
	CreatedAt      time.Time  `json:"created_at"`
	UserID         int        `json:"user_id"`
	UserName       string     `json:"user_name"`
	Operate        string     `json:"operate"`
	AssignUserId   int        `json:"assign_user_id"`
	AssignUserName string     `json:"assign_user_name"`
	RecordId       int        `json:"record_id"`
	Record         string     `json:"record"`
	Attachment     []FileData `json:"attachment"`
	Status         string     `json:"status"`
}

type UpdateCSWorkOrderReq struct {
	UUID         string     `json:"uuid"`
	InstanceUUID string     `json:"instance_uuid"`
	AssignUserId int        `json:"assign_user_id"`
	Description  string     `json:"description"`
	Attachment   []FileData `json:"attachment"`
	Tags         []string   `json:"tags"`
}

type UpdateCSWorkerOrderStatusReq struct {
	TenantId      int    `json:"-"` // 操作人租户id
	Uuid          string `json:"-"` // 操作人Uuid
	WorkOrderUUID string `json:"work_order_uuid"`
	Status        string `json:"status"`
}

type UpdateCSWordOrderAssignReq struct {
	TenantId      int    `json:"-"` // 操作人租户id
	Uuid          string `json:"-"` // 操作人Uuid
	WorkOrderUUID string `json:"work_order_uuid"`
	AssignUserId  int    `json:"assign_user_id"`
}

type CreateCSWorkOrderRecordReq struct {
	Content       string     `json:"content"`
	WorkOrderUUID string     `json:"work_order_uuid"`
	Attachment    []FileData `json:"attachment"`
	CustomId      int        `json:"custom_id"`
	Uuid          string     `json:"-"`
}

type CreateSmsTemplateReq struct {
	Name               string         `json:"name"`
	AliyunTemplateId   string         `json:"aliyun_template_id"`
	TemplateContent    string         `json:"template_content"`
	TemplateParamsInfo datatypes.JSON `json:"template_params_info"`
}
type DeleteSmsTemplateReq struct {
	SmsTemplateId int `json:"sms_template_id"`
}

type GetSmsTemplateListReq struct {
	db_helper.GetPagedRangeRequest
}

type GetSmsTemplateListData struct {
	SmsTemplateId    int       `json:"sms_template_id"`
	Name             string    `json:"name"`
	AliyunTemplateId string    `json:"aliyun_template_id"`
	TemplateContent  string    `json:"template_content"`
	CreatedAt        time.Time `json:"created_at"`
	UserName         string    `json:"user_name"`
}

type GetSmsTemplateDetailReq struct {
	SmsTemplateId int `json:"sms_template_id"`
}

type GetSmsTemplateData struct {
	SmsTemplateId               int            `json:"sms_template_id"`
	Name                        string         `json:"name"`
	TemplateContent             string         `json:"template_content"`
	TemplateParamsInfo          datatypes.JSON `json:"template_params_info"`
	IsInstanceIdAutoAssociation bool           `json:"is_instance_id_auto_association"`
	AliyunTemplateId            string         `json:"aliyun_template_id"`
}

type GetSmsTemplateInfoResp struct {
	List []GetSmsTemplateData `json:"list"`
}

type SendSmsReq struct {
	AliyunTemplateId    string                 `json:"aliyun_template_id"`
	Phone               string                 `json:"phone"`
	PhoneArea           string                 `json:"phone_area"`
	TemplateContent     string                 `json:"template_content"` // 拼接后的内容
	SendBasis           constant.SendBasisType `json:"send_basis"`
	TemplateParamsInfo  datatypes.JSON         `json:"template_params_info"` // 填充后的参数
	IsSendFeishuNotify  bool                   `json:"is_send_feishu_notify"`
	InstanceUuid        string                 `json:"instance_uuid"`
	SmsTemplateId       int                    `json:"sms_template_id"`
	FeishuNotifyUser    []int                  `json:"feishu_notify_user"`
	NotifyRecordIsRetry bool                   `json:"notify_record_is_retry"`
}

type ResendSmsReq struct {
	AliyunTemplateId string         `json:"aliyun_template_id"`
	Phone            string         `json:"phone"`
	PhoneArea        string         `json:"phone_area"`
	SmsParamsInfo    datatypes.JSON `json:"sms_params_info"` // 参数
}

type TemplateInfo struct {
	Template     string                 `json:"template"`
	Phone        string                 `json:"phone"`
	PhoneArea    string                 `json:"phone_area"`
	InstanceUuid string                 `json:"instance_uuid"`
	MachineId    string                 `json:"machine_machine"`
	ParamsInfo   map[string]interface{} `json:"params_info"`
}

type WorkOrderSendSmsSearchReq struct {
	Phone        string `json:"phone"`
	MachineId    string `json:"machine_id"`
	InstanceUuid string `json:"instance_uuid"`
	SendUserId   int    `json:"send_user_id"`
}

type GetSendSmsListReq struct {
	WorkOrderSendSmsSearchReq
	db_helper.GetPagedRangeRequest
}

type GetWorkOrderSmsListData struct {
	SmsID              int                    `json:"sms_id"`
	SmsTemplateId      int                    `json:"sms_template_id"`
	TemplateName       string                 `json:"template_name"`
	Phone              string                 `json:"phone"`
	PhoneArea          string                 `json:"phone_area"`
	SendChannel        string                 `json:"send_channel"`
	SmsParamsInfo      datatypes.JSON         `json:"sms_params_info"`
	SmsContent         string                 `json:"sms_content"`
	SmsStatus          constant.SmsStatusType `json:"sms_status"`
	MachineId          string                 `json:"machine_id"`
	InstanceUuid       string                 `json:"instance_uuid"`
	SendUserName       string                 `json:"send_user_name"`
	CreatedAt          time.Time              `json:"created_at"`
	IsSendFeishuNotify bool                   `json:"is_send_feishu_notify"`
	SendBasis          constant.SendBasisType `json:"send_basis"`
	FeishuNotifyUser   []int                  `json:"feishu_notify_user"`
}

type GetSmsInstanceIDInfoReq struct {
	MachineId               string `json:"machine_id"`
	MachineName             string `json:"machine_name"`
	IsRunningInstance       bool   `json:"is_running_instance"`
	IsNoPaygInstance        bool   `json:"is_no_payg_instance"`
	IsShutDownInThreeHours  bool   `json:"is_shutdown_in_three"`
	IsShutDownInTwelveHours bool   `json:"is_shutdown_in_twelve"`
}

type GetSmsInstanceIDInfoResp struct {
	HaveGpuResources bool                        `json:"have_gpu_resources"`
	InstanceUUID     constant.InstanceUUIDType   `json:"instance_uuid"`
	InstanceName     string                      `json:"instance_name"`
	Status           constant.InstanceStatusType `json:"status"`
	ChargeType       constant.ChargeType         `json:"charge_type"`
	StoppedAt        sql.NullTime                `json:"stopped_at"`
	ExpiredAt        sql.NullTime                `json:"expired_at"`
	CleanedEmojiName string                      `json:"cleaned_emoji_name"`
}

type GetInstanceInfoReq struct {
	InstanceUuid string `json:"instance_uuid"`
}

type InstanceNameInfo struct {
	Name             string `json:"name"`
	CleanedEmojiName string `json:"cleaned_emoji_name"`
}

type InstanceInfo struct {
	Phone     string `json:"phone"`
	PhoneArea string `json:"phone_area"`
}

type GetInstanceInfoResp struct {
	InstanceInfo []InstanceInfo `json:"instance_info"`
}

type GetUserPhoneDetailReq struct {
	Phone string `json:"phone"`
}

type CreateAgencyNotifyReq struct {
	NotifyContent string    `json:"notify_content"`
	MachineId     string    `json:"machine_id"`
	NotifyTime    time.Time `json:"notify_time"`
	NotifyUser    []int     `json:"notify_user"`
}

type DeleteAgencyNotifyReq struct {
	AgencyNotifyId int `json:"agency_notify_id"`
}

type GetAgencyNotifyListReq struct {
	db_helper.GetPagedRangeRequest
}

type GetAgencyNotifyListData struct {
	ID              int            `json:"id"`
	MachineId       string         `json:"machine_id"`
	NotifyContent   string         `json:"notify_content"`
	NotifyStatus    string         `json:"notify_status"`
	NotifyTime      sql.NullTime   `json:"notify_time"`
	NotifyUserName  []string       `json:"notify_user_name"`
	CreatedAt       time.Time      `json:"created_at"`
	CreateUserName  string         `json:"create_user_name"`
	SubNotifyStatus datatypes.JSON `json:"sub_notify_status"`
}

type SubNotifyStatus struct {
	NotifyUserId   int                       `json:"notify_user_id"`
	NotifyUserName string                    `json:"notify_user_name"`
	NotifyStatus   constant.NotifyStatusType `json:"notify_status"`
	NextNotifyTime sql.NullTime              `json:"next_notify_time"`
	FailedReason   string                    `json:"failed_reason"`
}

type NextRetryTimeInfo struct {
	NextRetryTimeStr string `json:"next_retry_time_str"`
	UserId           int    `json:"user_id"`
}

type CreateInstanceNotifyReq struct {
	MachineId  string         `json:"machine_id"`
	NotifyInfo datatypes.JSON `json:"notify_info"`
}

type UpdateInstanceNotifyReq struct {
	ID         int            `json:"id"`
	MachineId  string         `json:"machine_id"`
	NotifyInfo datatypes.JSON `json:"notify_info"`
}

type FinishInstanceNotifyReq struct {
	InstanceNotifyId int `json:"instance_notify_id"`
	//Status           string `json:"status"`
}

type WorkOrderInstanceNotifyListReq struct {
	WorkOrderInstanceNotifySearchReq
	db_helper.GetPagedRangeRequest
}

type GetInstanceNotifyListData struct {
	ID                   int                           `json:"id"`
	CreatedAt            time.Time                     `json:"created_at"`
	MachineId            string                        `json:"machine_id"`
	InstanceNotifyStatus constant.InstanceNotifyStatus `json:"instance_notify_status"`
	ReceiptCount         int64                         `json:"receipt_count"` // 回执数量
	NotifyInfo           datatypes.JSON                `json:"notify_info"`
	UserId               int                           `json:"user_id"` // 创建用户id
	UserName             string                        `json:"user_name"`
	TenantId             int                           `json:"tenant_id"`
}

type WorkOrderInstanceNotifySearchReq struct {
	MachineId    string `json:"machine_id"`
	InstanceUuid string `json:"instance_uuid"`
	Status       string `json:"status"`
}

type CreateInstanceReceiptReq struct {
	InstanceNotifyId int    `json:"instance_notify_id"`
	InstanceUUID     string `json:"instance_uuid"`
	ReceiptInfo      string `json:"receipt_info"`
}

type WorkOrderInstanceReceiptSearchReq struct {
	InstanceNotifyId int `json:"instance_notify_id"`
}

type WorkOrderInstanceReceiptListReq struct {
	WorkOrderInstanceReceiptSearchReq
	db_helper.GetPagedRangeRequest
}

type SendFeishuRemindReq struct {
	WorkOrderUUID string `json:"work_order_uuid"`
	RemindUser    []int  `json:"remind_user"`
}

type SubNotify struct {
	UserId    int  `json:"user_id"`
	IsSendMsg bool `json:"is_send_msg"`
}

type GetMachineRegionInfoReq struct {
	InstanceUuid string `json:"instance_uuid"`
}

type GetMachineRegionInfoResp struct {
	RegionSign   constant.RegionSignType `json:"region_sign"`
	MachineAlias string                  `json:"machine_alias"`
}
