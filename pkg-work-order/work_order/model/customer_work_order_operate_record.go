package model

import (
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"

	"gorm.io/gorm"
)

const TableNameCustomerWorkOrderOperateRecord = "customer_work_order_operate_record"

// CustomerWorkOrderOperateRecord 客服工单操作记录表
type CustomerWorkOrderOperateRecord struct {
	ID            int                                         `gorm:"column:id;AUTO_INCREMENT;unique_index;PRIMARY KEY" json:"id"`
	CreatedAt     time.Time                                   `gorm:"type:datetime;column:created_at;" json:"created_at"`
	UpdatedAt     time.Time                                   `gorm:"type:datetime;column:updated_at;" json:"updated_at"`
	DeletedAt     gorm.DeletedAt                              `gorm:"type:datetime;column:deleted_at;index" json:"-"`
	Operate       constant.CustomerWorkOrderOperateRecordType `gorm:"type:varchar(255);column:operate;" json:"operate"`
	UserID        int                                         `gorm:"column:user_id"`
	WorkOrderUUID string                                      `gorm:"type:varchar(255);column:work_order_uuid;" json:"work_order_uuid"`
	RecordId      int                                         `gorm:"type:int;column:record_id;" json:"record_id"`          // 记录uuid
	AssignUserId  int                                         `gorm:"type:int;column:assign_user_id" json:"assign_user_id"` // 指派人id
	Status        constant.CustomerWorkOrderStatus            `gorm:"type:varchar(255);column:status;" json:"status"`       // 工单状态
}

func (c *CustomerWorkOrderOperateRecord) TableName() string {
	return TableNameCustomerWorkOrderOperateRecord
}

func (c *CustomerWorkOrderOperateRecord) Init(dbConn *gorm.DB) error {
	return dbConn.AutoMigrate(&CustomerWorkOrderOperateRecord{})
}

func (c *CustomerWorkOrderOperateRecord) CustomerWorkOrderOperateCreate() (err error) {
	return db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: c,
		InsertPayload:   c,
	}).GetError()
}
