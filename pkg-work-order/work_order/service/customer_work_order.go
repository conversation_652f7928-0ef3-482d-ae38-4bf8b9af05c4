package service

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"strconv"
	"strings"
	"time"

	"github.com/levigross/grequests"
	"gorm.io/datatypes"
)

func (svc *WorkOrderService) CreateCSWorkOrder(params *model.CreateCustomerWorkOrderReq, uid int, workOrderUuid string, tenantID int) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	instance, err := svc.instance.GetInstance(constant.InstanceUUIDType(params.InstanceUUID))
	if err != nil {
		svc.log.WithField("err", err).WithField("instanceUuid", params.InstanceUUID).Error("get instance failed")
		return err
	}

	machine, err := svc.machine.Get(instance.MachineID)
	if err != nil {
		svc.log.WithField("err", err).Error("get machine failed")
		return
	}

	// 查询user是否operate user表存在
	operateUser, err := svc.GetWorkOrderTenantUserByUserId(uid)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", workOrderUuid).Error("get work order operate user failed")
		return
	}

	tagStr := strings.Join(params.Tags, ",")

	attachment := model.AttachmentInfo{Attachment: params.Attachment}
	customer := &model.CustomerServiceWorkOrder{
		WorkOrderUUID:    workOrderUuid,
		MachineId:        instance.MachineID,
		MachineName:      machine.MachineName,
		Online:           machine.Online,
		RegionSign:       machine.RegionSign,
		RegionName:       machine.RegionName,
		Description:      params.Description,
		AttachmentEntity: attachment,
		Status:           constant.CustomerWorkOrderStatusWaitProcess,
		Tag:              tagStr,
		UserId:           uid,
		AssignUserId:     params.AssignUserId,
		CustomID:         tenantID,
		InstanceUUID:     constant.InstanceUUIDType(params.InstanceUUID),
	}

	err = customer.CSWorkOrderCreate(nil)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", workOrderUuid).Error("create  customer work order failed")
		return
	}

	// TODO: 创建工单-发送飞书消息
	// 创建人
	if operateUser.FeishuBotUrl != "" {
		svc.SendCustomerWorkOrderMsg(operateUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDCSWorkOrderCreate,
				"template_version_name": constant.FeishuTemplateVersionCSWorkOrderCreate,
				"template_variable": map[string]interface{}{
					"machine_name":  machine.MachineName,
					"instance_uuid": params.InstanceUUID,
					"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatusWaitProcess),
					"operate_user":  operateUser.Username,
					"description":   params.Description,
				},
			},
		})
	}

	// 经办人
	if operateUser.ID != params.AssignUserId {
		assignUser, err := svc.GetWorkOrderTenantUserByUserId(params.AssignUserId)
		if err != nil {
			svc.log.ErrorE(err, "get work order assign user failed")
			return err
		}
		if assignUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(assignUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderCreate,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderCreate,
					"template_variable": map[string]interface{}{
						"machine_name":  machine.MachineName,
						"instance_uuid": params.InstanceUUID,
						"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatusWaitProcess),
						"operate_user":  operateUser.Username,
						"description":   params.Description,
					},
				},
			})
		}
	}

	return nil
}

func (svc *WorkOrderService) GetCSWorkOrderList(params *model.CustomerWorkOrderSearchReq, pageReq *db_helper.GetPagedRangeRequest, tenantID int) (paged *db_helper.PagedData, list []*model.CustomerServiceWorkOrder, err error) {
	list = make([]*model.CustomerServiceWorkOrder, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameCustomerServiceWorkOrder).
		Where("deleted_at is null").Where("custom_id = ?", tenantID)

	if params.Online == 7 {
		db = db.Where("online = ?", 0)
	}

	if params.Online != 0 && params.Online != 7 {
		db = db.Where("online = ?", params.Online)
	}

	if params.MachineId != "" {
		db = db.Where("machine_id = ?", params.MachineId)
	}

	if len(params.MachineIdList) != 0 {
		db = db.Where("machine_id in (?)", params.MachineIdList)
	} else if len(params.MachineIdList) == 0 && params.IsSearch {
		db = db.Where("machine_id = ?", "")
	}

	if params.MachineName != "" {
		db = db.Where("machine_name = ?", params.MachineName)
	}

	if params.InstanceUUID != "" {
		db = db.Where("instance_uuid = ?", params.InstanceUUID)
	}

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	if params.AssignUserId != 0 {
		db = db.Where("assign_user_id = ?", params.AssignUserId)
	}

	if params.UserId != 0 {
		db = db.Where("user_id = ?", params.UserId)
	}

	if params.Tag != "" {
		db = db.Where(fmt.Sprintf("MATCH(tag) AGAINST ('%s' IN BOOLEAN MODE)", params.Tag))
	}

	if pageReq.DateFrom != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') >= ?", pageReq.DateFrom)
	}
	if pageReq.DateTo != "" {
		db = db.Where("DATE_FORMAT(created_at,'%Y-%m-%d') <= ?", pageReq.DateTo)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetCSWorkOrderDetail(workOrderUuid string) (csWorkOrder *model.CustomerServiceWorkOrder, err error) {
	csWorkOrder = &model.CustomerServiceWorkOrder{}
	err = csWorkOrder.CSWorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{"work_order_uuid": workOrderUuid},
	})
	if err != nil {
		svc.log.WithField("work_order_uuid", workOrderUuid).WithError(err).Warn("get  cs work order by uuid failed.")
		return
	}

	return csWorkOrder, nil
}

func (svc *WorkOrderService) UpdateCSWorkOrder(params *model.UpdateCSWorkOrderReq, uid int) (err error) {
	update := make(map[string]interface{})
	csWorkOrder, err := svc.GetCSWorkOrderDetail(params.UUID)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", params.UUID).Error("get cs work order failed")
		return
	}

	if string(csWorkOrder.InstanceUUID) != params.InstanceUUID {
		instance, err := svc.instance.GetInstance(constant.InstanceUUIDType(params.InstanceUUID))
		if err != nil {
			svc.log.WithField("err", err).WithField("instanceUuid", params.InstanceUUID).Error("get instance failed")
			return err
		}
		machine, err := svc.machine.Get(instance.MachineID)
		if err != nil {
			svc.log.WithField("err", err).Error("get machine failed")
			return err
		}

		update["instance_uuid"] = params.InstanceUUID
		update["machine_id"] = machine.MachineID
		update["machine_name"] = machine.MachineName
		update["region_name"] = machine.RegionName
		update["region_sign"] = machine.RegionSign
	}

	// 查询user是否operate user表存在
	_, err = svc.GetWorkOrderTenantUserByUserId(uid)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", params.UUID).Error("get work order operate user failed")
		return
	}

	tagStr := strings.Join(params.Tags, ",")

	attachment := model.AttachmentInfo{Attachment: params.Attachment}

	attachmentJson, err := json.Marshal(attachment)
	if err != nil {
		svc.log.ErrorE(err, "handlerDCUpdateContainerParam marshal ContainerParam error")
		return
	}

	update["tag"] = tagStr
	update["attachment_info"] = attachmentJson
	update["assign_user_id"] = params.AssignUserId
	update["description"] = params.Description

	err = csWorkOrder.CSWorkOrderUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"work_order_uuid": params.UUID}},
		update)

	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) UpdateCSWorkOrderStatus(params *model.UpdateCSWorkerOrderStatusReq) (err error) {
	csWorkOrder := &model.CustomerServiceWorkOrder{}
	// 根据work order uuid获取operate_user assign_user
	err = csWorkOrder.CSWorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		return err
	}
	// 操作人
	operateUser, err := svc.GetWorkOrderCreateUser(params.Uuid, params.TenantId)
	if err != nil {
		svc.log.ErrorE(err, "get work order operate user failed")
		return
	}
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = csWorkOrder.CSWorkOrderUpdate(nil,
			&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"work_order_uuid": params.WorkOrderUUID}},
			map[string]interface{}{
				"status":     params.Status,
				"updated_at": time.Now(),
			})

		if err != nil {
			svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("update cs work order status failed")
			return err
		}
		ro := model.CustomerWorkOrderOperateRecord{
			UserID:        operateUser.ID,
			WorkOrderUUID: params.WorkOrderUUID,
			Operate:       constant.CustomerWorkOrderSetStatus,
			Status:        constant.CustomerWorkOrderStatus(params.Status),
		}
		err = ro.CustomerWorkOrderOperateCreate()
		if err != nil {
			svc.log.ErrorE(err, "insert customer work order operate record failed")
			return err
		}
		return nil

	})
	if err != nil {
		svc.log.Error("update cs work ordder status and create cs work order operate failed")
		return err
	}

	if operateUser.FeishuBotUrl != "" {
		_, err = svc.SendCustomerWorkOrderMsg(operateUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
				"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
				"template_variable": map[string]interface{}{
					"machine_name":  csWorkOrder.MachineName,
					"instance_uuid": csWorkOrder.InstanceUUID,
					"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
					"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
					"operate_user":  operateUser.Username,
				},
			},
		})
		if err != nil {
			svc.log.WithFields(map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
				"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
				"template_variable": map[string]interface{}{
					"machine_name":  csWorkOrder.MachineName,
					"instance_uuid": csWorkOrder.InstanceUUID,
					"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
					"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
					"operate_user":  operateUser.Username,
				},
			}).ErrorE(err, "send feishu message about update cs work order status to operate user failed")
			return err
		}

	}

	// 创建人
	if csWorkOrder.UserId != operateUser.ID {
		createUser, err := svc.GetWorkOrderTenantUserByUserId(csWorkOrder.UserId)
		if err != nil {
			svc.log.ErrorE(err, "get work order create user failed")
			return err
		}

		if createUser.ID != operateUser.ID {
			if createUser.FeishuBotUrl != "" {
				_, err = svc.SendCustomerWorkOrderMsg(createUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
					"type": "template",
					"data": map[string]interface{}{
						"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
						"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
						"template_variable": map[string]interface{}{
							"machine_name":  csWorkOrder.MachineName,
							"instance_uuid": csWorkOrder.InstanceUUID,
							"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
							"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
							"operate_user":  operateUser.Username,
						},
					},
				})

				if err != nil {
					svc.log.WithFields(map[string]interface{}{
						"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
						"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
						"template_variable": map[string]interface{}{
							"machine_name":  csWorkOrder.MachineName,
							"instance_uuid": csWorkOrder.InstanceUUID,
							"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
							"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
							"operate_user":  operateUser.Username,
						},
					}).ErrorE(err, "send feishu message about update cs work order status to create user failed")
					return err
				}
			}
		}
	}

	// 经办人
	if csWorkOrder.AssignUserId != operateUser.ID && csWorkOrder.AssignUserId != csWorkOrder.UserId {
		assignUser, err := svc.GetWorkOrderTenantUserByUserId(csWorkOrder.AssignUserId)
		if err != nil {
			svc.log.ErrorE(err, "get work order assign user failed")
			return err
		}

		if assignUser.FeishuBotUrl != "" {
			_, err = svc.SendCustomerWorkOrderMsg(assignUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
					"template_variable": map[string]interface{}{
						"machine_name":  csWorkOrder.MachineName,
						"instance_uuid": csWorkOrder.InstanceUUID,
						"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
						"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":  operateUser.Username,
					},
				},
			})
			if err != nil {
				svc.log.WithFields(map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderStatusUpdate,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderStatusUpdate,
					"template_variable": map[string]interface{}{
						"machine_name":  csWorkOrder.MachineName,
						"instance_uuid": csWorkOrder.InstanceUUID,
						"status":        constant.CustomerWorkOrderStatusToChinese(constant.CustomerWorkOrderStatus(params.Status)),
						"old_status":    constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":  operateUser.Username,
					},
				}).ErrorE(err, "send feishu message about update cs work order status to assign user failed")
				return err
			}
		}
	}

	return nil
}

func (svc *WorkOrderService) UpdateCSWorkOrderAssign(params *model.UpdateCSWordOrderAssignReq) (err error) {
	csWorkOrder := &model.CustomerServiceWorkOrder{}
	err = csWorkOrder.CSWorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		return err
	}

	// 创建人
	createUser, err := svc.GetWorkOrderTenantUserByUserId(csWorkOrder.UserId)
	if err != nil {
		svc.log.ErrorE(err, "get work order create user failed")
		return
	}

	// 原经办人
	oldAssignUser, err := svc.GetWorkOrderTenantUserByUserId(csWorkOrder.AssignUserId)
	if err != nil {
		svc.log.ErrorE(err, "get work order assign user failed")
		return
	}

	// 操作人
	operateUser, err := svc.GetWorkOrderCreateUser(params.Uuid, params.TenantId)
	if err != nil {
		svc.log.ErrorE(err, "get work order operate user failed")
		return
	}

	// 现经办人
	newAssignUser, err := svc.GetWorkOrderTenantUserByUserId(params.AssignUserId)
	if err != nil {
		svc.log.ErrorE(err, "get work order assign user failed")
		return
	}

	err = csWorkOrder.CSWorkOrderUpdate(nil,
		&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"work_order_uuid": params.WorkOrderUUID}},
		map[string]interface{}{
			"assign_user_id": params.AssignUserId,
			"updated_at":     time.Now(),
		})

	if err != nil {
		return err
	}

	if operateUser.FeishuBotUrl != "" {
		svc.SendCustomerWorkOrderMsg(operateUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDCSWorkOrderAssignUser,
				"template_version_name": constant.FeishuTemplateVersionCSWorkOrderAssignUser,
				"template_variable": map[string]interface{}{
					"instance_uuid":   csWorkOrder.InstanceUUID,
					"machine_name":    csWorkOrder.MachineName,
					"status":          constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
					"operate_user":    operateUser.Username,
					"assign_user":     newAssignUser.Username,
					"old_assign_user": oldAssignUser.Username,
				},
			},
		})
	}

	if createUser.ID != operateUser.ID {
		if createUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(createUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderAssignUser,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderAssignUser,
					"template_variable": map[string]interface{}{
						"instance_uuid":   csWorkOrder.InstanceUUID,
						"machine_name":    csWorkOrder.MachineName,
						"status":          constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":    operateUser.Username,
						"assign_user":     newAssignUser.Username,
						"old_assign_user": oldAssignUser.Username,
					},
				},
			})
		}
	}

	if oldAssignUser.ID != operateUser.ID && oldAssignUser.ID != createUser.ID {
		if oldAssignUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(oldAssignUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderAssignUser,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderAssignUser,
					"template_variable": map[string]interface{}{
						"instance_uuid":   csWorkOrder.InstanceUUID,
						"machine_name":    csWorkOrder.MachineName,
						"status":          constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":    operateUser.Username,
						"assign_user":     newAssignUser.Username,
						"old_assign_user": oldAssignUser.Username,
					},
				},
			})
		}
	}

	if newAssignUser.ID != operateUser.ID && newAssignUser.ID != createUser.ID && newAssignUser.ID != oldAssignUser.ID {
		if newAssignUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(newAssignUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderAssignUser,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderAssignUser,
					"template_variable": map[string]interface{}{
						"instance_uuid":   csWorkOrder.InstanceUUID,
						"machine_name":    csWorkOrder.MachineName,
						"status":          constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":    operateUser.Username,
						"assign_user":     newAssignUser.Username,
						"old_assign_user": oldAssignUser.Username,
					},
				},
			})
		}
	}

	return nil
}

func (svc *WorkOrderService) CreateCSWorkOrderRecord(params *model.CreateCSWorkOrderRecordReq) (recordId int, err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	attachmentJson := datatypes.JSON{}
	if len(params.Attachment) != 0 {
		attachmentJson, err = json.MarshalIndent(params.Attachment, "", "")
		if err != nil {
			svc.log.ErrorE(err, "Error marshaling JSON:")
			return
		}
	} else {
		attachmentJson = []byte("[{}]")
	}

	var (
		createRecordUser    model.WorkOrderOperateUser
		createWorkOrderUser model.WorkOrderOperateUser
		assignUser          model.WorkOrderOperateUser
	)

	// 记录创建人, 也是本次操作人
	createRecordUser, err = svc.GetWorkOrderCreateUser(params.Uuid, params.CustomId)
	if err != nil {
		svc.log.WithField("tenant_id", params.CustomId).Error("get create user failed.")
	}
	csWorkOrder := &model.CustomerServiceWorkOrder{}
	record := &model.CustomerWorkOrderRecord{
		UserId:         createRecordUser.ID,
		WorkOrderUUID:  params.WorkOrderUUID,
		Content:        params.Content,
		AttachmentInfo: attachmentJson,
		CustomID:       params.CustomId,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			ModelDefinition: &model.CustomerWorkOrderRecord{},
			InsertPayload:   record,
		}).GetError()
		if err != nil {
			svc.log.WithError(err).WithField("record", record).Error("create customer work order record failed")
			return
		}

		err = csWorkOrder.CSWorkOrderUpdate(nil, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"work_order_uuid": params.WorkOrderUUID,
			},
		}, map[string]interface{}{})
		if err != nil {
			svc.log.WithError(err).WithField("work_order_uuid", params.WorkOrderUUID).Error("update customer work order updated_at failed")
			return err
		}
		return nil
	})

	if err != nil {
		svc.log.Error("create customer service work order record err: %v", err)
		return
	}

	err = csWorkOrder.CSWorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
		NullField: []string{"deleted_at"},
	})
	if err != nil {
		return
	}

	if createRecordUser.FeishuBotUrl != "" {
		svc.SendCustomerWorkOrderMsg(createRecordUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDCSWorkOrderRecordCreate,
				"template_version_name": constant.FeishuTemplateVersionCSWorkOrderRecordCreate,
				"template_variable": map[string]interface{}{
					"machine_name":       csWorkOrder.MachineName,
					"instance_uuid":      csWorkOrder.InstanceUUID,
					"status":             constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
					"operate_user":       createRecordUser.Username,
					"record_description": params.Content,
				},
			},
		})
	}

	// 工单创建人
	if csWorkOrder.UserId != createRecordUser.ID {
		createWorkOrderUser, err = svc.GetWorkOrderTenantUserByUserId(csWorkOrder.UserId)
		if err != nil {
			svc.log.ErrorE(err, "get work order assign user failed")
			return
		}

		if createWorkOrderUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(createWorkOrderUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderRecordCreate,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderRecordCreate,
					"template_variable": map[string]interface{}{
						"machine_name":       csWorkOrder.MachineName,
						"instance_uuid":      csWorkOrder.InstanceUUID,
						"status":             constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":       createRecordUser.Username,
						"record_description": params.Content,
					},
				},
			})
		}
	}

	// 经办人
	if csWorkOrder.AssignUserId != createRecordUser.ID && csWorkOrder.AssignUserId != csWorkOrder.UserId {
		assignUser, err = svc.GetWorkOrderTenantUserByUserId(csWorkOrder.AssignUserId)
		if err != nil {
			svc.log.ErrorE(err, "get work order assign user failed")
			return
		}

		if assignUser.FeishuBotUrl != "" {
			svc.SendCustomerWorkOrderMsg(assignUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDCSWorkOrderRecordCreate,
					"template_version_name": constant.FeishuTemplateVersionCSWorkOrderRecordCreate,
					"template_variable": map[string]interface{}{
						"machine_name":       csWorkOrder.MachineName,
						"instance_uuid":      csWorkOrder.InstanceUUID,
						"status":             constant.CustomerWorkOrderStatusToChinese(csWorkOrder.Status),
						"operate_user":       createRecordUser.Username,
						"record_description": params.Content,
					},
				},
			})
		}
	}

	return record.ID, nil
}

func (svc *WorkOrderService) GetCSWorkOrderOperateRecordList(workOrderUuid string) ([]model.CustomerWorkOrderOperateRecord, error) {
	var operateRecords []model.CustomerWorkOrderOperateRecord
	err := db_helper.GlobalDBConn().Model(&model.CustomerWorkOrderOperateRecord{}).Where("deleted_at is null").
		Where("work_order_uuid = ?", workOrderUuid).
		Find(&operateRecords).Error
	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}
	return operateRecords, nil
}

func (svc *WorkOrderService) GetRecordMapByIdList(recordIdList []int) (recordMap map[int]model.CustomerWorkOrderRecord, err error) {
	records := make([]model.CustomerWorkOrderRecord, 0)
	err = db_helper.GlobalDBConn().Table(model.TableNameCustomerWorkOrderRecord).
		Where("deleted_at is null").
		Where("id in (?)", recordIdList).
		Find(&records).Error
	if err != nil {
		svc.log.WithField("recordList", recordIdList).ErrorE(err, "Get customer work order record detail failed.")
		return nil, businesserror.ErrDatabaseError
	}

	recordMap = make(map[int]model.CustomerWorkOrderRecord)

	for _, r := range records {
		recordMap[r.ID] = r
	}

	return

}

func (svc *WorkOrderService) GetCSWorkOrderRecordCount(workOrderUuid string) (count int64, err error) {
	err = db_helper.GlobalDBConn().Table(model.TableNameCustomerWorkOrderRecord).
		Where("work_order_uuid = ?", workOrderUuid).
		Where("deleted_at is null").Count(&count).Error
	return
}

func (svc *WorkOrderService) SendCustomerWorkOrderMsg(botUrl, msgType string, msg map[string]interface{}) (rawRespBody string, err error) {
	svc.log.Info("botUrl is %s", botUrl)
	resp, err := grequests.Post(botUrl, &grequests.RequestOptions{
		JSON: map[string]interface{}{
			"msg_type": msgType,
			"card":     msg,
		},
	})

	rawRespBody = resp.String()
	svc.log.Info("send webhook resp %s", resp.String())

	if err != nil {
		svc.log.ErrorE(err, "send webhook resp failed")
		return rawRespBody, err
	}
	var result map[string]interface{}
	if err = resp.JSON(&result); err != nil {
		svc.log.ErrorE(err, "Failed to parse response JSON")
		return rawRespBody, err
	}

	code, ok := result["code"].(float64)
	if !ok {
		svc.log.ErrorE(err, "Failed to get error code from response")
		return rawRespBody, errors.New("Failed to get error code from response")
	}
	if code != 0 {
		if code == 19001 {
			return rawRespBody, businesserror.ErrWorkOrderFeishuUrlError.New().Format(botUrl)
		}
		svc.log.WithFields(map[string]interface{}{
			"resp":        resp.String(),
			"rawRespBody": rawRespBody,
		}).ErrorE(err, "send webhook resp status code not 0 failed")
		return rawRespBody, errors.New(strconv.Itoa(resp.StatusCode))
	}

	return rawRespBody, nil
}
