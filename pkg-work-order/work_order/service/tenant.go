package service

import (
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

func (svc *WorkOrderService) CreateWorkOrderTenantRelationship(params *model.CreateWorkOrderTenantRelationshipReq) (err error) {
	tenant, err := svc.GetCustomByName(params.CustomName)
	if err != nil {
		log.WithError(err).WithField("name", params.CustomName).Error("Check sign exist failed")
		return
	}

	if tenant.Id == 0 {
		err = businesserror.ErrWorkOrderCustomNotExist
		return
	}
	// 判断之前该供应商是否绑定了该客户
	tenantRelations, err := svc.GetWorkOrderTenantRelationshipByProviderId(params.ProviderID)
	for _, relation := range tenantRelations {
		if relation.CustomID == tenant.Id {
			return businesserror.ErrCustomExist
		}
	}

	tenantRelationship := &model.WorkOrderTenantRelation{
		ProviderID: params.ProviderID,
		CustomID:   tenant.Id,
		CustomName: tenant.Name,
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderTenantRelation{},
		InsertPayload:   tenantRelationship,
	}).GetError()
	if err != nil {
		log.WithError(err).Error("create work order tenant relationship failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) GetWorkOrderTenantRelationshipByProviderId(providerId int) (list []model.WorkOrderTenantRelation, err error) {
	list = make([]model.WorkOrderTenantRelation, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenantRelationship).Where("deleted_at is null").Where("provider_id = ?", providerId)

	err = db.Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("get tenant relationship by provider id failed.")
		return list, err
	}

	return list, nil
}

func (svc *WorkOrderService) GetCustomByName(name string) (tenant model.WorkOrderTenant, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderTenant{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"name": name,
				"role": constant.Custom,
			},
		},
	}, &tenant).GetError()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return model.WorkOrderTenant{}, nil
		}
		log.WithError(err).WithField("name", name).Error("get work order tenant failed")
		return
	}
	return tenant, nil
}

func (svc *WorkOrderService) GetWorkOrderTenantRelationship(params *model.GetWorkOrderTenantRelationshipReq) (paged *db_helper.PagedData, list []*model.WorkOrderTenantRelation, err error) {
	list = make([]*model.WorkOrderTenantRelation, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenantRelationship).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order tenant  relationship failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderCustomList(params *model.GetWorkOrderCustomListReq) (list []model.WorkOrderTenant, err error) {
	list = make([]model.WorkOrderTenant, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenant).Where("deleted_at is null").Where("role = ? ", constant.Custom)

	if params.Name != "" {
		db = db.Where("name = ?", params.Name)
	}

	err = db.Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order custom failed")
		return list, businesserror.ErrDatabaseError
	}
	return
}

func (svc *WorkOrderService) GetWorkOrderProviderList() (list []model.WorkOrderTenant, err error) {
	list = make([]model.WorkOrderTenant, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenant).Where("deleted_at is null").Where("role = ? ", constant.Provider)

	err = db.Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order provider failed")
		return list, businesserror.ErrDatabaseError
	}
	return
}
