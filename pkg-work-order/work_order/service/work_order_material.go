package service

import (
	"github.com/pkg/errors"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
)

func (svc *WorkOrderService) CreateWorkOrderMaterial(params *model.CreateWorkOrderMaterialReq, uuid string) (err error) {
	// 查看名称是否重复
	exist, err := svc.CheckMaterialNameExist(params.Name)
	if err != nil {
		log.WithError(err).WithField("material_name", params.Name).Error("Check material name exist failed.")
		return
	}

	if exist {
		err = businesserror.ErrWorkOrderMaterialNameExist
		return
	}

	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.ProviderID)
	if err != nil {
		log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.ProviderID).Error("get create user failed.")
		return
	}

	material := &model.WorkOrderMaterial{
		Name:         params.Name,
		Sort:         constant.WorkOrderMaterialSort(params.Sort),
		Source:       params.Source,
		Remark:       params.Remark,
		CreateUserId: createUser.ID,
		ProviderId:   params.ProviderID,
	}

	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderMaterial{},
		InsertPayload:   material,
	}).GetError()
	if err != nil {
		log.WithError(err).WithField("material_name", params.Name).Error("create work order material failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderMaterial(params *model.UpdateWorkOrderMaterialReq) (err error) {
	// 查看名称是否重复
	material, err := svc.GetWorkOrderMaterialByMaterialID(params.MaterialID)
	if err != nil {
		log.WithError(err).WithField("material_name", params.Name).Error("get material failed.")
		return err
	}
	if material.Name != params.Name {
		exist, err := svc.CheckMaterialNameExist(params.Name)
		if err != nil {
			log.WithError(err).WithField("material_name", params.Name).Error("Check material name exist failed.")
			return err
		}

		if exist {
			err = businesserror.ErrWorkOrderMaterialNameExist
			return err
		}
	}

	data := map[string]interface{}{
		"name":   params.Name,
		"sort":   params.Sort,
		"source": params.Source,
		"remark": params.Remark,
	}

	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderMaterial{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.MaterialID,
				},
			},
		}, data).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) GetWorkOrderMaterialList(params *model.GetWorkOrderMaterialListReq) (paged *db_helper.PagedData, list []*model.WorkOrderMaterial, err error) {
	list = make([]*model.WorkOrderMaterial, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderMaterial).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Name != "" {
		name := "%" + params.Name + "%"
		db = db.Where("name like ?", name)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order material failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderCreateUser(uuid string, tenantId int) (workOrderOperateUser model.WorkOrderOperateUser, err error) {
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderOperateUser{}).Where("deleted_at is null").
		Where("uuid = ?", uuid).
		Where("tenant_id = ?", tenantId).
		Find(&workOrderOperateUser).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return workOrderOperateUser, businesserror.ErrWorkOrderRecordNotFound
		}
		return workOrderOperateUser, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) CheckMaterialNameExist(name string) (exist bool, err error) {
	var count int64

	err = db_helper.GlobalDBConn().Model(&model.WorkOrderMaterial{}).
		Where("deleted_at is null").
		Where("name = ?", name).
		Count(&count).Error

	if err != nil {
		log.WithError(err).WithField("material_name", name).Error("get work order material count failed")
		return
	}

	exist = count != 0
	return
}

func (svc *WorkOrderService) GetWorkOrderMaterialInfo(params *model.GetWorkOrderMaterialInfoReq) (materials []model.MaterialInfo, err error) {
	machine, err := svc.GetWorkOrderMachineByMachineID(params.MachineId)
	if err != nil {
		log.WithError(err).WithField("machineId", params.MachineId).Error("get work order machine by machineId failed")
		return
	}

	// 获取物料组成列表
	machineTypeMaterials, err := svc.GetMachineTypeMaterialByTypeID(machine.MachineTypeID)
	if err != nil {
		log.WithError(err).WithField("machineTypeId", machine.MachineTypeID).Error("get work order machine by machineId failed")
		return
	}

	for _, material := range machineTypeMaterials {
		// 获取单个物料详细信息
		workOrderMaterial, err := svc.GetWorkOrderMaterialByMaterialID(material.MaterialID)
		if err != nil {
			log.WithError(err).WithField("machineTypeId", machine.MachineTypeID).Error("get work order machine by machineId failed")
			return nil, err
		}
		materialInfo := model.MaterialInfo{
			MaterialId:   workOrderMaterial.ID,
			MaterialSort: string(workOrderMaterial.Sort),
			MaterialName: workOrderMaterial.Name,
			Count:        int(material.MaterialCount),
		}
		materials = append(materials, materialInfo)
	}
	return
}
