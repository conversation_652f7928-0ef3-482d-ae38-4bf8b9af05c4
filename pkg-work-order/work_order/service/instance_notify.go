package service

import (
	"fmt"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	redis "server/plugin/redis_plugin"
)

func (svc *WorkOrderService) CreateInstanceNotify(params *model.CreateInstanceNotifyReq, uid int, tenantID int) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	_, err = svc.machine.GetFromRO(params.MachineId)
	if err != nil {
		svc.log.WithField("err", err).Error("get machine failed in CreateInstanceNotify")
		return
	}

	instanceNotify := &model.InstanceNotify{
		MachineId:            params.MachineId,
		UserId:               uid,
		TenantId:             tenantID,
		InstanceNotifyStatus: constant.InEffect,
		NotifyInfo:           params.NotifyInfo,
	}

	notifyList, err := instanceNotify.InstanceNotifyGetAll(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"machine_id":             params.MachineId,
			"instance_notify_status": constant.InEffect,
		},
	})

	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"machine_id":             params.MachineId,
			"instance_notify_status": constant.InEffect,
		}).Error("get instance notify failed .")
		return err
	}

	if len(notifyList) != 0 {
		return businesserror.ErrWorkOrderInstanceNotifyExistInMachine
	}

	err = instanceNotify.InstanceNotifyCreate(nil)
	if err != nil {
		svc.log.WithError(err).WithField("content", params).Error("create instance notify failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) UpdateInstanceNotify(params *model.UpdateInstanceNotifyReq) (err error) {
	_, err = svc.machine.GetFromRO(params.MachineId)
	if err != nil {
		svc.log.WithField("machine_id", params.MachineId).ErrorE(err, "get machine failed in CreateInstanceNotify")
		return
	}

	instanceNotify := &model.InstanceNotify{}

	err = instanceNotify.InstanceNotifyUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"id": params.ID,
		},
	}, map[string]interface{}{
		"machine_id":  params.MachineId,
		"notify_info": params.NotifyInfo,
	})
	if err != nil {
		svc.log.WithFields(map[string]interface{}{
			"id":          params.ID,
			"machine_id":  params.MachineId,
			"notify_info": params.NotifyInfo,
		}).ErrorE(err, "create instance notify failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) FinishInstanceNotify(params *model.FinishInstanceNotifyReq) (err error) {
	updateTemplate := map[string]interface{}{
		"instance_notify_status": constant.Finished,
	}

	instanceNotify := &model.InstanceNotify{}
	err = instanceNotify.InstanceNotifyUpdate(nil, &db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.InstanceNotifyId}}, updateTemplate)
	if err != nil {
		svc.log.WithField("id", params.InstanceNotifyId).WithError(err).Warn("update work order instance notify by id failed.")
		return
	}
	return nil
}

func (svc *WorkOrderService) GetInstanceNotifyList(params *model.WorkOrderInstanceNotifySearchReq, pageReq *db_helper.GetPagedRangeRequest, tenantID int) (paged *db_helper.PagedData, list []*model.InstanceNotify, err error) {
	list = make([]*model.InstanceNotify, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameInstanceNotify).
		Where("work_order_instance_notify.tenant_id = ?", tenantID)

	if params.MachineId != "" {
		db = db.Where("work_order_instance_notify.machine_id = ?", params.MachineId)
	}

	if params.Status != "" {
		db = db.Where("work_order_instance_notify.instance_notify_status = ?", params.Status)
	}

	if params.InstanceUuid != "" {
		db = db.Joins(fmt.Sprintf("join %s ir on work_order_instance_notify.id=ir.instance_notify_id", model.TableNameInstanceReceipt)).
			Where("ir.instance_uuid = ?", params.InstanceUuid).
			Where("ir.deleted_at is null")
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) SetLockForInstanceReceipt(instanceUuid string) (lock *redis.RedisMutex, err error) {
	lock = svc.mutex.NewRedisMutex(redis.MutexCreateWorkOrderInstanceReceipt, "instance_receipt"+instanceUuid)
	var locked bool
	locked, err = lock.Lock(constant.CreateWOInstanceReceiptTimeout)
	if err != nil || !locked {
		svc.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}
	return
}

func (svc *WorkOrderService) CreateInstanceReceipt(params *model.CreateInstanceReceiptReq) (err error) {
	// 加锁
	lock, err := svc.SetLockForInstanceReceipt(params.InstanceUUID)
	if err != nil {
		return err
	}
	defer func() {
		_ = lock.UnLock()
	}()

	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	instanceReceipt := &model.InstanceReceipt{
		InstanceUUID:     params.InstanceUUID,
		InstanceNotifyID: params.InstanceNotifyId,
		ReceiptInfo:      params.ReceiptInfo,
	}
	count, err := instanceReceipt.InstanceReceiptCount(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{
		"instance_notify_id": params.InstanceNotifyId,
		"instance_uuid":      params.InstanceUUID,
	}})

	if err != nil {
		svc.log.WithError(err).WithFields(map[string]interface{}{
			"id":            params.InstanceNotifyId,
			"instance_uuid": params.InstanceUUID,
		}).Error("count work order instance receipt failed")
		return err
	}

	instanceNotify := &model.InstanceNotify{}
	err = instanceNotify.InstanceNotifyGetFirst(&db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.InstanceNotifyId}})
	if err != nil {
		svc.log.WithError(err).WithField("id", params.InstanceNotifyId).Error("get instance notify failed")
		return err
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		if count == 0 {
			err = instanceReceipt.InstanceReceiptCreate(tx)
			if err != nil {
				svc.log.WithError(err).WithField("content", params).Error("create instance receipt failed")
				return err
			}

			err = instanceNotify.InstanceNotifyUpdate(tx, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.InstanceNotifyId,
				},
			}, map[string]interface{}{
				"receipt_count": instanceNotify.ReceiptCount + 1,
			})
			if err != nil {
				svc.log.WithError(err).Error("add instance notify receipt_count failed")
				return err
			}
		} else {
			err = instanceReceipt.InstanceReceiptUpdate(tx, &db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"instance_notify_id": params.InstanceNotifyId,
					"instance_uuid":      params.InstanceUUID,
				},
			}, map[string]interface{}{
				"receipt_info": params.ReceiptInfo,
			})

			if err != nil {
				svc.log.WithError(err).WithFields(map[string]interface{}{
					"instance_notify_id": params.InstanceNotifyId,
					"instance_uuid":      params.InstanceUUID,
				}).Error("update instance receipt receipt_info failed")
				return err
			}
		}

		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("transaction in CreateInstanceReceipt failed")
		return err
	}

	// 发送飞书消息，飞书消息发送失败不返回回执
	notifyUser, err := svc.GetWorkOrderTenantUserByUserId(instanceNotify.UserId)
	if err != nil {
		svc.log.WithField("err", err).Error("get notify user failed")
		return err
	}

	machine, err := svc.machine.Get(instanceNotify.MachineId)
	if err != nil {
		svc.log.WithField("machineID", instanceNotify.MachineId).Warn("get machine in CreateInstanceReceipt failed")
		return
	}
	if notifyUser.FeishuBotUrl != "" {
		svc.SendCustomerWorkOrderMsg(notifyUser.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
			"type": "template",
			"data": map[string]interface{}{
				"template_id":           constant.FeishuTemplateIDInstanceReceiptNotify,
				"template_version_name": constant.FeishuTemplateVersionInstanceReceiptNotify,
				"template_variable": map[string]interface{}{
					"machine_id":    instanceNotify.MachineId,
					"machine_name":  machine.MachineName,
					"instance_uuid": params.InstanceUUID,
				},
			},
		})
	}
	return nil
}

func (svc *WorkOrderService) GetInstanceReceiptList(params *model.WorkOrderInstanceReceiptSearchReq, pageReq *db_helper.GetPagedRangeRequest) (paged *db_helper.PagedData, list []*model.InstanceReceipt, err error) {
	list = make([]*model.InstanceReceipt, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameInstanceReceipt).
		Where("deleted_at is null").
		Where("instance_notify_id = ?", params.InstanceNotifyId)

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}
