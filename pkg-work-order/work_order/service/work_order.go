package service

import (
	"encoding/json"
	"fmt"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"strconv"
	"strings"
	"time"

	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (svc *WorkOrderService) FindByRecordIdMap(recordList []int) (recordMap map[int]model.WorkOrderRecord, err error) {
	records := make([]model.WorkOrderRecord, 0)
	err = db_helper.GlobalDBConn().Table(model.TableNameWorkOrderRecord).Where("deleted_at is null").Where("id in (?)", recordList).Find(&records).Error
	if err != nil {
		svc.log.WithField("err", err).With<PERSON>ield("recordList", recordList).Error("Get record detail failed.")
		return nil, businesserror.ErrDatabaseError
	}

	recordMap = make(map[int]model.WorkOrderRecord)

	for _, r := range records {
		recordMap[r.ID] = r
	}

	return

}

func (svc *WorkOrderService) CreateWorkOrder(params *model.CreateWorkOrderReq, uid int, workOrderUuid string, tenantID int) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	// 查询machine是否存在
	machine, err := svc.machine.Find(params.MachineId)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			svc.log.WithField("err", err).WithField("machine_id", params.MachineId).Error("machine not found")
			err = businesserror.ErrMachineNotFound
			return
		}
		svc.log.WithField("err", err).WithField("machine_id", params.MachineId).Error("get machine info failed")
		err = businesserror.ErrDatabaseError
		return
	}

	// 查询user是否operate user表存在
	_, err = svc.GetWorkOrderTenantUserByUserId(uid)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", workOrderUuid).Error("get work order operate user failed")
		return
	}

	faultCount, err := svc.GetMachineHistoryFaultCount(machine.MachineID, tenantID)
	if err != nil {
		svc.log.WithField("err", err).Error("get machine history fault count failed")
		return
	}

	workOrder := &model.WorkOrder{
		WorkOrderUUID:     workOrderUuid,
		MachineId:         params.MachineId,
		MachineName:       machine.MachineName,
		Online:            machine.Online,
		RegionSign:        machine.RegionSign,
		RegionName:        machine.RegionName,
		Description:       params.Description,
		Status:            constant.WaitProcess,
		FaultLevel:        constant.WorkOrderFaultLevel(params.FaultLevel),
		FaultType:         constant.WorkOrderFaultType(params.FaultType),
		UserId:            uid,
		AssignUserId:      params.AssignUserId,
		CustomID:          tenantID,
		HistoryFaultCount: faultCount + 1,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrder{},
		InsertPayload:   workOrder,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", workOrderUuid).Error("create work order failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) CreateWorkOrderTenant(params *model.CreateWorkOrderTenantReq) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	// 查看租户标识是否存在
	exist, err := svc.CheckTenantSignExist(params.TenantSign)
	if err != nil {
		svc.log.WithError(err).WithField("sign", params.TenantSign).Error("get work order tenant by sign failed")
		return
	}

	if exist {
		err = businesserror.ErrWorkOrderTenantSignExist
		return
	}

	tenant := &model.WorkOrderTenant{
		Name:       params.Name,
		TenantSign: params.TenantSign,
		Role:       constant.WorkOrderTenantRole(params.Role),
		Remark:     params.Remark,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderTenant{},
		InsertPayload:   tenant,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).WithField("tenant_name", params.Name).Error("create work order tenant failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) CheckTenantSignExist(sign string) (exist bool, err error) {
	var count int64
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderTenant{}).
		Where("deleted_at is null").
		Where("tenant_sign = ?", sign).
		Count(&count).Error
	if err != nil {
		svc.log.WithError(err).WithField("tenant_sign", sign).Error("Check sign exist failed.")
		return
	}

	exist = count != 0
	return
}

func (svc *WorkOrderService) CreateWorkOrderUser(params *model.CreateWorkOrderUserReq) (err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	// 查看autodl uuid是否存在
	autodlUser, err := svc.user.FindByUserUuid(params.UUID)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", params.UUID).Error("get autodl user by uuid failed")
		return
	}
	// 判断当前租户下是否存在该autodl User
	exist, err := svc.CheckAutodlUuidExistInTenant(params.TenantID, params.UUID)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", params.UUID).Error("Check user exist failed.")
		return
	}

	if exist {
		err = businesserror.ErrAutoUserExistInTenant
		return
	}
	// 判断租户id是否存在
	tenant, err := svc.GetWorkOrderTenantByTenantId(params.TenantID)
	if err != nil {
		svc.log.WithError(err).WithField("tenant_id", params.TenantID).Error("get tenant failed.")
		return
	}

	if tenant.Id == 0 {
		err = businesserror.ErrWorkOrderTenantNotExist
		return
	}

	user := &model.WorkOrderOperateUser{
		UID:          autodlUser.ID,
		Username:     params.Username,
		TenantID:     params.TenantID,
		UUID:         params.UUID,
		UserRole:     constant.WorkOrderUserRole(params.UserRole),
		UserStatus:   constant.Normal,
		FeishuBotUrl: params.FeishuBotUrl,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderOperateUser{},
		InsertPayload:   user,
	}).GetError()
	if err != nil {
		svc.log.WithError(err).Error("create work order user failed")
		return
	}

	if params.FeishuBotUrl != "" {
		svc.sendWorkOrderMsg(params.FeishuBotUrl, fmt.Sprintf("【AutoDL工单系统】\n%s，欢迎使用AutoDL工单系统~", params.Username))
	}

	return nil
}

func (svc *WorkOrderService) CheckAutodlUuidExistInTenant(tenantId int, autodlUuid string) (exist bool, err error) {
	var count int64
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderOperateUser{}).
		Where("tenant_id = ?", tenantId).Where("uuid = ?", autodlUuid).
		Count(&count).Error

	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", autodlUuid).WithField("tenant_id", tenantId).Error("get count failed.")
		return false, err
	}
	exist = count != 0
	return
}

func (svc *WorkOrderService) CreateWorkOrderRecord(params *model.CreateWorkOrderRecordReq, uuid string) (recordId int, err error) {
	if params == nil {
		svc.log.WithField("params", params).Error("Params is nil")
		err = businesserror.ErrInternalError
		return
	}

	attachmentJson := datatypes.JSON{}

	if len(params.Attachment) != 0 {
		// 将数组转换为json类型
		//attachment = params.Attachment
		attachmentJson, err = json.MarshalIndent(params.Attachment, "", "")
		if err != nil {
			svc.log.WithError(err).ErrorE(err, "Error marshaling JSON")
			return
		}
	} else {
		attachmentJson = []byte("[{}]")
	}

	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.CustomId)
	if err != nil {
		svc.log.WithField("autodl_uuid", uuid).WithField("tenant_id", params.CustomId).Error("get create user failed.")
	}

	record := &model.WorkOrderRecord{
		UserId:         createUser.ID,
		WorkOrderUUID:  params.WorkOrderUUID,
		Content:        params.Content,
		AttachmentInfo: attachmentJson,
		CustomID:       params.CustomId,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			DBTransactionConnection: tx,
			ModelDefinition:         &model.WorkOrderRecord{},
			InsertPayload:           record,
		}).GetError()
		if err != nil {
			svc.log.WithError(err).WithField("work_order_uuid", params.WorkOrderUUID).Error("create work order record failed")
			return err
		}
		wo := &model.WorkOrder{}
		err = wo.WorkOrderUpdate(tx, &db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"work_order_uuid": params.WorkOrderUUID,
			},
		}, map[string]interface{}{})
		if err != nil {
			svc.log.WithError(err).WithField("work_order_uuid", params.WorkOrderUUID).Error("update work order updated_at failed")
			return err
		}
		return nil
	})
	if err != nil {
		svc.log.Error("create work order record err: %v", err)
		return
	}

	return record.ID, nil
}

func (svc *WorkOrderService) GetWorkOrderDetail(workOrderUuid string) (workOrder model.WorkOrder, err error) {
	err = db_helper.GlobalDBConn().Model(&model.WorkOrder{}).Where("deleted_at is null").
		Where("work_order_uuid=?", workOrderUuid).
		Find(&workOrder).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return workOrder, businesserror.ErrWorkOrderRecordNotFound
		}
		return workOrder, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) GetWorkOrderTenantUserByUserId(userId int) (workOrderOperateUser model.WorkOrderOperateUser, err error) {
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderOperateUser{}).Where("deleted_at is null").
		Where("uuid is not null").Where("uuid != ''").
		Where("id = ?", userId).
		Find(&workOrderOperateUser).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = businesserror.ErrUserNotExist
			return
		}
		svc.log.WithError(err).WithField("userId", userId).Error("get work order tenant user failed")
		return
	}
	return workOrderOperateUser, nil
}

func (svc *WorkOrderService) GetWorkOrderTenantUserByAutodlUUID(uuid string) ([]model.WorkOrderOperateUser, error) {
	var workOrderOperateUser []model.WorkOrderOperateUser
	err := db_helper.GlobalDBConn().Model(&model.WorkOrderOperateUser{}).Where("deleted_at is null").
		Where("uuid = ?", uuid).Find(&workOrderOperateUser).Error

	if err != nil {
		svc.log.WithError(err).WithField("uuid", uuid).Error("get work order tenant user failed")
		return nil, err
	}
	return workOrderOperateUser, nil
}

func (svc *WorkOrderService) GetWorkOrderTenantByTenantId(tenantId int) (tenant model.WorkOrderTenant, err error) {
	err = db_helper.GetOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderTenant{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": tenantId,
			},
		},
	}, &tenant).GetError()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return model.WorkOrderTenant{}, nil
		}
		svc.log.WithError(err).WithField("tenantId", tenantId).Error("get tenant failed")
		return tenant, businesserror.ErrDatabaseError
	}
	return
}

func (svc *WorkOrderService) UpdateWorkOrderAssign(params *model.UpdateWordOrderAssignReq) (err error) {
	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrder{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"work_order_uuid": params.WorkOrderUUID,
				},
			},
		}, map[string]interface{}{
			"assign_user_id": params.AssignUserId,
			"updated_at":     time.Now(),
		}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrder(params model.UpdateWorkOrderReq, uuid string) (err error) {
	workOrder := make(map[string]interface{})

	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.CustomId)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.CustomId).Error("get create user failed.")
		return
	}

	work, err := svc.GetWorkOrderDetail(params.WorkOrderUUID)
	if err != nil {
		svc.log.WithError(err).WithField("work_order_uuid", params.WorkOrderUUID).Error("get work order failed")
		return
	}

	if work.MachineId != params.MachineId {
		// 对machine进行查询
		machine, err := svc.machine.Find(params.MachineId)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				svc.log.WithField("err", err).WithField("machine_id", params.MachineId).Info("machine not found")
				err = nil
				return err
			}
			svc.log.WithField("err", err).WithField("machine_id", params.MachineId).Error("get machine info failed")
			err = businesserror.ErrDatabaseError
			return err
		}

		workOrder["machine_id"] = params.MachineId
		workOrder["machine_name"] = machine.MachineName
		workOrder["region_name"] = machine.RegionName
		workOrder["region_sign"] = machine.RegionSign
	}

	if params.IsChangeAssignUser {
		workOrder["assign_user_id"] = params.AssignUserId

		// 新增一条 指派记录
		ro := model.WorkOrderOperateRecord{
			UserID:        createUser.ID,
			WorkOrderUUID: params.WorkOrderUUID,
			AssignUserId:  params.AssignUserId,
			Operate:       constant.Assign,
		}
		err = ro.WorkOrderOperateCreate()
		if err != nil {
			svc.log.WithField("err", err).Error("insert work order operate record failed")
			return err
		}
	}

	workOrder["description"] = params.Description
	workOrder["fault_level"] = params.FaultLevel
	workOrder["updated_at"] = time.Now()
	workOrder["fault_type"] = params.FaultType

	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrder{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"work_order_uuid": params.WorkOrderUUID,
				},
			},
		}, workOrder).GetError()
	if err != nil {
		return err
	}

	return nil

}

func (svc *WorkOrderService) UpdateWorkOrderMachine(params *model.UpdateWorkOrderMachineReq) (err error) {
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		machineUpdate1 := map[string]interface{}{
			"custom_bind_machine_id": params.CustomBindMachineID,
			"machine_room":           params.MachineRoom,
			"cabinet_position":       params.CabinetPosition,
			"public_ip":              params.PublicIp,
			"private_ip":             params.PrivateIp,
			"ipmi":                   params.IPMI,
			"purpose":                params.Purpose,
			"remark":                 params.Remark,
		}

		err = db_helper.UpdateOne(
			db_helper.QueryDefinition{
				ModelDefinition: &model.WorkOrderMachine{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"id": params.ID,
					},
				},
			}, machineUpdate1).GetError()
		if err != nil {
			return err
		}

		machineUpdate2 := &model.WorkOrderMachine{
			IPMIAccountPassword: params.IPMIAccountPassword,
			AccountPassword1:    params.AccountPassword1,
			AccountPassword2:    params.AccountPassword2,
		}
		err = db_helper.UpdateOne(
			db_helper.QueryDefinition{
				ModelDefinition: &model.WorkOrderMachine{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"id": params.ID,
					},
				},
			}, machineUpdate2).GetError()
		if err != nil {
			return err
		}

		return nil
	})

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderMachineByProvider(params *model.UpdateWorkOrderMachineByProviderReq) (err error) {
	machineUpdate := map[string]interface{}{
		"custom_id":       params.CustomID,
		"remark":          params.Remark,
		"machine_type_id": params.MachineTypeID,
	}
	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderMachine{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"machine_id": params.MachineID,
				},
			},
		}, machineUpdate).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) ImportWorkOrderMachine(params []model.WorkOrderMachineImportData, customId int) (unSuccessMachine []string, err error) {
	for _, data := range params {
		machine, err := svc.GetWorkOrderMachineByMachineID(data.MachineID)
		if err != nil {
			return unSuccessMachine, err
		}

		if machine.MachineID == "" || machine.CustomID != customId { // 机器不存在或者机器不在当前租户下
			unSuccessMachine = append(unSuccessMachine, data.MachineID)
			continue
		}

		machineUpdate := map[string]interface{}{
			"custom_bind_machine_id": data.CustomBindMachineID,
			"machine_room":           data.MachineRoom,
			"cabinet_position":       data.CabinetPosition,
			"public_ip":              data.PublicIp,
			"private_ip":             data.PrivateIp,
			"account_password1":      data.AccountPassword1,
			"account_password2":      data.AccountPassword2,
			"ipmi":                   data.IPMI,
			"ipmi_account_password":  data.IPMIAccountPassword,
			"purpose":                data.Purpose,
			"remark":                 data.Remark,
		}
		affectRow, errDB := db_helper.UpdateOneAffectRows(db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderMachine{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"machine_id": data.MachineID,
					"custom_id":  customId,
				},
			},
		}, machineUpdate)
		if errDB.IsNotNil() {
			svc.log.WithField("err", err).WithField("machine_id ", data.MachineID).Error("update machine failed")
			return unSuccessMachine, errDB.GetError()
		}
		if affectRow == 0 { // 说明当前数据已经更新
			continue
		}
	}
	return unSuccessMachine, nil
}

func (svc *WorkOrderService) UpdateWorkOrderStatus(params model.UpdateWorkerOrderStatusReq, createUserId int) (err error) {
	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) error {
		err = db_helper.UpdateOne(
			db_helper.QueryDefinition{
				ModelDefinition: &model.WorkOrder{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"work_order_uuid": params.WorkOrderUUID,
					},
				},
			}, map[string]interface{}{
				"status":     params.Status,
				"updated_at": time.Now(),
			}).GetError()
		if err != nil {
			svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("update work order status failed")
			return err
		}

		ro := model.WorkOrderOperateRecord{
			UserID:        createUserId,
			WorkOrderUUID: params.WorkOrderUUID,
			Operate:       constant.SetStatus,
			Status:        constant.WorkOrderStatus(params.Status),
		}
		err = ro.WorkOrderOperateCreate()
		if err != nil {
			svc.log.ErrorE(err, "insert work order operate record failed")
			return err
		}
		return nil
	})

	if err != nil {
		svc.log.Error("update wo status and create wo operate failed")
		return err
	}

	workOrder := &model.WorkOrder{}
	err = workOrder.WorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
	})
	if err != nil {
		svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("get work order by work_order_uuid failed")
		return err
	}

	machine, err := svc.machine.GetFromRONoNotFoundErr(workOrder.MachineId)
	if err != nil {
		svc.log.WithField("machine_id", workOrder.MachineId).ErrorE(err, "get machine by id failed")
		return err
	}

	if machine.ID != 0 {
		if params.Status == string(constant.Finish) || params.Status == string(constant.PressureMeasurement) {
			// 1.发送飞书给订阅工单结束事件的操作用户
			userList := strings.Split(workOrder.SubscribeFinishUser, ",")
			for _, user := range userList {
				userId, _ := strconv.Atoi(user)
				user, err := svc.GetWorkOrderTenantUserByUserId(userId)
				if err != nil {
					svc.log.ErrorE(err, "get sms assign send user failed")
					return err
				}

				if user.FeishuBotUrl != "" {
					_, err = svc.SendCustomerWorkOrderMsg(user.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
						"type": "template",
						"data": map[string]interface{}{
							"template_id":           constant.FeiShuTemplateIDWOFinishSubscribe,
							"template_version_name": constant.FeiShuTemplateVersionWOFinishSubscribe,
							"template_variable": map[string]interface{}{
								"machine_id":   workOrder.MachineId,
								"machine_name": machine.MachineName,
							},
						},
					})
					if err != nil {
						svc.log.WithFields(map[string]interface{}{
							"template_id":           constant.FeiShuTemplateIDWOFinishSubscribe,
							"template_version_name": constant.FeiShuTemplateVersionWOFinishSubscribe,
							"template_variable": map[string]interface{}{
								"machine_id":   workOrder.MachineId,
								"machine_name": machine.MachineName,
							},
						}).ErrorE(err, "send feishu message about subscribe finish failed")
						return err
					}
				}
			}

			// 2.发送飞书提醒给发送 sms指定的操作用户
			sms := &model.WorkOrderSms{}
			smsList, err := sms.WorkOrderSmsGetAll(db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"machine_id":            workOrder.MachineId,
					"is_send_feishu_notify": true,
				},
				CompareFilters: []db_helper.Compare{{Key: "created_at", Sign: db_helper.BiggerThan, CompareValue: time.Now().Add(-120 * time.Hour)}},
				NullField:      []string{"deleted_at"},
			})
			if err != nil {
				svc.log.WithError(err).Error("get work order sms by machine id failed")
				return err
			}

			if len(smsList) == 0 {
				for _, sms := range smsList {
					notifyUsers := strings.Split(sms.FeishuNotifyUser, ",")
					for _, user := range notifyUsers {
						userId, _ := strconv.Atoi(user)
						// 发送飞书消息给消息的飞书提醒人
						user, err := svc.GetWorkOrderTenantUserByUserId(userId)
						if err != nil {
							svc.log.ErrorE(err, "get sms assign send user failed")
							return err
						}
						if user.FeishuBotUrl != "" {
							_, err = svc.SendCustomerWorkOrderMsg(user.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
								"type": "template",
								"data": map[string]interface{}{
									"template_id":           constant.FeishuTemplateIDSendSms,
									"template_version_name": constant.FeishuTemplateVersionSendSms,
									"template_variable": map[string]interface{}{
										"instance_uuid": sms.InstanceUuid,
										"phone":         sms.Phone,
										"sms_content":   sms.SmsContent,
									},
								},
							})
							if err != nil {
								svc.log.WithFields(map[string]interface{}{
									"template_id":           constant.FeishuTemplateIDSendSms,
									"template_version_name": constant.FeishuTemplateVersionSendSms,
									"template_variable": map[string]interface{}{
										"instance_uuid": sms.InstanceUuid,
										"phone":         sms.Phone,
										"sms_content":   sms.SmsContent,
									},
								}).ErrorE(err, "send feishu message about sms failed")
								return err
							}
						}
					}

				}
			}

		}
	}

	return nil
}

func (svc *WorkOrderService) UpdateMachineMonitor(params model.DisposeMachineMonitorReq) (err error) {
	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.MachineMonitor{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.Id,
				},
			},
		}, map[string]interface{}{
			"status":         constant.Processed,
			"process_result": params.ProcessResult,
			"updated_at":     time.Now(),
		}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) GetWorkOrderList(params *model.WorkOrderListSearchReq, pageReq *db_helper.GetPagedRangeRequest, tenantID int) (paged *db_helper.PagedData, list []*model.WorkOrder, err error) {
	list = make([]*model.WorkOrder, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrder).
		Where("deleted_at is null").Where("custom_id = ?", tenantID)

	if params.MachineId != "" {
		db = db.Where("machine_id = ?", params.MachineId)
	}

	if len(params.MachineIdList) != 0 {
		db = db.Where("machine_id in (?)", params.MachineIdList)
	} else if len(params.MachineIdList) == 0 && params.IsSearch {
		db = db.Where("machine_id = ?", "")
	}

	if params.MachineName != "" {
		db = db.Where("machine_name = ?", params.MachineName)
	}

	if params.Online == 7 {
		db = db.Where("online = ?", 0)
	}

	if params.Online != 0 && params.Online != 7 {
		db = db.Where("online = ?", params.Online)
	}

	if params.RegionSign != "" {
		db = db.Where("region_sign = ?", params.RegionSign)
	}
	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}
	if params.FaultLevel != "" {
		db = db.Where("fault_level = ?", params.FaultLevel)
	}

	if params.FaultType != "" {
		db = db.Where("fault_type = ?", params.FaultType)
	}
	if params.AssignUserId != 0 {
		db = db.Where("assign_user_id = ?", params.AssignUserId)
	}
	if params.Description != "" {
		db = db.Where("description like ?", "%"+params.Description+"%")
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(pageReq.PageIndex, pageReq.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("id desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderOperateUserList(tenantId int) (list []*model.WorkOrderOperateUser, err error) {
	list = make([]*model.WorkOrderOperateUser, 0)

	err = db_helper.GlobalDBConn().Table(model.TableNameWorkOrderOperateUser).
		Where("deleted_at is null").Where("tenant_id = ?", tenantId).Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) GetWorkOrderOperationUserList(tenantId int) (list []*model.WorkOrderOperateUser, err error) {
	list = make([]*model.WorkOrderOperateUser, 0)

	err = db_helper.GlobalDBConn().Table(model.TableNameWorkOrderOperateUser).
		Where("user_role in (?)", []constant.WorkOrderUserRole{constant.Administrator, constant.Operation}).
		Where("deleted_at is null").Where("tenant_id = ?", tenantId).Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order failed")
		return nil, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) GetWorkOrderMachineTypeInfo(params *model.GetWorkOrderMachineTypeInfoReq) (list []*model.WorkOrderMachineType, err error) {
	list = make([]*model.WorkOrderMachineType, 0)
	err = db_helper.GlobalDBConn().Table(model.TableNameWorkOrderMachineType).
		Where("deleted_at is null").
		Where("provider_id = ?", params.ProviderID).
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find machine type failed")
		return nil, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) GetWorkOrderCustomInfo(params *model.GetWorkOrderCustomInfoReq) (list []*model.WorkOrderTenantRelation, err error) {
	list = make([]*model.WorkOrderTenantRelation, 0)
	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenantRelationship).
		Where("deleted_at is null").
		Where("provider_id = ?", params.ProviderID)

	if params.Name != "" {
		db = db.Where("custom_name = ?", params.Name)
	}

	err = db.Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find tenant relationship failed")
		return nil, businesserror.ErrDatabaseError
	}

	return
}

func (svc *WorkOrderService) GetWorkOrderTenantList(params *model.GetWorkOrderTenantListReq) (paged *db_helper.PagedData, list []*model.WorkOrderTenant, err error) {
	list = make([]*model.WorkOrderTenant, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderTenant).
		Where("deleted_at is null")

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order tenant failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderTenantUserList(params *model.GetWorkOrderTenantUserListReq) (paged *db_helper.PagedData, list []*model.WorkOrderOperateUser, err error) {
	list = make([]*model.WorkOrderOperateUser, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderOperateUser).
		Where("deleted_at is null").Where("uuid is not null").Where("uuid != ''")

	if params.CustomId != 0 {
		db = db.Where("tenant_id = ?", params.CustomId)
	}

	if params.ProviderId != 0 {
		db = db.Where("tenant_id = ?", params.ProviderId)
	}

	if params.Status != "" {
		db = db.Where("user_status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderRecordCount(workOrderUuid string) (count int64, err error) {
	err = db_helper.GlobalDBConn().Table(model.TableNameWorkOrderRecord).
		Where("work_order_uuid = ?", workOrderUuid).
		Where("deleted_at is null").Count(&count).Error
	return
}

func (svc *WorkOrderService) GetWorkOrderOperateRecordList(workOrderUuid string) ([]model.WorkOrderOperateRecord, error) {
	var operateRecords []model.WorkOrderOperateRecord
	err := db_helper.GlobalDBConn().Model(&model.WorkOrderOperateRecord{}).Where("deleted_at is null").
		Where("work_order_uuid = ?", workOrderUuid).
		Find(&operateRecords).Error
	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}
	return operateRecords, nil
}

func (svc *WorkOrderService) GetMachineHistoryFaultCount(machineId string, tenantId int) (count int64, err error) {
	err = db_helper.Count(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrder{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{"machine_id": machineId, "custom_id": tenantId},
			NullField:    []string{"deleted_at"},
		},
	}, &count).GetError()
	if err != nil {
		svc.log.WithError(err).Error("db get machine history fault count  failed")
		return count, businesserror.ErrServerBusy
	}
	return
}

func (svc *WorkOrderService) sendWorkOrderMsg(botUrl, msg string) {
	resp, err := grequests.Post(botUrl, &grequests.RequestOptions{
		JSON: map[string]interface{}{
			"msg_type": "text",
			"content": map[string]string{
				"text": msg,
			},
		},
	})
	svc.log.Info("send webhook resp %s", resp.String())
	if err != nil {
		svc.log.ErrorE(err, "send webhook resp failed")
		return
	}
	return
}

func (svc *WorkOrderService) SendFeishuRemind(req model.SendFeishuRemindReq, operateUserName string) (sendFailedUser []int, err error) {
	sendFailedUser = make([]int, 0)

	if len(req.RemindUser) == 0 {
		return sendFailedUser, businesserror.ErrEmptyBox.New().Format("提醒用户不能为空")
	}

	workOrder, err := svc.GetWorkOrderDetail(req.WorkOrderUUID)
	if err != nil {
		svc.log.WithError(err).Error("db get work order by work_order_uuid failed")
		return
	}

	for _, remindUserId := range req.RemindUser {
		user, err := svc.GetWorkOrderTenantUserByUserId(remindUserId)
		if err != nil {
			svc.log.ErrorE(err, "get feishu remind user failed")
			return sendFailedUser, err
		}
		if user.FeishuBotUrl != "" {
			_, err := svc.SendCustomerWorkOrderMsg(user.FeishuBotUrl, constant.FeishuMsgTypeInteractive, map[string]interface{}{
				"type": "template",
				"data": map[string]interface{}{
					"template_id":           constant.FeishuTemplateIDWorkOrderRemind,
					"template_version_name": constant.FeishuTemplateVersionWorkOrderRemind,
					"template_variable": map[string]interface{}{
						"machine_id":   workOrder.MachineId,
						"machine_name": workOrder.MachineName,
						"operate_user": operateUserName,
						"status":       constant.WorkOrderStatusString(workOrder.Status),
						"description":  workOrder.Description,
					},
				},
			})

			if err != nil {
				sendFailedUser = append(sendFailedUser, remindUserId)
				err = nil
				continue
			}
		}

	}
	return sendFailedUser, nil
}
func (svc *WorkOrderService) UpdateFinishSubscribeUser(params model.UpdateFinishSubscribeUserReq) (err error) {
	workOrder := &model.WorkOrder{}

	err = workOrder.WorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
	})
	if err != nil {
		svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("get work order by work order uuid failed.")
		return
	}

	var userStr string
	userList := strings.Split(workOrder.SubscribeFinishUser, ",")
	if params.IsSubscribe {
		for _, user := range userList {
			if user == strconv.Itoa(params.UserId) {
				return nil
			}
		}
		if workOrder.SubscribeFinishUser != "" {
			userStr = workOrder.SubscribeFinishUser + "," + strconv.Itoa(params.UserId)
		} else {
			userStr = strconv.Itoa(params.UserId)
		}
	} else {
		if workOrder.SubscribeFinishUser != "" {
			var newUserList []string
			for _, user := range userList {
				if user != strconv.Itoa(params.UserId) {
					newUserList = append(newUserList, user)
				}
			}
			userStr = strings.Join(newUserList, ",")
		} else {
			userStr = ""
		}
	}

	err = workOrder.WorkOrderUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
	}, map[string]interface{}{
		"subscribe_finish_user": userStr,
	})

	if err != nil {
		svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("update work order subscribe_finish_user by id failed.")
		return
	}
	return nil
}

func (svc *WorkOrderService) UpdateMachineIdleUser(params model.UpdateMachineIdleUserReq) (err error) {
	var userStrSlice []string

	for _, userId := range params.SubscribeUser {
		userStrSlice = append(userStrSlice, strconv.Itoa(userId))
	}
	workOrder := &model.WorkOrder{}

	err = workOrder.WorkOrderGet(db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
	})
	if err != nil {
		svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("get work order in UpdateMachineIdleUser failed.")
		return
	}

	if workOrder.Status.IsFinished() && params.IsSubscribe {
		return businesserror.ErrWorkOrderStatusIsFinishNotSubscribe
	}
	originSubNotifyStatus := make([]*model.SubNotify, 0)
	var newSubNotifyStatusMarshal []byte

	if workOrder.IsSendMachineIdleNotify != nil {
		err = json.Unmarshal(workOrder.IsSendMachineIdleNotify, &originSubNotifyStatus)
		if err != nil {
			svc.log.WithField("isSendMachineIdleNotify", workOrder.IsSendMachineIdleNotify).Error("unmarshal IsSendMachineIdleNotify failed.")
			return err
		}
	}

	var userStr string
	userList := strings.Split(workOrder.SubscribeMachineIdleUser, ",")
	if params.IsSubscribe {
		for _, user := range userList {
			if user == strconv.Itoa(params.UserId) {
				return nil
			}
		}
		if workOrder.SubscribeMachineIdleUser != "" {
			userStr = workOrder.SubscribeMachineIdleUser + "," + strconv.Itoa(params.UserId)
		} else {
			userStr = strconv.Itoa(params.UserId)
		}

		originSubNotifyStatus = append(originSubNotifyStatus, &model.SubNotify{
			UserId:    params.UserId,
			IsSendMsg: false,
		})

		newSubNotifyStatusMarshal, err = json.Marshal(&originSubNotifyStatus)
		if err != nil {
			svc.log.WithField("originSubNotifyStatus", originSubNotifyStatus).Error("marshal originSubNotifyStatus failed.")
			return err
		}
	} else {
		if workOrder.SubscribeMachineIdleUser != "" {

			var newUserList []string
			for _, user := range userList {
				if user != strconv.Itoa(params.UserId) {
					newUserList = append(newUserList, user)
				}
			}
			userStr = strings.Join(newUserList, ",")
		} else {
			userStr = ""
		}

		for i, subNotify := range originSubNotifyStatus {
			if subNotify.UserId == params.UserId {
				originSubNotifyStatus = append(originSubNotifyStatus[:i], originSubNotifyStatus[i+1:]...)
				break
			}
		}

		newSubNotifyStatusMarshal, err = json.Marshal(&originSubNotifyStatus)
		if err != nil {
			svc.log.WithField("originSubNotifyStatus", originSubNotifyStatus).Error("marshal originSubNotifyStatus failed.")
			return err
		}
	}

	err = workOrder.WorkOrderUpdate(nil, &db_helper.QueryFilters{
		EqualFilters: map[string]interface{}{
			"work_order_uuid": params.WorkOrderUUID,
		},
	}, map[string]interface{}{
		"subscribe_machine_idle_user": userStr,
		"is_send_machine_idle_notify": newSubNotifyStatusMarshal,
	})

	if err != nil {
		svc.log.WithField("work_order_uuid", params.WorkOrderUUID).WithError(err).Error("update work order subscribe_machine_idle_user by id failed.")
		return
	}
	return nil

}
