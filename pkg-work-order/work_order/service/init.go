package service

import (
	"server/pkg/logger"
	"server/pkg/module_definition"
	"server/pkg/notify"
	"server/plugin/redis_plugin"
)

const ModuleName = "work_order_service"

type WorkOrderService struct {
	log              *logger.Logger
	machine          module_definition.MachineInference
	user             module_definition.UserInterface
	mutex            *redis_plugin.MutexRedis
	instance         module_definition.InstanceInterface
	smsNotifyChannel notify.Channel
	gpuStock         module_definition.GpuStockInterface
	notify           module_definition.NotifyInterface
}

func NewWorkOrderServiceProvider(mutex *redis_plugin.MutexRedis,
	machine module_definition.MachineInference,
	user module_definition.UserInterface,
	instance module_definition.InstanceInterface,
	smsNotifyChannel notify.Channel,
	gpuStock module_definition.GpuStockInterface,
	notify module_definition.NotifyInterface,
) *WorkOrderService {
	return &WorkOrderService{
		log:              logger.NewLogger(ModuleName),
		machine:          machine,
		user:             user,
		mutex:            mutex,
		instance:         instance,
		smsNotifyChannel: smsNotifyChannel,
		gpuStock:         gpuStock,
		notify:           notify,
	}
}
