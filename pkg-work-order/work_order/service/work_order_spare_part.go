package service

import (
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"time"
)

func (svc *WorkOrderService) CreateWorkOrderSparePart(params *model.CreateWorkOrderSparePartReq, uuid string) (err error) {
	// 判断机器是否存在
	machine, err := svc.GetWorkOrderMachineByMachineID(params.MachineID)
	if err != nil {
		log.WithError(err).WithField("machine_id", params.MachineID).Error("get machine failed.")
		return
	}
	if machine.ID == 0 {
		err = businesserror.ErrWorkOrderMachineNotExist
		return err
	}

	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.ProviderID)
	if err != nil {
		log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.ProviderID).Error("get create user failed.")
		return
	}

	sparePart := &model.WorkOrderSparePart{
		MachineID:     params.MachineID,
		ProviderID:    params.ProviderID,
		CustomID:      params.CustomID,
		MachineRoom:   params.MachineRoom,
		Remark:        params.Remark,
		MaterialID:    params.MaterialID,
		MaterialCount: params.Count,
		CreateUserId:  createUser.ID,
		Status:        constant.WaitDeal,
	}
	err = db_helper.InsertOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderSparePart{},
		InsertPayload:   sparePart,
	}).GetError()
	if err != nil {
		log.WithError(err).Error("create work order spare part failed")
		return
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderSparePartByProvider(params *model.UpdateWorkOrderSparePartByProviderReq) (err error) {
	// 判断机器是否存在
	machine, err := svc.GetWorkOrderMachineByMachineID(params.MachineID)
	if err != nil {
		log.WithError(err).WithField("machine_id", params.MachineID).Error("get machine failed.")
		return
	}
	if machine.ID == 0 {
		err = businesserror.ErrWorkOrderMachineNotExist
		return err
	}

	data := map[string]interface{}{
		"machine_id":     params.MachineID,
		"custom_id":      params.CustomID,
		"machine_room":   params.MachineRoom,
		"remark":         params.Remark,
		"material_id":    params.MaterialID,
		"material_count": params.Count,
	}
	err = db_helper.UpdateOne(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderSparePart{},
		Filters: db_helper.QueryFilters{
			EqualFilters: map[string]interface{}{
				"id": params.ID,
			},
		},
	}, data).GetError()
	if err != nil {
		svc.log.WithField("err", err).WithField("id", params.ID).Error("update WorkOrderSparePart failed")
		return err
	}
	return nil
}

func (svc *WorkOrderService) CreateDispatchOrderSparePart(params *model.CreateDispatchOrderSparePartReq, uuid string) (err error) {
	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.ProviderID)
	if err != nil {
		log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.ProviderID).Error("get create user failed.")
		return
	}

	dispatchOrderSparePart := &model.DispatchOrderSparePart{
		CustomID:         params.CustomID,
		ProviderID:       params.ProviderID,
		MachineRoom:      params.MachineRoom,
		RecipientName:    params.RecipientName,
		RecipientPhone:   params.RecipientPhone,
		RecipientAddress: params.RecipientAddress,
		ExpressNumber:    params.ExpressNumber,
		CreateUserId:     createUser.ID,
		Status:           constant.DispatchOrderSparePartStocking,
		DispatchTime:     nil,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		err = tx.Model(&model.DispatchOrderSparePart{}).Create(&dispatchOrderSparePart).Error
		if err != nil {
			return
		}

		// 添加备件工单-物料记录
		for _, material := range params.MaterialList {
			machineTypeMaterial := model.DispatchOrderSparePartMaterial{
				MaterialID:               material.MaterialID,
				MaterialCount:            int64(material.Count),
				DispatchOrderSparePartID: dispatchOrderSparePart.ID,
			}
			err = tx.Model(&model.DispatchOrderSparePartMaterial{}).Create(&machineTypeMaterial).Error
			if err != nil {
				return
			}
		}

		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("db failed")
		return businesserror.ErrServerBusy
	}

	return nil
}

func (svc *WorkOrderService) UpdateDispatchOrderSparePartUpdateByProvider(params *model.UpdateDispatchOrderUpdateByProviderReq) (err error) {
	dispatchOrderSparePartUpdateData := map[string]interface{}{
		"custom_id":         params.CustomID,
		"machine_room":      params.MachineRoom,
		"recipient_name":    params.RecipientName,
		"recipient_phone":   params.RecipientPhone,
		"recipient_address": params.RecipientAddress,
		"express_number":    params.ExpressNumber,
	}
	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.DispatchOrderSparePart{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.ID,
				},
			},
		}, dispatchOrderSparePartUpdateData).GetError()
	if err != nil {
		return err
	}

	for _, material := range params.MaterialUpdateList {
		materialUpdate := map[string]interface{}{
			"material_count": material.Count,
		}
		if material.Operate == "update" {
			err = db_helper.UpdateOne(
				db_helper.QueryDefinition{
					ModelDefinition: &model.DispatchOrderSparePartMaterial{},
					Filters: db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"dispatch_order_spare_part_id": params.ID,
							"material_id":                  material.MaterialID,
						},
					},
				}, materialUpdate).GetError()
			if err != nil {
				svc.log.WithField("material_count", material.Count).Error("update workOrder machineType material failed")
				return err
			}
		} else if material.Operate == "add" {
			materials, err := svc.GetDispatchOrderSparePartMaterial(params.ID)
			if err != nil {
				svc.log.WithField("id", params.ID).Error("get dispatch order spare part failed")
				return err
			}
			for _, originMaterial := range materials {
				if originMaterial.MaterialID == material.MaterialID {
					return businesserror.ErrDispatchOrderSparePartExistInDispatchOrder
				}
			}

			dispatchOrderSparePartAdd := model.DispatchOrderSparePartMaterial{
				MaterialID:               material.MaterialID,
				MaterialCount:            int64(material.Count),
				DispatchOrderSparePartID: params.ID,
			}
			err = db_helper.GlobalDBConn().Model(&model.DispatchOrderSparePartMaterial{}).Create(&dispatchOrderSparePartAdd).Error
			if err != nil {
				svc.log.WithField("dispatchOrderSparePartMaterialID", dispatchOrderSparePartAdd.ID).Error("add dispatch order spare part material failed")
				return err
			}
		} else if material.Operate == "delete" {
			err = db_helper.Delete(db_helper.QueryDefinition{
				ModelDefinition: &model.DispatchOrderSparePartMaterial{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"dispatch_order_spare_part_id": params.ID,
						"material_id":                  material.MaterialID,
					},
				},
			}, &model.WorkOrderMachineTypeMaterial{}).GetError()
			if err != nil {
				svc.log.WithField("dispatch_order_spare_part_id", params.ID).Error("delete dispatch order spare part material failed")
				return err
			}
		}
	}

	return nil
}

func (svc *WorkOrderService) GetWorkOrderSparePartList(params *model.GetWorkOrderSparePartProviderListReq) (paged *db_helper.PagedData, list []*model.WorkOrderSparePart, err error) {
	list = make([]*model.WorkOrderSparePart, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderSparePartTable).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetDispatchOrderSparePartList(params *model.GetDispatchOrderSparePartCustomListReq) (paged *db_helper.PagedData, list []*model.DispatchOrderSparePart, err error) {
	list = make([]*model.DispatchOrderSparePart, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameDispatchOrderSparePartTable).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("get dispatch order spare part failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetWorkOrderSparePartCustomList(params *model.GetWorkOrderSparePartCustomListReq) (paged *db_helper.PagedData, list []*model.WorkOrderSparePart, err error) {
	list = make([]*model.WorkOrderSparePart, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderSparePartTable).
		Where("deleted_at is null").Where("custom_id = ?", params.CustomID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order user failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) BindDispatchOrderID(params *model.BindDispatchOrderIDReq) (err error) {
	dispatchOrderSparePart, err := svc.GetDispatchOrderSparePartByID(params.DispatchOrderID)
	if err != nil {
		log.WithError(err).WithField("id", params.DispatchOrderID).Error("get dispatch order spare part  by id failed")
		return err
	}

	if dispatchOrderSparePart.ID == 0 {
		err = businesserror.ErrDispatchOrderSparePartNotExist
		return err
	}

	if dispatchOrderSparePart.ProviderID != params.ProviderID {
		err = businesserror.ErrDispatchOrderNotExistInTenant
		return err
	}

	sparePart := map[string]interface{}{
		"status":           model.ChangeTransition(dispatchOrderSparePart.Status),
		"dispatch_bill_id": params.DispatchOrderID,
		"updated_at":       time.Now(),
	}

	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderSparePart{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: params.WorkOrderSparePartIDs}}},
		UpdatePayload:   sparePart,
	}).GetError()
	if err != nil {
		svc.log.WithField("workOrderSparePartList", params.WorkOrderSparePartIDs).WithField("dispatch_bill_id", params.DispatchOrderID).
			WithError(err).Error("update work order spare part dispatch_bill_id failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

func (svc *WorkOrderService) BindExpressNumber(params *model.BindExpressNumberReq) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.DispatchOrderSparePart{},
		Filters:         db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": params.DispatchOrderSparePartID}},
		UpdatePayload: map[string]interface{}{
			"express_number": params.ExpressNumber,
			"updated_at":     time.Now()},
	}).GetError()
	if err != nil {
		svc.log.WithField("DispatchOrderSparePartID", params.DispatchOrderSparePartID).WithField("express_number", params.ExpressNumber).
			WithError(err).Error("update dispatch order spare part express_number failed")
		err = businesserror.ErrDatabaseError
	}
	return
}

func (svc *WorkOrderService) UpdateDispatchOrderSparePartStatus(params *model.UpdateDispatchOrderSparePartStatusReq) (err error) {
	data := map[string]interface{}{
		"status":     params.Status,
		"updated_at": time.Now(),
	}
	if params.Status == string(constant.DispatchOrderSparePartShipped) {
		data["dispatch_time"] = time.Now()
	}
	for _, dispatchOrderSparePartID := range params.DispatchOrderSparePartIDs {
		err = db_helper.UpdateOne(db_helper.QueryDefinition{ModelDefinition: &model.DispatchOrderSparePart{},
			Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{"id": dispatchOrderSparePartID}}},
			data).GetError()
		if err != nil {
			svc.log.Error("update dispatch order spare part status failed")
			err = businesserror.ErrDatabaseError
			return err
		}

		workOrderSpareParts, err := svc.GetWorkOrderSparePartByDispatchOrderId(dispatchOrderSparePartID)
		if err != nil {
			log.WithError(err).WithField("dispatchOrderSparePartId", dispatchOrderSparePartID).Error("get workOrderSpareParts failed")
			return err
		}

		if len(workOrderSpareParts) != 0 {
			status := model.ChangeTransition(constant.DispatchOrderSparePartStatus(params.Status))
			err = svc.UpdateWorkOrderSparePartStatusByDispatchBillId(dispatchOrderSparePartID, string(status))
			if err != nil {
				log.WithError(err).WithField("dispatchOrderSparePartId", dispatchOrderSparePartID).Error("update workOrderSpareParts status failed")
				return err
			}
		}
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderSparePartStatusByDispatchBillId(id int, status string) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderSparePart{},
		Filters: db_helper.QueryFilters{EqualFilters: map[string]interface{}{
			"dispatch_bill_id": id,
		}},
		UpdatePayload: map[string]interface{}{
			"status":     status,
			"updated_at": time.Now()},
	}).GetError()
	if err != nil {
		svc.log.WithField("dispatch_bill_id", id).
			WithError(err).Error("update work order spare part status failed")
		err = businesserror.ErrDatabaseError
		return err
	}
	return
}

func (svc *WorkOrderService) GetWorkOrderSparePartByID(workOrderSparePartID int) (model.WorkOrderSparePart, error) {
	var workOrderSparePart model.WorkOrderSparePart

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderSparePart{}).Where("deleted_at is null").
		Where("id = ?", workOrderSparePartID).
		Find(&workOrderSparePart).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.WorkOrderSparePart{}, businesserror.ErrServerBusy
	}
	return workOrderSparePart, nil
}

func (svc *WorkOrderService) UpdateWorkOrderSparePartStatus(params *model.UpdateWorkOrderSparePartStatusReq) (err error) {
	workOrderSparePart, err := svc.GetWorkOrderSparePartByID(params.WorkOrderSparePartID)
	if err != nil {
		svc.log.Error("get work order spare part by id failed")
		return err
	}

	if workOrderSparePart.ID == 0 {
		err = businesserror.ErrWorkOrderSparePartNotExist
		return err
	}

	if workOrderSparePart.DispatchBillID != 0 {
		err = businesserror.ErrWorkOrderSparePartHasBoundDispatchOrder
		return err
	}

	data := map[string]interface{}{
		"status":     params.Status,
		"updated_at": time.Now(),
	}

	if params.Status == string(constant.WorkOrderSparePartShipped) {
		data["dispatch_time"] = time.Now()
	}

	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderSparePart{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.WorkOrderSparePartID,
				},
			},
		}, data).GetError()
	if err != nil {
		svc.log.Error("update work order spare part status failed")
		return err
	}
	return nil
}

func (svc *WorkOrderService) GetWorkOrderSparePartByDispatchOrderId(dispatchOrderId int) ([]model.WorkOrderSparePart, error) {
	var workOrderSpareParts []model.WorkOrderSparePart

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderSparePart{}).Where("deleted_at is null").
		Where("dispatch_bill_id = ?", dispatchOrderId).Find(&workOrderSpareParts).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}
	return workOrderSpareParts, nil
}
