package service

import (
	"fmt"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	redis "server/plugin/redis_plugin"
	"strconv"
	"strings"
	"time"
)

func (svc *WorkOrderService) CreateWorkOrderMachineType(params *model.CreateWorkOrderMachineTypeReq, uuid string) (err error) {
	// 查看名称是否重复
	exist, err := svc.CheckMachineTypeNameExist(params.Name)
	if err != nil {
		svc.log.WithError(err).WithField("material_name", params.Name).Error("Check material name exist failed.")
		return
	}

	if exist {
		err = businesserror.ErrWorkOrderMachineTypeNameExist
		return
	}

	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.ProviderID)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.ProviderID).Error("get create user failed.")
		return
	}

	machineType := &model.WorkOrderMachineType{
		Name:         params.Name,
		CreateUserId: createUser.ID,
		ProviderId:   params.ProviderID,
		Remark:       params.Remark,
	}

	err = db_helper.NewDBConn().TransactionNormal(func(tx *gorm.DB) (err error) {
		// 创建机型
		err = tx.Model(&model.WorkOrderMachineType{}).Create(&machineType).Error
		if err != nil {
			return err
		}

		// 添加机型-物料记录
		materialIDMap := make(map[int]bool)
		for _, material := range params.MaterialList {
			// 判断params.MaterialList中的material.MaterialID有无重复
			if materialIDMap[material.MaterialID] {
				return businesserror.ErrWorkOrderMaterialExistInMachineType
			}
			materialIDMap[material.MaterialID] = true
			machineTypeMaterial := model.WorkOrderMachineTypeMaterial{
				MaterialID:    material.MaterialID,
				MaterialCount: int64(material.Count),
				MachineTypeId: machineType.ID,
			}
			err = tx.Model(&model.WorkOrderMachineTypeMaterial{}).Create(&machineTypeMaterial).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		svc.log.WithError(err).Error("db failed")
		return businesserror.ErrServerBusy
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderMachineType(params *model.UpdateWorkOrderMachineTypeReq) (err error) {
	// 查看名称是否重复
	machineType, err := svc.GetWorkOrderMachineTypeByTypeID(params.MachineTypeID)
	if err != nil {
		svc.log.WithError(err).WithField("machine_type_id", params.MachineTypeID).Error("get machineType failed.")
		return err
	}

	if machineType.Name != params.Name {
		exist, err := svc.CheckMachineTypeNameExist(params.Name)
		if err != nil {
			svc.log.WithError(err).WithField("material_name", params.Name).Error("Check material name exist failed.")
			return err
		}

		if exist {
			err = businesserror.ErrWorkOrderMachineTypeNameExist
			return err
		}
	}

	machineTypeData := map[string]interface{}{
		"name":   params.Name,
		"remark": params.Remark,
	}
	err = db_helper.UpdateOne(
		db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderMachineType{},
			Filters: db_helper.QueryFilters{
				EqualFilters: map[string]interface{}{
					"id": params.MachineTypeID,
				},
			},
		}, machineTypeData).GetError()
	if err != nil {
		return err
	}

	for _, material := range params.MaterialList {
		machineTypeMaterialUpdate := map[string]interface{}{
			"material_count": material.Count,
		}
		if material.Operate == "update" {
			err = db_helper.UpdateOne(
				db_helper.QueryDefinition{
					ModelDefinition: &model.WorkOrderMachineTypeMaterial{},
					Filters: db_helper.QueryFilters{
						EqualFilters: map[string]interface{}{
							"machine_type_id": params.MachineTypeID,
							"material_id":     material.MaterialID,
						},
					},
				}, machineTypeMaterialUpdate).GetError()
			if err != nil {
				svc.log.WithField("material_count", material.Count).Error("update workOrder machineType material failed")
				return err
			}
		} else if material.Operate == "add" {
			materialsByTypeID, err := svc.GetMachineTypeMaterialByTypeID(params.MachineTypeID)
			if err != nil {
				svc.log.WithError(err).WithField("machine_type_id", params.MachineTypeID).Error("get material by machine type id failed.")
				return err
			}
			for _, originalMaterial := range materialsByTypeID {
				if originalMaterial.MaterialID == material.MaterialID {
					return businesserror.ErrWorkOrderMaterialExistInMachineType
				}
			}
			machineTypeMaterialAdd := model.WorkOrderMachineTypeMaterial{
				MaterialID:    material.MaterialID,
				MaterialCount: int64(material.Count),
				MachineTypeId: params.MachineTypeID,
			}
			err = db_helper.GlobalDBConn().Model(&model.WorkOrderMachineTypeMaterial{}).Create(&machineTypeMaterialAdd).Error
			if err != nil {
				svc.log.WithField("machine_type_id", params.MachineTypeID).Error("add workOrder machineType material failed")
				return err
			}
		} else if material.Operate == "delete" {
			err = db_helper.Delete(db_helper.QueryDefinition{
				ModelDefinition: &model.WorkOrderMachineTypeMaterial{},
				Filters: db_helper.QueryFilters{
					EqualFilters: map[string]interface{}{
						"machine_type_id": params.MachineTypeID,
						"material_id":     material.MaterialID,
					},
				},
			}, &model.WorkOrderMachineTypeMaterial{}).GetError()
			if err != nil {
				svc.log.WithField("machine_type_id", params.MachineTypeID).Error("delete workOrder machineType material failed")
				return err
			}
		}
	}
	return nil

}

func (svc *WorkOrderService) SetLockForCreateWorkOrderMachine(providerId int) (lock *redis.RedisMutex, err error) {
	lock = svc.mutex.NewRedisMutex(redis.MutexCreateWorkOrderMachine, "create_machine"+strconv.Itoa(providerId))
	var locked bool
	locked, err = lock.Lock(constant.CreateMachineTimeout)
	if err != nil || !locked {
		svc.log.WithError(err).WithField("is locked", locked).Warn("get redis lock failed.")
		err = businesserror.ErrServerBusy
		return
	}
	return
}

func (svc *WorkOrderService) CreateWorkOrderMachine(params *model.CreateWorkOrderMachineReq, uuid string) (err error) {
	// 加锁
	lock, err := svc.SetLockForCreateWorkOrderMachine(params.ProviderID)
	if err != nil {
		return
	}
	defer func() {
		_ = lock.UnLock()
	}()
	// 获取创建人
	createUser, err := svc.GetWorkOrderCreateUser(uuid, params.ProviderID)
	if err != nil {
		svc.log.WithError(err).WithField("autodl_uuid", uuid).WithField("tenant_id", params.ProviderID).Error("get create user failed.")
		return
	}

	provider, err := svc.GetWorkOrderTenantByTenantId(params.ProviderID)
	if err != nil {
		svc.log.WithError(err).WithField("tenant_id", params.ProviderID).Error("get provider failed.")
		return
	}

	if params.StartMachineId != "" { // KY-000043
		// 判断起始值~起始值+机器数量区间是否有重复
		parts := strings.Split(params.StartMachineId, "-")
		if len(parts) != 2 {
			return businesserror.ErrInvalidRequestParams // 解析错误
		}
		startId, err := strconv.Atoi(parts[1]) // 43
		if err != nil {
			svc.log.WithError(err).Error("get the second half of the machineId failed.")
			return err
		}
		endID := startId + params.Number - 1

		var count int64
		err = db_helper.GlobalDBConn().Model(&model.WorkOrderMachine{}).
			Where("machine_id BETWEEN ? AND ?", params.StartMachineId, fmt.Sprintf("%s-%06d", provider.TenantSign, endID)).
			Count(&count).Error

		if count > 0 { // 该区间已存在机器
			return businesserror.ErrRangeWorkOrderMachineExist
		}

	} else {
		// 找到最大的machineId
		prefix := fmt.Sprintf("%s-", provider.TenantSign)

		params.StartMachineId, err = dbFindMaxMachineID(prefix) // KY-000043
		if err != nil {
			svc.log.WithError(err).Error("get max machineId failed.")
			return
		}
	}

	for i := 0; i < params.Number; i++ {
		number, err := extractNumber(params.StartMachineId)
		if err != nil {
			svc.log.WithError(err).Error("extract the number in the machineId failed.")
			return err
		}
		machineID := fmt.Sprintf("%s-%06d", provider.TenantSign, number+i)

		machine := &model.WorkOrderMachine{
			MachineID:     machineID,
			MachineTypeID: params.MachineTypeID,
			CustomID:      params.CustomID,
			ProviderID:    params.ProviderID,
			Remark:        params.Remark,
			Status:        constant.WaitProduction,
			CreateUserId:  createUser.ID,
			DeliverTime:   nil,
		}
		err = db_helper.InsertOne(db_helper.QueryDefinition{
			ModelDefinition: &model.WorkOrderMachine{},
			InsertPayload:   machine,
		}).GetError()
		if err != nil {
			log.WithError(err).Error("create work order machine failed")
			return err
		}
	}
	return nil

}

// 提取字符串中的数字部分
func extractNumber(machineID string) (int, error) {
	parts := strings.Split(machineID, "-")
	if len(parts) != 2 {
		return 0, businesserror.ErrInvalidRequestParams
	}
	startId, err := strconv.Atoi(parts[1]) // 43
	if err != nil {
		log.WithError(err).Error("get the second half of the machineId failed.")
		return 0, businesserror.ErrInvalidRequestParams
	}
	return startId, nil
}

func dbFindMaxMachineID(prefix string) (machineId string, err error) {
	maxID := 0
	// 检查是否存在匹配的记录
	var count int64
	err = db_helper.GlobalDBConn().Model(&model.WorkOrderMachine{}).
		Where("machine_id LIKE ?", prefix+"%").
		Count(&count).Error
	if err != nil {
		log.WithError(err).Error("get count of matching records failed")
		return "", err
	}
	if count > 0 {
		err = db_helper.GlobalDBConn().Model(&model.WorkOrderMachine{}).
			Select("MAX(CAST(RIGHT(machine_id, 6) AS UNSIGNED))").
			Where("machine_id like ?", prefix+"%").
			Scan(&maxID).Error
		if err != nil {
			log.WithError(err).Error("get max machineID failed")
			return
		}
	}

	machineId = fmt.Sprintf("%s%06d", prefix, maxID+1)
	return
}

func (svc *WorkOrderService) CheckMachineTypeNameExist(name string) (exist bool, err error) {
	var count int64

	err = db_helper.GlobalDBConn().Model(&model.WorkOrderMachineType{}).
		Where("deleted_at is null").
		Where("name = ?", name).
		Count(&count).Error
	if err != nil {
		log.WithError(err).WithField("material_name", name).Error("get work order machine type count failed")
		return
	}

	exist = count != 0
	return
}

func (svc *WorkOrderService) GetWorkOrderMachineTypeList(params *model.GetWorkOrderMachineTypeListReq) (paged *db_helper.PagedData, list []*model.GetWorkOrderMachineTypeListData, err error) {
	list = make([]*model.GetWorkOrderMachineTypeListData, 0)
	machineTypes := make([]*model.WorkOrderMachineType, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderMachineType).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Name != "" {
		name := "%" + params.Name + "%"
		db = db.Where("name like ?", name)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&machineTypes).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order material failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	for _, machineType := range machineTypes {
		// 获取该机型下的物料
		machineTypeMaterials, err := svc.GetMachineTypeMaterialByTypeID(machineType.ID)
		if err != nil {
			svc.log.WithError(err).Error("find work order materials failed")
			return nil, nil, businesserror.ErrDatabaseError
		}

		materialInfo := make([]model.MaterialInfo, 0)
		for _, machineTypeMaterial := range machineTypeMaterials {
			workOrderMaterial, err := svc.GetWorkOrderMaterialByMaterialID(machineTypeMaterial.MaterialID)
			if err != nil {
				svc.log.WithError(err).Error("get work order material by materialID failed")
				return nil, nil, err
			}
			if workOrderMaterial.ID == 0 {
				continue
			}

			material := model.MaterialInfo{
				MaterialId:   machineTypeMaterial.MaterialID,
				MaterialSort: string(workOrderMaterial.Sort),
				MaterialName: workOrderMaterial.Name,
				Count:        int(machineTypeMaterial.MaterialCount),
			}

			materialInfo = append(materialInfo, material)
		}
		if len(materialInfo) == 0 {
			continue
		}

		// 获取创建人
		tenantUser, err := svc.GetWorkOrderTenantUserByUserId(machineType.CreateUserId)
		if err != nil {
			svc.log.WithError(err).Error("get tenant user failed")
			return nil, nil, businesserror.ErrDatabaseError
		}

		// 获取使用该机型的机器数量
		machineNumber, err := svc.GetWorkOrderMachineCountByMachineTypeID(machineType.ID)
		if err != nil {
			svc.log.WithError(err).Error("get machineNumber failed")
			return nil, nil, businesserror.ErrDatabaseError
		}
		list = append(list, &model.GetWorkOrderMachineTypeListData{
			ID:             machineType.ID,
			Name:           machineType.Name,
			MaterialInfo:   materialInfo,
			Remark:         machineType.Remark,
			CreateUserName: tenantUser.Username,
			CreatedAt:      machineType.CreatedAt,
			MachineNumber:  machineNumber,
		})
	}

	paged.List = &list
	return
}

func (svc *WorkOrderService) GetWorkOrderMachineCountByMachineTypeID(machineTypeID int) (int64, error) {
	var count int64
	err := db_helper.GlobalDBConn().Model(&model.WorkOrderMachine{}).
		Where("deleted_at is null").
		Where("machine_type_id = ?", machineTypeID).
		Count(&count).Error
	if err != nil {
		log.WithError(err).WithField("machine_type_id", machineTypeID).Error("get work order machine count by machineTypeID.")
		return 0, err
	}
	return count, nil
}

func (svc *WorkOrderService) GetWorkOrderMachineList(params *model.GetWorkOrderMachineListReq) (paged *db_helper.PagedData, list []*model.WorkOrderMachine, err error) {
	list = make([]*model.WorkOrderMachine, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderMachine).
		Where("deleted_at is null").Where("provider_id = ?", params.ProviderID)

	if params.Status != "" {
		db = db.Where("status = ?", params.Status)
	}

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order machine failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderCustomMachineList(params *model.GetWorkOrderCustomMachineListReq) (paged *db_helper.PagedData, list []*model.WorkOrderMachine, err error) {
	list = make([]*model.WorkOrderMachine, 0)

	db := db_helper.GlobalDBConn().Table(model.TableNameWorkOrderMachine).
		Where("deleted_at is null").Where("custom_id = ?", params.CustomID).Where("status = ?", constant.Delivered)

	var count int64
	err = db.Count(&count).Error
	if err != nil {
		svc.log.WithError(err).Error("Count failed.")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged = db_helper.BuildPagedDataUtil(params.PageIndex, params.PageSize, int(count), 10)
	err = db.Offset(paged.Offset).
		Limit(paged.PageSize).
		Order("created_at desc").
		Find(&list).Error
	if err != nil {
		svc.log.WithError(err).Error("find work order machine failed")
		return nil, nil, businesserror.ErrDatabaseError
	}

	paged.List = &list

	return
}

func (svc *WorkOrderService) GetWorkOrderMachineTypeByTypeID(typeID int) (model.WorkOrderMachineType, error) {
	var machineType model.WorkOrderMachineType

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderMachineType{}).Where("deleted_at is null").
		Where("id = ?", typeID).
		Find(&machineType).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.WorkOrderMachineType{}, businesserror.ErrServerBusy
	}
	return machineType, nil
}

func (svc *WorkOrderService) GetMachineTypeMaterialByTypeID(typeID int) ([]model.WorkOrderMachineTypeMaterial, error) {
	var machineTypeMaterials []model.WorkOrderMachineTypeMaterial

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderMachineTypeMaterial{}).Where("deleted_at is null").
		Where("machine_type_id = ?", typeID).
		Find(&machineTypeMaterials).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}

	return machineTypeMaterials, nil
}

func (svc *WorkOrderService) GetDispatchOrderSparePartMaterial(sparePartID int) ([]model.DispatchOrderSparePartMaterial, error) {
	var dispatchOrderSparePartMaterials []model.DispatchOrderSparePartMaterial

	err := db_helper.GlobalDBConn().Model(&model.DispatchOrderSparePartMaterial{}).Where("deleted_at is null").
		Where("dispatch_order_spare_part_id = ?", sparePartID).
		Find(&dispatchOrderSparePartMaterials).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return nil, businesserror.ErrServerBusy
	}

	return dispatchOrderSparePartMaterials, nil
}

func (svc *WorkOrderService) GetWorkOrderMaterialByMaterialID(materialID int) (model.WorkOrderMaterial, error) {
	var material model.WorkOrderMaterial

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderMaterial{}).Where("deleted_at is null").
		Where("id = ?", materialID).
		Find(&material).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.WorkOrderMaterial{}, businesserror.ErrServerBusy
	}

	return material, nil
}

func (svc *WorkOrderService) GetDispatchOrderSparePartByID(id int) (model.DispatchOrderSparePart, error) {
	var dispatchOrderSparePart model.DispatchOrderSparePart

	err := db_helper.GlobalDBConn().Model(&model.DispatchOrderSparePart{}).Where("deleted_at is null").
		Where("id = ?", id).
		Find(&dispatchOrderSparePart).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.DispatchOrderSparePart{}, businesserror.ErrServerBusy
	}

	return dispatchOrderSparePart, nil
}

func (svc *WorkOrderService) GetWorkOrderMachineByMachineID(machineID string) (model.WorkOrderMachine, error) {
	var machine model.WorkOrderMachine

	err := db_helper.GlobalDBConn().Model(&model.WorkOrderMachine{}).Where("deleted_at is null").
		Where("machine_id = ?", machineID).
		Find(&machine).Error

	if err != nil {
		svc.log.WithError(err).Error("db find failed")
		return model.WorkOrderMachine{}, businesserror.ErrServerBusy
	}
	return machine, nil
}

func (svc *WorkOrderService) UpdateWorkOrderMachineStatus(params *model.UpdateWorkOrderMachineStatusReq) (err error) {
	data := map[string]interface{}{
		"status":     params.Status,
		"updated_at": time.Now(),
	}

	if params.Status == string(constant.Delivered) {
		data["deliver_time"] = time.Now()
	}

	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderMachine{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: params.IDs}}},
		UpdatePayload:   data,
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}

func (svc *WorkOrderService) UpdateWorkOrderMachineCustom(params *model.UpdateWorkOrderMachineCustomReq) (err error) {
	err = db_helper.UpdateAll(db_helper.QueryDefinition{
		ModelDefinition: &model.WorkOrderMachine{},
		Filters:         db_helper.QueryFilters{InFilters: []db_helper.In{{Key: "id", InSet: params.IDs}}},
		UpdatePayload: map[string]interface{}{
			"custom_id":  params.CustomID,
			"updated_at": time.Now(),
		},
	}).GetError()
	if err != nil {
		return err
	}

	return nil
}
