package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/http"
	instanceModel "server/pkg/instance/model"
	"server/pkg/libs"
	"strconv"
	"strings"
)

func (ctrl *WorkOrderController) CreateSmsTemplate(c *gin.Context) {
	var req model.CreateSmsTemplateReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		http.SendError(c, err)
		return
	}

	err = ctrl.WorkOrderSvc.CreateWorkOrderSmsTemplate(&req, createUser.ID, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) DeleteSmsTemplate(c *gin.Context) {
	var req model.DeleteSmsTemplateReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.DeleteWorkOrderSmsTemplate(req.SmsTemplateId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}

func (ctrl *WorkOrderController) GetSmsTemplateList(c *gin.Context) {
	var req model.GetSmsTemplateListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	paged, smsTemplates, err := ctrl.WorkOrderSvc.GetSmsTemplateList(&req.GetPagedRangeRequest, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}
	if len(smsTemplates) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		http.SendOK(c, paged)
		return
	}

	var list []model.GetSmsTemplateListData
	for _, smsTemplate := range smsTemplates {
		// 获取userName
		operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(smsTemplate.UserId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get create user failed")
			http.SendError(c, err)
			return
		}

		info := model.GetSmsTemplateListData{
			SmsTemplateId:    smsTemplate.ID,
			Name:             smsTemplate.Name,
			AliyunTemplateId: smsTemplate.AliyunTemplateId,
			TemplateContent:  smsTemplate.TemplateContent,
			CreatedAt:        smsTemplate.CreatedAt,
			UserName:         operateUser.Username,
		}
		list = append(list, info)
	}
	paged.List = list
	http.SendOK(c, paged)
	return
}

func (ctrl *WorkOrderController) GetSmsTemplateDetail(c *gin.Context) {
	var req model.GetSmsTemplateDetailReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	template, err := ctrl.WorkOrderSvc.GetSmsTemplateDetail(req.SmsTemplateId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, template)
	return
}

func (ctrl *WorkOrderController) GetSmsTemplateInfo(c *gin.Context) {
	var resp model.GetSmsTemplateInfoResp
	u := http.GetTenantUserInfo(c)

	smsTemplates, err := ctrl.WorkOrderSvc.GetSmsTemplateInfo(u.TenantId)
	if err != nil {
		ctrl.log.WarnE(err, "get work order user list failed.")
		http.SendError(c, err)
		return
	}

	resp.List = make([]model.GetSmsTemplateData, 0, len(smsTemplates))

	if len(smsTemplates) == 0 {
		http.SendOK(c, resp)
		return
	}

	for _, template := range smsTemplates {
		resp.List = append(resp.List, model.GetSmsTemplateData{
			SmsTemplateId:      template.ID,
			Name:               template.Name,
			TemplateContent:    template.TemplateContent,
			TemplateParamsInfo: template.TemplateParamsInfo,
			AliyunTemplateId:   template.AliyunTemplateId,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) SendSms(c *gin.Context) {
	var req model.SendSmsReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	err := ctrl.WorkOrderSvc.SendSms(&req, u.UUID, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
	return
}

func (ctrl *WorkOrderController) GetSendSmsList(c *gin.Context) {
	var req model.GetSendSmsListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	u := http.GetTenantUserInfo(c)
	paged, smsList, err := ctrl.WorkOrderSvc.GetSendSmsList(&req.WorkOrderSendSmsSearchReq, &req.GetPagedRangeRequest, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}
	if len(smsList) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		http.SendOK(c, paged)
		return
	}

	var list []model.GetWorkOrderSmsListData
	for _, sms := range smsList {
		template, err := ctrl.WorkOrderSvc.GetSmsTemplateDetail(sms.TemplateId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get template by template id failed")
			http.SendError(c, err)
			return
		}
		// 获取userName
		sendUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(sms.SendUserId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get create user failed")
			http.SendError(c, err)
			return
		}

		var uid []int
		if sms.IsSendFeishuNotify {
			usersStr := strings.Split(sms.FeishuNotifyUser, ",")
			for _, userIdStr := range usersStr {
				uId, _ := strconv.Atoi(userIdStr)
				uid = append(uid, uId)
			}
		}

		info := model.GetWorkOrderSmsListData{
			SmsID:              sms.ID,
			SmsTemplateId:      sms.TemplateId,
			TemplateName:       template.Name,
			Phone:              sms.Phone,
			PhoneArea:          sms.PhoneArea,
			SmsParamsInfo:      sms.SmsParamsInfo,
			SendChannel:        sms.SendChannel,
			SmsContent:         sms.SmsContent,
			SmsStatus:          sms.SmsStatus,
			MachineId:          sms.MachineId,
			InstanceUuid:       sms.InstanceUuid,
			SendUserName:       sendUser.Username,
			CreatedAt:          sms.CreatedAt,
			IsSendFeishuNotify: sms.IsSendFeishuNotify,
			SendBasis:          sms.SendBasis,
			FeishuNotifyUser:   uid,
		}
		list = append(list, info)
	}
	paged.List = list
	http.SendOK(c, paged)
	return
}

func (ctrl *WorkOrderController) GetSmsInstanceIDInfo(c *gin.Context) {
	var req model.GetSmsInstanceIDInfoReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	instances, err := ctrl.instance.GetSmsInstanceIDInfo(&instanceModel.GetSmsInstanceIDParams{
		MachineId:               req.MachineId,
		MachineName:             req.MachineName,
		IsRunningInstance:       req.IsRunningInstance,
		IsNoPaygInstance:        req.IsNoPaygInstance,
		IsShutDownInThreeHours:  req.IsShutDownInThreeHours,
		IsShutDownInTwelveHours: req.IsShutDownInTwelveHours,
	})
	if err != nil {
		http.SendError(c, err)
		return
	}

	if len(instances) == 0 {
		http.SendOK(c, nil)
		return
	}

	var instanceInfo []model.GetSmsInstanceIDInfoResp
	for _, instance := range instances {
		cleanedEmoji := instance.Name
		if instance.Name != "" {
			cleanedEmoji = libs.CheckForEmojis(instance.Name)
			if strings.TrimSpace(cleanedEmoji) == "" {
				cleanedEmoji = ""
			}
		}
		info := model.GetSmsInstanceIDInfoResp{
			HaveGpuResources: instance.HaveGpuResources,
			InstanceUUID:     instance.InstanceUUID,
			Status:           instance.Status,
			ChargeType:       instance.ChargeType,
			StoppedAt:        instance.StoppedAt,
			ExpiredAt:        instance.ExpiredAt,
			InstanceName:     instance.Name,
			CleanedEmojiName: cleanedEmoji,
		}
		instanceInfo = append(instanceInfo, info)
	}

	http.SendOK(c, instanceInfo)
	return
}

func (ctrl *WorkOrderController) GetInstanceInfo(c *gin.Context) {
	var req model.GetInstanceInfoReq

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	instance, err := ctrl.instance.GetInstanceNoEqualRemoving(constant.InstanceUUIDType(req.InstanceUuid))
	if err != nil {
		http.SendError(c, err)
		return
	}

	user, err := ctrl.user.FindUserById(instance.UID)
	if err != nil {
		http.SendError(c, err)
		return
	}
	var instanceInfo []model.InstanceInfo
	instanceInfo = append(instanceInfo, model.InstanceInfo{
		Phone:     user.Phone,
		PhoneArea: user.PhoneArea,
	})

	if instance.Creator != "" {
		subUser, err := ctrl.user.SubUserGet(instance.Creator)
		if err != nil {
			http.SendError(c, err)
			return
		}
		if subUser.Phone != "" {
			instanceInfo = append(instanceInfo, model.InstanceInfo{
				Phone:     subUser.Phone,
				PhoneArea: subUser.PhoneArea,
			})
		}

	}

	http.SendOK(c, instanceInfo)
	return
}

func (ctrl *WorkOrderController) GetInstanceName(c *gin.Context) {
	var req model.GetInstanceInfoReq

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	instance, err := ctrl.instance.GetInstanceNotExistNoError(constant.InstanceUUIDType(req.InstanceUuid))
	if err != nil {
		http.SendError(c, err)
		return
	}

	cleanedEmoji := instance.Name
	if instance.Name != "" {
		cleanedEmoji = libs.CheckForEmojis(instance.Name)
		if strings.TrimSpace(cleanedEmoji) == "" {
			cleanedEmoji = ""
		}
	}

	info := model.InstanceNameInfo{
		Name:             instance.Name,
		CleanedEmojiName: cleanedEmoji,
	}

	http.SendOK(c, info)
	return
}

func (ctrl *WorkOrderController) GetUserPhoneDetail(c *gin.Context) {
	var req model.GetUserPhoneDetailReq

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user, err := ctrl.user.FindByUserPhoneFromRO(req.Phone)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, user.PhoneArea)
	return
}
