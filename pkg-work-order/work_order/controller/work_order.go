package controller

import (
	"encoding/json"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/http"
	"server/pkg/libs"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func (ctrl *WorkOrderController) CreateWorkOrder(c *gin.Context) {
	var req model.CreateWorkOrderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	workOrderUUID := libs.GenRandomStrByUUID(10)

	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	err = ctrl.WorkOrderSvc.CreateWorkOrder(&req, createUser.ID, workOrderUUID, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)

	ro := model.WorkOrderOperateRecord{
		UserID:        createUser.ID,
		WorkOrderUUID: workOrderUUID,
		AssignUserId:  req.AssignUserId,
		Operate:       constant.Assign,
		Status:        constant.WaitProcess,
	}
	err = ro.WorkOrderOperateCreate()
	if err != nil {
		ctrl.log.WarnE(err, "insert work order operate record failed")
	}
}

func (ctrl *WorkOrderController) GetWorkOrderList(c *gin.Context) {
	var (
		req  model.GetWorkOrderListReq
		resp model.GetWorkOrderListResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	if req.GpuTypeId != 0 {
		machineIdList, err := ctrl.MachineSvc.GetMachineIdListByGpuTypeID(req.GpuTypeId)
		if err != nil {
			ctrl.log.WithError(err).Error("get machine id list failed")
			http.SendError(c, err)
			return
		}
		req.MachineIdList = machineIdList
		req.IsSearch = true
	}

	paged, workOrders, err := ctrl.WorkOrderSvc.GetWorkOrderList(&req.WorkOrderListSearchReq, &req.GetPagedRangeRequest, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderListData, 0, len(workOrders)),
	}

	userMap := make(map[int]string)
	for _, workOrder := range workOrders {
		// 获取机器信息
		machine, err := ctrl.MachineSvc.GetFromRONoNotFoundErr(workOrder.MachineId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get machine failed")
			http.SendError(c, err)
			return
		}

		machineName := "未查询到主机"
		if machine.MachineID != "" {
			machineName = workOrder.MachineName
		}

		// 获取记录数
		count, err := ctrl.WorkOrderSvc.GetWorkOrderRecordCount(workOrder.WorkOrderUUID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get work order record count failed")
			http.SendError(c, err)
			return
		}

		if _, ok := userMap[workOrder.UserId]; !ok {
			operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrder.UserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}
			userMap[workOrder.UserId] = operateUser.Username
		}

		// 获取assignUserName
		if _, ok := userMap[workOrder.AssignUserId]; !ok {
			assignUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrder.AssignUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get assign user failed")
				http.SendError(c, err)
				return
			}
			userMap[workOrder.AssignUserId] = assignUser.Username
		}

		gpuName := ""
		if machine.GPUTypeID != 0 {
			gpuType, err := ctrl.gpuType.GetGpu(machine.GPUTypeID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get gpuType failed")
				http.SendError(c, err)
				return
			}
			gpuName = gpuType.GpuName
		}

		subscribeFinishUser := make([]int, 0)
		if workOrder.SubscribeFinishUser != "" {
			finishUserList := strings.Split(workOrder.SubscribeFinishUser, ",")
			for _, user := range finishUserList {
				userId, _ := strconv.Atoi(user)
				subscribeFinishUser = append(subscribeFinishUser, userId)
			}
		}

		subscribeMachineIdleUser := make([]int, 0)
		if workOrder.SubscribeMachineIdleUser != "" {
			machineIdleUserList := strings.Split(workOrder.SubscribeMachineIdleUser, ",")
			for _, user := range machineIdleUserList {
				userId, _ := strconv.Atoi(user)
				subscribeMachineIdleUser = append(subscribeMachineIdleUser, userId)
			}
		}

		resp.List = append(resp.List, model.GetWorkOrderListData{
			ID:                       workOrder.ID,
			MachineId:                workOrder.MachineId,
			MachineName:              machineName,
			MachineStatus:            int(workOrder.Online),
			GpuType:                  gpuName,
			MachineGpuNumber:         machine.GpuNumber,
			MachineGpuUsed:           machine.GpuUsed,
			MachineGpuOnly10:         machine.GpuUsedOnly10,
			MachineAlias:             machine.MachineAlias,
			RegionSign:               string(workOrder.RegionSign),
			RegionName:               workOrder.RegionName,
			WorkOrderStatus:          string(workOrder.Status),
			FaultLevel:               string(workOrder.FaultLevel),
			FaultType:                string(workOrder.FaultType),
			HistoryFaultCount:        workOrder.HistoryFaultCount,
			Description:              workOrder.Description,
			RecordCount:              count,
			UserId:                   workOrder.UserId,
			UserName:                 userMap[workOrder.UserId],
			AssignUserId:             workOrder.AssignUserId,
			AssignUserName:           userMap[workOrder.AssignUserId],
			OnLineBackup:             int(machine.OnlineBackup),
			WorkOrderUUID:            workOrder.WorkOrderUUID,
			CreatedAt:                workOrder.CreatedAt,
			UpdatedAt:                workOrder.UpdatedAt,
			SubscribeFinishUser:      subscribeFinishUser,
			SubscribeMachineIdleUser: subscribeMachineIdleUser,
		})
	}

	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderDetail(c *gin.Context) {
	var (
		req  model.GetWorkOrderDetailReq
		resp model.GetWorkOrderDetailResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	workOrder, err := ctrl.WorkOrderSvc.GetWorkOrderDetail(req.WorkOrderUuid)
	if err != nil {
		http.SendError(c, err)
		return
	}

	// 获取处理过程
	operateRecordList, err := ctrl.WorkOrderSvc.GetWorkOrderOperateRecordList(req.WorkOrderUuid)

	resp = model.GetWorkOrderDetailResp{
		WorkOrderUUID:   workOrder.WorkOrderUUID,
		MachineId:       workOrder.MachineId,
		Description:     workOrder.Description,
		DealProcessList: make([]model.GetWorkOrderDealProcessData, 0, len(operateRecordList)),
	}

	if len(operateRecordList) == 0 {
		http.SendOK(c, resp)
		return
	}

	recordIds := make([]int, 0, len(operateRecordList))

	for _, record := range operateRecordList {
		if record.RecordId != 0 {
			recordIds = append(recordIds, record.RecordId)
		}

	}
	recordMap, err := ctrl.WorkOrderSvc.FindByRecordIdMap(recordIds)

	for _, operateRecord := range operateRecordList {
		// 获取userName
		operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(operateRecord.UserID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get work order operate user failed")
			http.SendError(c, err)
			return
		}

		assignUserName := ""
		if operateRecord.Operate == constant.Assign {
			operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(operateRecord.AssignUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get work order operate user failed")
				http.SendError(c, err)
				return
			}
			assignUserName = operateUser.Username
		}

		var attachments []model.FileData
		if operateRecord.Operate == constant.AddRecord {
			// json 类型转换成切片
			if len(recordMap) == 0 {
				log.WithError(err).Error("The record for which the operation is add record does not exist")
				continue
			}

			err := json.Unmarshal(recordMap[operateRecord.RecordId].AttachmentInfo, &attachments)
			if err != nil {
				log.WithError(err).Error("unmarshal json failed")
				return
			}
		}

		resp.DealProcessList = append(resp.DealProcessList, model.GetWorkOrderDealProcessData{
			ID:             operateRecord.ID,
			CreatedAt:      operateRecord.CreatedAt,
			UserID:         operateRecord.UserID,
			UserName:       operateUser.Username,
			Operate:        string(operateRecord.Operate),
			AssignUserId:   operateRecord.AssignUserId,
			AssignUserName: assignUserName,
			RecordId:       operateRecord.RecordId,
			Record:         recordMap[operateRecord.RecordId].Content,
			Attachment:     attachments,
			Status:         string(operateRecord.Status),
		})
	}

	http.SendOK(c, resp)
	return

}

func (ctrl *WorkOrderController) UpdateWorkOrder(c *gin.Context) {
	var req model.UpdateWorkOrderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	req.CustomId = u.TenantId
	err := ctrl.WorkOrderSvc.UpdateWorkOrder(req, u.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderStatus(c *gin.Context) {
	var req model.UpdateWorkerOrderStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	err = ctrl.WorkOrderSvc.UpdateWorkOrderStatus(req, createUser.ID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}

func (ctrl *WorkOrderController) UpdateWorkOrderAssign(c *gin.Context) {
	var req model.UpdateWordOrderAssignReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	err := ctrl.WorkOrderSvc.UpdateWorkOrderAssign(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)

	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	ro := model.WorkOrderOperateRecord{
		UserID:        createUser.ID,
		Operate:       constant.Assign,
		WorkOrderUUID: req.WorkOrderUUID,
		AssignUserId:  req.AssignUserId,
	}
	err = ro.WorkOrderOperateCreate()
	if err != nil {
		ctrl.log.WarnE(err, "insert work order operate record failed")
	}
}

func (ctrl *WorkOrderController) CreateWorkOrderRecord(c *gin.Context) {
	var req model.CreateWorkOrderRecordReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	req.CustomId = u.TenantId
	recordId, err := ctrl.WorkOrderSvc.CreateWorkOrderRecord(&req, u.UUID)

	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	ro := model.WorkOrderOperateRecord{
		UserID:        createUser.ID,
		Operate:       constant.AddRecord,
		WorkOrderUUID: req.WorkOrderUUID,
		RecordId:      recordId,
	}
	err = ro.WorkOrderOperateCreate()
	if err != nil {
		ctrl.log.WarnE(err, "insert work order operate record failed")
	}
}

func (ctrl *WorkOrderController) GetWorkOrderUserList(c *gin.Context) {
	var resp model.GetWorkOrderUserListResp
	u := http.GetTenantUserInfo(c)

	userList, err := ctrl.WorkOrderSvc.GetWorkOrderOperateUserList(u.TenantId)
	if err != nil {
		ctrl.log.WarnE(err, "get work order user list failed.")
		http.SendError(c, err)
		return
	}

	resp.List = make([]model.GetWorkOrderUserListData, 0, len(userList))

	if len(userList) == 0 {
		http.SendOK(c, resp)
		return
	}

	for _, user := range userList {
		resp.List = append(resp.List, model.GetWorkOrderUserListData{
			ID:       user.ID,
			Uid:      user.UID,
			UserName: user.Username,
		})
	}

	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderOperationUserList(c *gin.Context) {
	var resp model.GetWorkOrderUserListResp

	u := http.GetTenantUserInfo(c)

	userList, err := ctrl.WorkOrderSvc.GetWorkOrderOperationUserList(u.TenantId)
	if err != nil {
		ctrl.log.WarnE(err, "get work order user list failed.")
		http.SendError(c, err)
		return
	}

	resp.List = make([]model.GetWorkOrderUserListData, 0, len(userList))

	if len(userList) == 0 {
		http.SendOK(c, resp)
		return
	}

	for _, user := range userList {
		resp.List = append(resp.List, model.GetWorkOrderUserListData{
			ID:       user.ID,
			Uid:      user.UID,
			UserName: user.Username,
		})
	}

	http.SendOK(c, resp)
	return
}
func (ctrl *WorkOrderController) UpdateFinishSubscribeUser(c *gin.Context) {
	var req model.UpdateFinishSubscribeUserReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	req.UserId = createUser.ID

	err = ctrl.WorkOrderSvc.UpdateFinishSubscribeUser(req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateMachineIdleSubscribeUser(c *gin.Context) {
	var req model.UpdateMachineIdleUserReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	req.UserId = createUser.ID

	err = ctrl.WorkOrderSvc.UpdateMachineIdleUser(req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) SendFeishuRemind(c *gin.Context) {
	var req model.SendFeishuRemindReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	// 获取操作人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		http.SendError(c, err)
		return
	}

	remindFailedUser, err := ctrl.WorkOrderSvc.SendFeishuRemind(req, createUser.Username)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, remindFailedUser)

}
