package controller

import (
	"encoding/json"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/constant"
	"server/pkg/db_helper"
	"server/pkg/http"
	"server/pkg/libs"
	"strings"

	"github.com/gin-gonic/gin"
)

func (ctrl *WorkOrderController) CreateCSWorkOrder(c *gin.Context) {
	var req model.CreateCustomerWorkOrderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	workOrderUUID := libs.GenRandomStrByUUID(10)

	// 获取创建人
	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithError(err).With<PERSON>ield("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).Error("get create user failed.")
		return
	}

	err = ctrl.WorkOrderSvc.CreateCSWorkOrder(&req, createUser.ID, workOrderUUID, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) GetCSWorkOrderList(c *gin.Context) {
	var req model.GetCustomerWorkOrderListReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)

	if req.GpuTypeId != 0 {
		machineIdList, err := ctrl.MachineSvc.GetMachineIdListByGpuTypeID(req.GpuTypeId)
		if err != nil {
			ctrl.log.WithError(err).Error("get machine id list failed")
			http.SendError(c, err)
			return
		}
		req.MachineIdList = machineIdList
		req.IsSearch = true
	}

	paged, workOrders, err := ctrl.WorkOrderSvc.GetCSWorkOrderList(&req.CustomerWorkOrderSearchReq, &req.GetPagedRangeRequest, u.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}
	if len(workOrders) == 0 {
		paged = &db_helper.PagedData{List: []int{}}
		http.SendOK(c, paged)
		return
	}

	var list []model.GetCSWorkOrderListData
	for _, workOrder := range workOrders {
		// 获取机器信息
		machine, err := ctrl.MachineSvc.GetFromRONoNotFoundErr(workOrder.MachineId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get machine failed")
			http.SendError(c, err)
			return
		}
		machineName := "未查询到主机"
		if machine.MachineID != "" {
			machineName = workOrder.MachineName
		}

		// 获取记录数
		count, err := ctrl.WorkOrderSvc.GetCSWorkOrderRecordCount(workOrder.WorkOrderUUID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get work order record count failed")
			http.SendError(c, err)
			return
		}

		// 获取userName
		operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrder.UserId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get create user failed")
			http.SendError(c, err)
			return
		}

		// 获取assignUserName
		assignUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrder.AssignUserId)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get assign user failed")
			http.SendError(c, err)
			return
		}
		gpuName := ""
		if machine.GPUTypeID != 0 {
			gpuType, err := ctrl.gpuType.GetGpu(machine.GPUTypeID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get gpuType failed")
				http.SendError(c, err)
				return
			}
			gpuName = gpuType.GpuName
		}

		info := model.GetCSWorkOrderListData{
			ID:               workOrder.ID,
			MachineId:        workOrder.MachineId,
			InstanceUUID:     string(workOrder.InstanceUUID),
			MachineName:      machineName,
			MachineStatus:    int(workOrder.Online),
			GpuType:          gpuName,
			MachineGpuNumber: machine.GpuNumber,
			MachineGpuUsed:   machine.GpuUsed,
			MachineGpuOnly10: machine.GpuUsedOnly10,
			MachineAlias:     machine.MachineAlias,
			RegionSign:       string(workOrder.RegionSign),
			RegionName:       workOrder.RegionName,
			WorkOrderStatus:  string(workOrder.Status),
			Description:      workOrder.Description,
			AttachmentEntity: workOrder.AttachmentEntity,
			UserId:           workOrder.UserId,
			UserName:         operateUser.Username,
			AssignUserId:     workOrder.AssignUserId,
			Tag:              workOrder.Tag,
			AssignUserName:   assignUser.Username,
			OnLineBackup:     int(machine.OnlineBackup),
			WorkOrderUUID:    workOrder.WorkOrderUUID,
			CreatedAt:        workOrder.CreatedAt,
			UpdatedAt:        workOrder.UpdatedAt,
			RecordCount:      count,
		}

		list = append(list, info)
	}
	paged.List = list
	http.SendOK(c, paged)
	return
}

func (ctrl *WorkOrderController) GetCSWorkOrderDetail(c *gin.Context) {
	var (
		req  model.GetCSWorkOrderDetailReq
		resp model.GetCSWorkOrderDetailResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	csWorkOrder, err := ctrl.WorkOrderSvc.GetCSWorkOrderDetail(req.WorkOrderUuid)
	if err != nil {
		http.SendError(c, err)
		return
	}

	// 获取处理过程
	operateRecordList, err := ctrl.WorkOrderSvc.GetCSWorkOrderOperateRecordList(req.WorkOrderUuid)
	if err != nil {
		ctrl.log.WithField("word_order_uuid", req.WorkOrderUuid).Info("get operate record list failed")
		http.SendError(c, err)
		return
	}

	resp = model.GetCSWorkOrderDetailResp{
		WorkOrderUUID:   csWorkOrder.WorkOrderUUID,
		MachineId:       csWorkOrder.MachineId,
		InstanceUUID:    csWorkOrder.InstanceUUID,
		Description:     csWorkOrder.Description,
		Attachment:      csWorkOrder.AttachmentEntity.Attachment,
		DealProcessList: make([]model.GetCSWorkOrderDealProcessData, 0, len(operateRecordList)),
		Tag:             csWorkOrder.Tag,
	}

	if len(operateRecordList) == 0 {
		http.SendOK(c, resp)
		return
	}

	recordIds := make([]int, 0, len(operateRecordList))

	for _, record := range operateRecordList {
		if record.RecordId != 0 {
			recordIds = append(recordIds, record.RecordId)
		}
	}
	recordMap, err := ctrl.WorkOrderSvc.GetRecordMapByIdList(recordIds)
	if err != nil {
		ctrl.log.WithField("recordIds", recordIds).Info("get record map failed")
		http.SendError(c, err)
		return
	}

	for _, operateRecord := range operateRecordList {
		// 获取userName
		operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(operateRecord.UserID)
		if err != nil {
			ctrl.log.WithField("err", err).Error("get work order operate user failed")
			http.SendError(c, err)
			return
		}

		assignUserName := ""
		if operateRecord.Operate == constant.CustomerWorkOrderAssign {
			operateUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(operateRecord.AssignUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get work order operate user failed")
				http.SendError(c, err)
				return
			}
			assignUserName = operateUser.Username
		}

		var attachments []model.FileData
		if operateRecord.Operate == constant.CustomerWorkOrderAddRecord {
			// json 类型转换成切片
			if len(recordMap) == 0 {
				ctrl.log.WithError(err).Error("The record for which the operation is add record does not exist")
				continue
			}

			err := json.Unmarshal(recordMap[operateRecord.RecordId].AttachmentInfo, &attachments)
			if err != nil {
				ctrl.log.WithError(err).Error("unmarshal json failed")
				return
			}
		}

		resp.DealProcessList = append(resp.DealProcessList, model.GetCSWorkOrderDealProcessData{
			ID:             operateRecord.ID,
			CreatedAt:      operateRecord.CreatedAt,
			UserID:         operateRecord.UserID,
			UserName:       operateUser.Username,
			Operate:        string(operateRecord.Operate),
			AssignUserId:   operateRecord.AssignUserId,
			AssignUserName: assignUserName,
			RecordId:       operateRecord.RecordId,
			Record:         recordMap[operateRecord.RecordId].Content,
			Attachment:     attachments,
			Status:         string(operateRecord.Status),
		})
	}

	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) UpdateCSWorkOrder(c *gin.Context) {
	var req model.UpdateCSWorkOrderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	err := ctrl.WorkOrderSvc.UpdateCSWorkOrder(&req, u.UID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

}

func (ctrl *WorkOrderController) UpdateCSWorkOrderStatus(c *gin.Context) {
	var req model.UpdateCSWorkerOrderStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	req.Uuid = u.UUID
	req.TenantId = u.TenantId
	err := ctrl.WorkOrderSvc.UpdateCSWorkOrderStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateCSWorkOrderAssign(c *gin.Context) {
	var req model.UpdateCSWordOrderAssignReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	req.Uuid = u.UUID
	req.TenantId = u.TenantId
	err := ctrl.WorkOrderSvc.UpdateCSWorkOrderAssign(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, nil)

	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).ErrorE(err, "")
		return
	}
	ro := model.CustomerWorkOrderOperateRecord{
		UserID:        createUser.ID,
		Operate:       constant.CustomerWorkOrderAssign,
		WorkOrderUUID: req.WorkOrderUUID,
		AssignUserId:  req.AssignUserId,
	}
	err = ro.CustomerWorkOrderOperateCreate()
	if err != nil {
		ctrl.log.WithField("customer_work_order_operate_record", ro).WarnE(err, "insert customer work order operate record failed")
	}
}

func (ctrl *WorkOrderController) CreateCSWorkOrderRecord(c *gin.Context) {
	var req model.CreateCSWorkOrderRecordReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	u := http.GetTenantUserInfo(c)
	req.CustomId = u.TenantId
	req.Uuid = u.UUID
	recordId, err := ctrl.WorkOrderSvc.CreateCSWorkOrderRecord(&req)

	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)

	createUser, err := ctrl.WorkOrderSvc.GetWorkOrderCreateUser(u.UUID, u.TenantId)
	if err != nil {
		ctrl.log.WithField("autodl_uuid", u.UUID).WithField("custom_id", u.TenantId).ErrorE(err, "get create user failed.")
		return
	}
	ro := model.CustomerWorkOrderOperateRecord{
		UserID:        createUser.ID,
		Operate:       constant.CustomerWorkOrderAddRecord,
		WorkOrderUUID: req.WorkOrderUUID,
		RecordId:      recordId,
	}
	err = ro.CustomerWorkOrderOperateCreate()
	if err != nil {
		ctrl.log.WarnE(err, "insert customer work order operate record failed")
	}

}

func (ctrl *WorkOrderController) GetMachineRegionInfo(c *gin.Context) {
	var req model.GetMachineRegionInfoReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	instanceUuidSplit := strings.Split(req.InstanceUuid, "-")
	if len(instanceUuidSplit) != 2 {
		http.SendError(c, businesserror.ErrWorkOrderInstanceFormatError)
		return
	}
	machine, err := ctrl.MachineSvc.Get(instanceUuidSplit[0])
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, &model.GetMachineRegionInfoResp{
		RegionSign:   machine.RegionSign,
		MachineAlias: machine.MachineAlias,
	})
}
