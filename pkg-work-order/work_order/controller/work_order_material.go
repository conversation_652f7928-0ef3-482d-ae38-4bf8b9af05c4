package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/http"
)

func (ctrl *WorkOrderController) CreateWorkOrderMaterial(c *gin.Context) {
	var req model.CreateWorkOrderMaterialReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateWorkOrderMaterial(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) GetWorkOrderMaterialList(c *gin.Context) {
	var (
		req  model.GetWorkOrderMaterialListReq
		resp model.GetWorkOrderMaterialListResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, workOrderMaterials, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderMaterialListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderMaterialListData, 0, len(workOrderMaterials)),
	}

	tenantMap := make(map[int]model.WorkOrderOperateUser)

	for _, workOrderMaterial := range workOrderMaterials {
		if _, ok := tenantMap[workOrderMaterial.CreateUserId]; !ok {
			tenantUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(workOrderMaterial.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get tenant user failed")
				http.SendError(c, err)
				return
			}
			tenantMap[tenantUser.ID] = tenantUser
		}

		resp.List = append(resp.List, model.GetWorkOrderMaterialListData{
			ID:             workOrderMaterial.ID,
			Name:           workOrderMaterial.Name,
			Sort:           string(workOrderMaterial.Sort),
			Source:         workOrderMaterial.Source,
			Remark:         workOrderMaterial.Remark,
			CreateUserName: tenantMap[workOrderMaterial.CreateUserId].Username,
			CreatedAt:      workOrderMaterial.CreatedAt,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) UpdateWorkOrderMaterial(c *gin.Context) {
	var req model.UpdateWorkOrderMaterialReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMaterial(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) GetWorkOrderMaterialInfo(c *gin.Context) {
	var (
		req  model.GetWorkOrderMaterialInfoReq
		resp model.GetWorkOrderMaterialInfoResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
	}

	materialInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialInfo(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp.MaterialInfo = materialInfo

	http.SendOK(c, resp)

}
