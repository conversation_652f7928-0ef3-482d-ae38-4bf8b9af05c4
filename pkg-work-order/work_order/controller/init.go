package controller

import (
	"server/pkg-work-order/module_definition"
	"server/pkg/logger"
	pkg "server/pkg/module_definition"
	"server/plugin/redis_plugin"
)

const ModuleName = "work_order_controller"

type WorkOrderController struct {
	log          *logger.Logger
	WorkOrderSvc module_definition.WorkOrderInterface
	MachineSvc   pkg.MachineInference
	plugin       *redis_plugin.UserPlugin
	user         pkg.UserInterface
	gpuType      pkg.GpuTypeInference
	instance     pkg.InstanceInterface
	container    pkg.ContainerRuntimeInterface
}

func NewWorkOrderControllerProvider(
	WorkOrderSvc module_definition.WorkOrderInterface,
	MachineSvc pkg.MachineInference,
	plugin *redis_plugin.UserPlugin,
	user pkg.UserInterface,
	gpuType pkg.GpuTypeInference,
	instance pkg.InstanceInterface,
	container pkg.ContainerRuntimeInterface,
) *WorkOrderController {
	return &WorkOrderController{
		log:          logger.NewLogger(ModuleName),
		WorkOrderSvc: WorkOrderSvc,
		MachineSvc:   MachineSvc,
		plugin:       plugin,
		user:         user,
		gpuType:      gpuType,
		instance:     instance,
		container:    container,
	}
}
