package controller

import (
	"github.com/gin-gonic/gin"
	"server/pkg-work-order/work_order/model"
	"server/pkg/businesserror"
	"server/pkg/http"
)

func (ctrl *WorkOrderController) CreateWorkOrderMachine(c *gin.Context) {
	var req model.CreateWorkOrderMachineReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId

	err := ctrl.WorkOrderSvc.CreateWorkOrderMachine(&req, user.UUID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) GetWorkOrderProviderMachineList(c *gin.Context) {
	var (
		req  model.GetWorkOrderMachineListReq
		resp model.GetWorkOrderProviderMachineListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.ProviderID = user.TenantId
	paged, workOrderMachines, err := ctrl.WorkOrderSvc.GetWorkOrderMachineList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderProviderMachineListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderProviderMachineListData, 0, len(workOrderMachines)),
	}

	machineTypeMap := make(map[int]model.WorkOrderMachineType)
	createUserMap := make(map[int]model.WorkOrderOperateUser)
	customMap := make(map[int]model.WorkOrderTenant)

	for _, machine := range workOrderMachines {
		if _, ok := machineTypeMap[machine.MachineTypeID]; !ok {
			machineType, err := ctrl.WorkOrderSvc.GetWorkOrderMachineTypeByTypeID(machine.MachineTypeID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get machine type failed")
				http.SendError(c, err)
				return
			}
			machineTypeMap[machine.MachineTypeID] = machineType
		}

		if _, ok := createUserMap[machine.CreateUserId]; !ok {
			createUser, err := ctrl.WorkOrderSvc.GetWorkOrderTenantUserByUserId(machine.CreateUserId)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get create user failed")
				http.SendError(c, err)
				return
			}

			createUserMap[machine.CreateUserId] = createUser
		}
		if _, ok := customMap[machine.CustomID]; !ok {
			custom, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(machine.CustomID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get custom failed")
				http.SendError(c, err)
				return
			}

			customMap[machine.CustomID] = custom
		}

		resp.List = append(resp.List, model.GetWorkOrderProviderMachineListData{
			ID:              machine.ID,
			MachineID:       machine.MachineID,
			MachineTypeID:   machine.MachineTypeID,
			MachineTypeName: machineTypeMap[machine.MachineTypeID].Name,
			CreateUserName:  createUserMap[machine.CreateUserId].Username,
			Status:          string(machine.Status),
			CustomID:        machine.CustomID,
			CustomName:      customMap[machine.CustomID].Name,
			DeliverTime:     machine.DeliverTime,
			CreatedAt:       machine.CreatedAt,
			Remark:          machine.Remark,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderCustomMachineList(c *gin.Context) {
	var (
		req  model.GetWorkOrderCustomMachineListReq
		resp model.GetWorkOrderCustomMachineListResp
	)
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)
	req.CustomID = user.TenantId
	paged, workOrderMachines, err := ctrl.WorkOrderSvc.GetWorkOrderCustomMachineList(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderCustomMachineListResp{
		PagedData: paged,
		List:      make([]model.GetWorkOrderCustomMachineData, 0, len(workOrderMachines)),
	}

	providerMap := make(map[int]model.WorkOrderTenant)
	for _, machine := range workOrderMachines {
		if _, ok := providerMap[machine.ProviderID]; !ok {
			provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(machine.ProviderID)
			if err != nil {
				ctrl.log.WithField("err", err).Error("get custom failed")
				http.SendError(c, err)
				return
			}
			providerMap[machine.ProviderID] = provider
		}

		materialInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialInfo(&model.GetWorkOrderMaterialInfoReq{MachineId: machine.MachineID})
		if err != nil {
			ctrl.log.WithField("err", err).Error("get materialInfo failed")
			http.SendError(c, err)
			return
		}

		resp.List = append(resp.List, model.GetWorkOrderCustomMachineData{
			ID:                  machine.ID,
			MachineID:           machine.MachineID,
			CustomBindMachineID: machine.CustomBindMachineID,
			ProviderName:        providerMap[machine.ProviderID].Name,
			MaterialInfo:        materialInfo,
			MachineRoom:         machine.MachineRoom,
			CabinetPosition:     machine.CabinetPosition,
			PublicIp:            machine.PublicIp,
			PrivateIp:           machine.PrivateIp,
			IPMI:                machine.IPMI,
			Purpose:             machine.Purpose,
			DeliverTime:         machine.DeliverTime,
			Remark:              machine.Remark,
		})
	}
	http.SendOK(c, resp)
	return
}

func (ctrl *WorkOrderController) UpdateWorkOrderMachineStatus(c *gin.Context) {
	var req model.UpdateWorkOrderMachineStatusReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMachineStatus(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderMachineCustom(c *gin.Context) {
	var req model.UpdateWorkOrderMachineCustomReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMachineCustom(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) WorkOrderMachineImport(c *gin.Context) {
	var req model.WorkOrderMachineImportReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}
	user := http.GetTenantUserInfo(c)
	affectRows, err := ctrl.WorkOrderSvc.ImportWorkOrderMachine(req.List, user.TenantId)
	if err != nil {
		http.SendError(c, err)
		return
	}
	http.SendOK(c, affectRows)
	return
}

func (ctrl *WorkOrderController) UpdateWorkOrderMachine(c *gin.Context) {
	var req model.UpdateWorkOrderMachineReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMachine(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
}

func (ctrl *WorkOrderController) UpdateWorkOrderMachineByProvider(c *gin.Context) {
	var req model.UpdateWorkOrderMachineByProviderReq
	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	err := ctrl.WorkOrderSvc.UpdateWorkOrderMachineByProvider(&req)
	if err != nil {
		http.SendError(c, err)
		return
	}

	http.SendOK(c, nil)
	return
}

func (ctrl *WorkOrderController) GetWorkOrderMachineDetail(c *gin.Context) {
	var (
		req  model.GetWorkOrderMachineDetailReq
		resp model.GetWorkOrderMachineDetailResp
	)

	if err := c.ShouldBind(&req); err != nil {
		ctrl.log.WithError(err).Info("bind failed")
		http.SendError(c, businesserror.ErrInvalidRequestParams)
		return
	}

	user := http.GetTenantUserInfo(c)

	machine, err := ctrl.WorkOrderSvc.GetWorkOrderMachineByMachineID(req.MachineID)
	if err != nil {
		http.SendError(c, err)
		return
	}

	if machine.ID == 0 {
		http.SendError(c, businesserror.ErrWorkOrderMachineNotExist)
		return
	}

	if machine.CustomID != user.TenantId {
		http.SendError(c, businesserror.ErrMachineNotExistInTenant)
		return
	}

	provider, err := ctrl.WorkOrderSvc.GetWorkOrderTenantByTenantId(machine.ProviderID)
	if err != nil {
		ctrl.log.WithField("err", err).Error("get provider failed")
		http.SendError(c, err)
		return
	}

	materialInfo, err := ctrl.WorkOrderSvc.GetWorkOrderMaterialInfo(&model.GetWorkOrderMaterialInfoReq{
		MachineId: req.MachineID,
	})
	if err != nil {
		ctrl.log.WithField("err", err).Error("get materialInfo failed")
		http.SendError(c, err)
		return
	}

	resp = model.GetWorkOrderMachineDetailResp{
		MachineID:    req.MachineID,
		ProviderID:   machine.ProviderID,
		ProviderName: provider.Name,
		MaterialInfo: materialInfo,
	}
	http.SendOK(c, resp)
	return
}
