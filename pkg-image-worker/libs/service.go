package image_libs

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"path/filepath"
	"server/pkg-image-worker/request"
	"server/pkg/constant"
	piModel "server/pkg/image/model"
	"server/pkg/logger"
	"sync"
	"time"
)

type ProgressBar struct {
	mutex sync.Mutex
	Param *piModel.ImageWorkerGetWaitingReply

	publicImageStatus   constant.PublicImageStatus
	publicImageProgress int
	publicImageErrMsg   string
	publicImageSize     int64

	communityImageStatus   constant.PublicImageStatus
	communityImageProgress int
	communityImageErrMsg   string
	communityImageSize     int64
}

func (p *ProgressBar) SetSuccessPublicImage(size int64) {
	p.mutex.Lock()
	p.publicImageStatus = constant.PublicImageSuccess
	p.publicImageSize = size
	p.mutex.Unlock()
}

func (p *ProgressBar) SetSuccessCommunityImage(size int64) {
	p.mutex.Lock()
	p.communityImageStatus = constant.PublicImageSuccess
	p.communityImageSize = size
	p.mutex.Unlock()
}

func (p *ProgressBar) SetStatusPublicImage(status constant.PublicImageStatus, g int) {
	p.mutex.Lock()
	p.publicImageStatus = status
	if g != 0 {
		p.publicImageProgress = g
	}
	p.mutex.Unlock()
}

func (p *ProgressBar) SetStatusCommunityImage(status constant.PublicImageStatus, g int) {
	p.mutex.Lock()
	p.communityImageStatus = status
	if g != 0 {
		p.communityImageProgress = g
	}
	p.mutex.Unlock()
}

func (p *ProgressBar) AddProgressPublicImage(g int) {
	p.mutex.Lock()
	p.publicImageProgress += g
	p.mutex.Unlock()
}

func (p *ProgressBar) AddProgressCommunityImage(g int) {
	p.mutex.Lock()
	p.communityImageProgress += g
	p.mutex.Unlock()
}

func (p *ProgressBar) SetErrPublicImage(err error) {
	p.mutex.Lock()
	p.publicImageStatus = constant.PublicImageFailed
	p.publicImageErrMsg = err.Error()
	p.mutex.Unlock()
}

func (p *ProgressBar) SetErrCommunityImage(err error) {
	p.mutex.Lock()
	p.communityImageStatus = constant.PublicImageFailed
	p.communityImageErrMsg = err.Error()
	p.mutex.Unlock()
}

func (p *ProgressBar) SetErrCommon(err error) {
	p.mutex.Lock()
	p.communityImageStatus = constant.PublicImageFailed
	p.communityImageErrMsg = err.Error()
	p.publicImageStatus = constant.PublicImageFailed
	p.publicImageErrMsg = err.Error()
	p.mutex.Unlock()
}

func (p *ProgressBar) AsyncProgressHook(ctx context.Context, l *logger.Logger) {
	p.publicImageStatus = constant.PublicImageWorkerStartProcessing

	f := func() {
		p.mutex.Lock()
		if p.publicImageErrMsg != "" {
			p.publicImageStatus = constant.PublicImageFailed
		}
		if p.publicImageStatus == constant.PublicImageSuccess {
			p.publicImageProgress = 100
		}
		if p.communityImageErrMsg != "" {
			p.communityImageStatus = constant.PublicImageFailed
		}
		if p.communityImageStatus == constant.PublicImageSuccess {
			p.communityImageProgress = 100
		}
		req := &piModel.ImageWorkerPostStatusParams{
			CommunityImageUUID:     p.Param.CommunityImageUUID,
			BuildPublicImage:       p.Param.BuildPublicImage,
			PublicImageStatus:      p.publicImageStatus,
			PublicImageProgress:    p.publicImageProgress,
			PublicImagErrMsg:       p.publicImageErrMsg,
			PublicImageSize:        p.publicImageSize,
			BuildCommunityImage:    p.Param.BuildCommunityImage,
			CommunityImageStatus:   p.communityImageStatus,
			CommunityImageProgress: p.communityImageProgress,
			CommunityImageErrMsg:   p.communityImageErrMsg,
			CommunityImageSize:     p.communityImageSize,
		}
		p.mutex.Unlock()
		err := request.PostStatus(req)
		if err != nil {
			l.WithField("params", req).ErrorE(err, "post publicImageStatus failed")
		}
		if (req.BuildPublicImage && req.PublicImageStatus == constant.PublicImageFailed || req.PublicImageStatus == constant.PublicImageSuccess) &&
			(req.BuildCommunityImage && req.CommunityImageStatus == constant.PublicImageFailed || req.CommunityImageStatus == constant.PublicImageSuccess) {
			return
		} else {
			t := time.NewTicker(time.Second * 5)
			<-t.C
			t.Stop()
		}
	}

	for {
		select {
		case <-ctx.Done():
			f()
			return
		default:
			f()
		}
	}
}

var SensitiveDocuments = []string{
	"/root/.ssh",
	"/root/.git",
	"/root/.bash_history",
	//"/root/.cache",
	"/root/.local/share/jupyter",
	"/root/miniconda3/pkgs",
	"/root/.local/share/Trash",
	"/tmp",
}

func ClearSensitiveDocuments(path string) (err error) {
	for _, v := range SensitiveDocuments {
		fp := filepath.Join(path, v)
		err = os.RemoveAll(fp)
		if err != nil {
			err = errors.Wrap(err, fmt.Sprintf("remove sensitive file %s failed", v))
			return err
		}
		_, err1 := os.Stat(fp)
		if err1 == nil {
			err = errors.New(fmt.Sprintf("remove sensitive file %s failed", fp))
			return err
		}

	}
	return nil
}
