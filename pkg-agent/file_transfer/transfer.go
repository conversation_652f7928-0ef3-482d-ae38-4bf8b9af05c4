package file_transfer

import (
	"context"
	"fmt"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path/filepath"
	"server/pkg-agent/agent_constant"
	h "server/pkg/http"
	"server/pkg/logger"
	"strconv"
	"strings"
	"sync"
	"time"
)

type FileTransfer struct {
	l            *logger.Logger
	dockerClient agent_constant.DockerClient

	runTaskCh chan struct{}
	taskMap   sync.Map

	taskMutex sync.Mutex
	taskPool  []RunP2PFileTransferRequest

	valMu       sync.RWMutex
	kvServerURL string `json:"kv_server_url"`
	ftServerURL string `json:"ft_server_url"`
	auth        string `json:"auth"`
}

func (f *FileTransfer) setUrlAuth(url, ftUrl, auth string) {
	f.valMu.Lock()
	defer f.valMu.Unlock()

	if len(url) > 0 && url != f.kvServerURL {
		f.kvServerURL = url
	}
	if len(ftUrl) > 0 && ftUrl != f.ftServerURL {
		f.ftServerURL = ftUrl
	}
	if len(auth) > 0 && auth != f.auth {
		f.auth = auth
	}
}

func (f *FileTransfer) getUrlAuth() (url, ftUrl, auth string) {
	f.valMu.RLock()
	defer f.valMu.RUnlock()

	if ftServerUrl != "" {
		return ftServerUrl + "api/v1/internal/kv/key/", ftServerUrl + "api/v1/internal/ft/status/", f.auth
	}

	return f.kvServerURL, f.ftServerURL, f.auth
}

func NewFileTransfer(dockerClient agent_constant.DockerClient) *FileTransfer {
	f := &FileTransfer{
		l:            logger.NewLogger("FileTransfer"),
		dockerClient: dockerClient,

		runTaskCh: make(chan struct{}, 2),
		taskMap:   sync.Map{},
	}

	return f
}

func (f *FileTransfer) RunDaemonWorker(ctx context.Context, panicChan chan<- error) {
	for {
		select {
		case <-ctx.Done():
			return
		case f.runTaskCh <- struct{}{}:
			f.taskMutex.Lock()
			if len(f.taskPool) == 0 {
				<-f.runTaskCh
				f.taskMutex.Unlock()
				time.Sleep(time.Millisecond * 500)
				continue
			}
			getOne := f.taskPool[0]
			f.taskPool = f.taskPool[1:]
			f.taskMutex.Unlock()

			go func(newTask RunP2PFileTransferRequest) {
				f.l.WithField("param", newTask).Info("start task")
				defer f.l.WithField("param", newTask).Info("ft task exit")
				runCtx, cancel := context.WithTimeout(ctx, time.Hour*48)
				f.setTaskCancel(newTask.TaskID, cancel)
				f.SrcRunP2PFileTransfer(runCtx, newTask)
				cancel()
				<-f.runTaskCh
			}(getOne)
		}
	}
}

func (f *FileTransfer) RunAPIServer(ctx context.Context, panicChan chan<- error) {
	gin.SetMode(gin.DebugMode)
	r := gin.Default()
	r.Use(h.GetGinPanicHandler(logger.NewLogger("ApiServerGinHandler")))

	r.Use(cors.New(cors.Config{
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "AppVersion"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
		AllowAllOrigins:  true,
	}))
	f.initHandlers(r)

	srv := &http.Server{
		Addr:              fmt.Sprintf("0.0.0.0:%d", ftApiPort),
		Handler:           r,
		ReadTimeout:       time.Hour,
		ReadHeaderTimeout: time.Hour,
		WriteTimeout:      time.Hour,
	}
	go func() {
		for {
			select {
			case <-ctx.Done():
				ctxWithTimeout, _ := context.WithTimeout(context.Background(), 10*time.Second)
				shutdownErr := srv.Shutdown(ctxWithTimeout)
				if shutdownErr != nil {
					f.l.ErrorE(shutdownErr, "shut down server failed")
				}
				return
			}
		}
	}()

	panicChan <- srv.ListenAndServe()
	return
}

func (f *FileTransfer) initHandlers(r *gin.Engine) {
	api := r.Group("/api/v1")
	{
		src := api.Group("/src")
		{
			src.POST(FileTransferStartRoute, func(c *gin.Context) {
				var req P2PFileTransferRequest
				if err := c.ShouldBindJSON(&req); err != nil {
					f.l.WithField("err", err).Error("check invalid request params.")
					c.String(http.StatusBadRequest, "request invalid "+err.Error())
					return
				}
				res, err := f.SrcStartP2PFileTransferTask(req)
				if err != nil {
					f.l.WithField("err", err).Error("check failed.")
					c.String(http.StatusInternalServerError, "SrcStartP2PFileTransferTask failed "+err.Error())
					return
				}
				c.JSON(http.StatusOK, res)
			})

			src.POST(FileTransferCancelRoute, func(c *gin.Context) {
				var req P2PFileTransferRequest
				if err := c.ShouldBind(&req); err != nil {
					f.l.WithField("err", err).Error("check invalid request params.")
					c.String(http.StatusBadRequest, "request invalid "+err.Error())
					return
				}
				f.SrcCancelP2PFileTransferTask(req)
				c.JSON(http.StatusOK, nil)
			})

			src.POST(FileTransferStatusRoute+"/:taskID", func(c *gin.Context) {
				status := f.SrcGetTaskStatus(TaskID(strings.Trim(c.Param("taskID"), `"`)))
				c.JSON(http.StatusOK, status)
			})
		}

		dst := api.Group("/dst")
		{
			dst.POST("/check", func(c *gin.Context) {
				var req P2PFileTransferRequest
				if err := c.ShouldBind(&req); err != nil {
					f.l.WithField("err", err).Error("check invalid request params.")
					c.String(http.StatusBadRequest, "request invalid "+err.Error())
					return
				}
				res, err := f.DstP2PCheck(req)
				if err != nil {
					f.l.WithField("err", err).Error("check failed.")
					c.String(http.StatusInternalServerError, "DstP2PCheck failed "+err.Error())
					return
				}
				c.JSON(http.StatusOK, res)
			})
		}

		diffCache := api.Group("/diff_cache")
		{
			diffCache.POST("", func(c *gin.Context) {
				var req struct {
					FileName string `json:"file_name"`
				}
				if err := c.ShouldBind(&req); err != nil {
					f.l.WithField("err", err).Error("check invalid request params.")
					c.String(http.StatusBadRequest, "request invalid "+err.Error())
					return
				}

				if c.GetHeader(agent_constant.HeaderAuthName) != agent_constant.HeaderAuthContent {
					c.String(http.StatusUnauthorized, "")
				}

				if req.FileName == "" {
					c.String(http.StatusBadRequest, "empty file name")
					return
				}

				diffFilePath := filepath.Join(agent_constant.MigrationOssTmpPath, req.FileName)

				fileStat, err := os.Lstat(diffFilePath)
				if err != nil {
					c.String(http.StatusNotFound, "file not found: %s", req.FileName)
					return
				}

				c.Header(agent_constant.HeaderFileSize, strconv.FormatInt(fileStat.Size(), 10))
				c.File(diffFilePath)
			})
		}

	}
}
