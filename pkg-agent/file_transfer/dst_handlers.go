package file_transfer

import (
	"fmt"
	"os"
	"server/pkg/global_kv/kv_libs"
	"server/pkg/libs"
)

type P2PCheckResult struct {
	DstHost          string `json:"dst_host"`
	DstShellPort     int    `json:"dst_shell_port"`
	DstShellUsername string `json:"dst_shell_username"`
	DstShellPassword string `json:"dst_shell_password"`
	DstPath          string `json:"dst_path"`
}

func (f *FileTransfer) DstP2PCheck(param P2PFileTransferRequest) (res P2PCheckResult, err error) {
	f.l.WithField("param", param).Info("got p2p check request")

	if !kv_libs.CheckAuthorizationIsValid(param.Authorization) {
		err = fmt.Errorf("invalid authorization")
		return
	}

	var dstPath string
	dstPath, err = f.getPath(param.DstPathType, param.DstPath, param.DstVMName)
	if err != nil {
		return
	}

	_ = os.MkdirAll(dstPath, 0o755)

	if param.DeleteDstFolder {
		err1 := libs.ClearFolder(dstPath)
		if err1 != nil {
			f.l.WithField("path", dstPath).ErrorE(err1, "clear path failed")
		}
	}

	res = P2PCheckResult{
		DstHost:          param.DstHost,
		DstShellPort:     ftSSHPort,
		DstShellUsername: ftUser,
		DstShellPassword: ftPass,
		DstPath:          dstPath,
	}
	return
}
