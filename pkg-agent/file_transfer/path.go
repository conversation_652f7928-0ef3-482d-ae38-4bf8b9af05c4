package file_transfer

import (
	"context"
	"github.com/docker/docker/api/types"
	"github.com/pkg/errors"
	"server/pkg/libs"
	"time"
)

func (f *FileTransfer) getPath(pathType FileTransferPathType, path, vmName string) (resultPath string, err error) {
	switch pathType {
	case PosixPath:
		resultPath = path
		return
	case DockerVolumePath:
		ctx, _ := context.WithTimeout(context.Background(), time.Second*5)
		var volume types.Volume
		volume, err = f.dockerClient.VolumeInspect(ctx, vmName)
		if err != nil {
			err = errors.Wrap(err, "volume inspect failed")
			return
		}

		resultPath = libs.SafeFilePathJoin(volume.Mountpoint, path)
	case DockerDiffPath:
		ctx, _ := context.WithTimeout(context.Background(), time.Second*5)
		var containerJSON types.ContainerJSON
		containerJSON, err = f.dockerClient.ContainerInspect(ctx, vmName)
		if err != nil {
			err = errors.Wrap(err, "container inspect one shot failed")
			return
		}

		upperDir := containerJSON.GraphDriver.Data["UpperDir"]
		resultPath = libs.SafeFilePathJoin(upperDir, path)
	}

	return
}
