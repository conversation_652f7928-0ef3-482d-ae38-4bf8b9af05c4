package file_transfer

import (
	"encoding/json"
	"fmt"
	"github.com/levigross/grequests"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"server/pkg/libs"
	"server/pkg/logger"
	"sync"
	"time"
)

type FileTransferPathType string

const (
	PosixPath        FileTransferPathType = ""
	DockerVolumePath FileTransferPathType = "DockerVolumePath"
	DockerDiffPath   FileTransferPathType = "DockerDiffPath" // clone实例用
)

const (
	DefaultAPIPort = 20221
	DefaultSSHPort = 20222
)

var (
	FileTransferAPIURLLocalhost = fmt.Sprintf("http://127.0.0.1:%d/api/v1/src", DefaultAPIPort)
	FileTransferStartRoute      = "/start"
	FileTransferCancelRoute     = "/cancel"
	FileTransferStatusRoute     = "/status"
)

type TaskID string

func (t TaskID) StatusField() string {
	return fmt.Sprintf("%s-status", t)
}

func (t TaskID) cancelField() string {
	return fmt.Sprintf("%s-cancel", t)
}

type TaskStatus struct {
	TaskID TaskID `json:"task_id"`

	//  NotFound Status = ""
	//	Queued   Status = "Queued"
	//	Running  Status = "Running"
	//	Failed   Status = "Failed"
	//	Succeed  Status = "Succeed"
	Status       Status `json:"status"`
	Progress     int    `json:"progress"`      // 100 = 100%
	Speed        string `json:"speed"`         // 111.72MB/s
	FinishedSize string `json:"finished_size"` // 10808634429 (byte)
	ErrMsg       string `json:"err_msg"`

	Time         time.Time `json:"time"`
	lastSentTime time.Time
}

func (i TaskStatus) ToString() (s string) {
	byteData, _ := json.Marshal(i)
	return string(byteData)
}

type Status string

const (
	NotFound Status = ""
	Queued   Status = "Queued"
	Running  Status = "Running"
	Failed   Status = "Failed"
	Succeed  Status = "Succeed"
)

func PostHttpUrl(url string, payload interface{}, auth string) (int, []byte, error) {
	rsp, err := grequests.Post(url, &grequests.RequestOptions{
		RequestTimeout: time.Second * 10,
		Headers: map[string]string{
			"authorization": auth,
		},
		JSON: payload,
	})
	if err != nil {
		err = errors.Wrap(err, "internal requests failed.")
		return http.StatusServiceUnavailable, nil, err
	}
	defer rsp.RawResponse.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		err = fmt.Errorf("Failed with status code %d ", rsp.StatusCode)
	}

	content, _ := io.ReadAll(rsp.RawResponse.Body)
	return rsp.StatusCode, content, err
}

var statusHookSendMu sync.Mutex

func DoFTServerStatusHook(host, auth string, status *TaskStatus) {
	l := logger.NewLogger("ft_status_hook")
	l.WithFields(map[string]interface{}{
		"host":   host,
		"auth":   auth,
		"status": libs.IndentString(status),
	}).Info("DoFTServerStatusHook doing.....")

	statusHookSendMu.Lock()
	defer statusHookSendMu.Unlock()
	if len(host) == 0 || len(auth) == 0 {
		return
	}

	if len(status.ErrMsg) > 200 {
		status.ErrMsg = status.ErrMsg[:200]
	}
	err := libs.Retry(libs.RetryParams{
		Do: func() error {
			statusCode, content, postErr := PostHttpUrl(host+string(status.TaskID), status, auth)
			if postErr != nil {
				return postErr
			}
			if statusCode != http.StatusOK {
				return errors.New("status code != 200, " + string(content))
			}
			return nil
		},
		RetryInterval: time.Second * 2,
		RetryTimes:    10,
		Name:          "FT Status Hook",
		L:             l,
	})
	if err != nil {
		l.ErrorE(err, "retry FtStatusHook failed")
	}
}
