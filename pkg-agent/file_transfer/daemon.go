package file_transfer

import (
	"context"
	"github.com/pkg/errors"
	"io/ioutil"
	"os"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/logger"
	"strings"
)

var ftUser, ftPass string
var ftApiPort, ftSSHPort int
var ftServerUrl string

func RunFileTransfer(ctx context.Context, panicChan chan<- error, user, pass string, apiPort, sshPort int, serverUrl string) {
	l := logger.NewLogger("RunFileTransfer")
	l.Info("ready to init FileTransfer...")
	defer func() {
		l.Info("FileTransfer quit...")
	}()
	ftUser, ftPass = user, pass
	ftApiPort, ftSSHPort = apiPort, sshPort

	ftServerUrl = strings.TrimSpace(serverUrl)
	l.Info("ftServerUrl is %s", ftServerUrl)

	if len(ftPass) == 0 {
		ftPass = os.Getenv("pass")
	}
	if len(ftPass) == 0 {
		passByte, _ := ioutil.ReadFile("/tmp/pass")
		ftPass = string(passByte)
	}
	if len(ftPass) == 0 {
		panicChan <- errors.New("no password given, panic...")
		return
	}
	l.Info("got pass %s", ftPass)

	dockerClient, err := constant.NewDockerClient()
	if err != nil {
		err = errors.Wrap(err, "failed to new docker client")
		panicChan <- err
		return
	}

	fileTransfer := NewFileTransfer(dockerClient)
	go fileTransfer.RunDaemonWorker(ctx, panicChan)
	fileTransfer.RunAPIServer(ctx, panicChan)
}
