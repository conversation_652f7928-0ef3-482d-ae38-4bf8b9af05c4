package agent_cg_tool

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"golang.org/x/net/context"
	"net/http"
	"os"
	"path/filepath"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/diff_cache"
	"server/pkg/logger"
	"strconv"
)

type ModelFileDownloadByAgentRequest struct {
	Token string `json:"token"` // 用于本地没找到,去storage-agent下载
	Host  string `json:"host"`

	ModelName   string `json:"model_name"`
	FileName    string `json:"file_name"`
	FileSize    int64  `json:"file_size"`
	MD5         string `json:"md5"`
	ContainerID string `json:"container_id"`
}

func CGDownloadModel(c *gin.Context) {
	// 只有一个函数, 把中间件校验直接放在此处
	if ok, _ := IsCgTokenOK(c.GetHeader(CgTokenKey)); !ok {
		c.String(401, "cgToken incorrect")
		return
	}

	l := logger.NewLogger("CGDownloadModel")
	var req ModelFileDownloadByAgentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		l.ErrorE(err, "request params bind failed")
		c.String(400, "request params bind failed")
		return
	}

	l.WithField("payload", req).Info("get a msg")
	l = l.WithFields(map[string]interface{}{"fileName": req.FileName, "container_id": req.ContainerID})

	/*
		检查本地是否存在
			存在 直接拷贝至容器内目标路径
			不存在
				查看本地cache是否存在
					存在		从缓存下载
					不存在	从oss下载
			重命名, 拷贝至目标路径

		为保证可读性, 降低耦合, 每个返回方式单独处理, 所有会有很多重复代码
	*/

	cacheFileName := fmt.Sprintf("cg-%s", req.MD5)
	md5FilePath := filepath.Join(agent_constant.MigrationOssTmpPath, cacheFileName)
	tmpFilePath := fmt.Sprintf("%s.%s.downloading", md5FilePath, req.ContainerID)

	// 存在本地文件
	stat, err := os.Stat(md5FilePath)
	if err == nil {
		l.Info("find local file [%s], try to use it.", md5FilePath)
		// 存在判断大小, 符合的保留, 不符合的删除后重新下载
		if stat.Size() == req.FileSize {
			c.File(md5FilePath)
			return
		}
		l.Info("file [%s] local size does not match the remote size, [%d!=%d], remove and download it now.", md5FilePath, stat.Size(), req.FileSize)
		err = os.Remove(md5FilePath)
		if err != nil {
			l.Info("remove file [%s] failed", md5FilePath)
		}
	}

	// 没有本地文件, 新下载, 准备.downloading文件
	var file *os.File
	defer os.Remove(tmpFilePath) // 在退出前都主动清除一下.downloading文件
	file, err = os.OpenFile(tmpFilePath, os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		l.ErrorE(err, "open file [%s] failed", tmpFilePath)
		return
	}
	defer file.Close()

	// 没有本地文件, 查看内网cache
	var needCache bool
	diffCacheGetParams := &diff_cache.DiffCacheGetParams{
		FileName:        cacheFileName,
		NotAvailableMap: map[string]struct{}{},
	}
	ctx := context.Background()
	for {
		file.Seek(0, 0)
		// 将获取的逻辑封装在diff_cache内部, 此处只需要知道有没有可用的缓存就可以了
		cacheFileOK, cacheFileReader, _, cacheFromIP, needNewCache := diff_cache.DiffCacheGet(l, diffCacheGetParams)
		needCache = needNewCache
		if !cacheFileOK {
			l.Info("file [%s] have no cache, will get from oss...", cacheFileName)
			break
		}

		if needCache {
			// 获取到一个可用的内网文件, 开始下载
			_, err = copyWithProgress(ctx, file, cacheFileReader)
			if err != nil {
				l.ErrorE(err, "download [%s] from [%s] failed, try next one...", tmpFilePath, cacheFromIP)
				// 此处失败不应退出, 而是尝试下一个本地缓存
				diffCacheGetParams.NotAvailableMap[cacheFromIP] = struct{}{}
				continue
			}
			l.Info("download file [%s] from [%s] success", tmpFilePath, cacheFromIP)

			// 下载完成, 重命名, 发送文件
			err = os.Rename(tmpFilePath, md5FilePath)
			if err != nil {
				l.ErrorE(err, "Internal error, rename file [%s] to [%s] failed", tmpFilePath, md5FilePath)
				return
			}
			diff_cache.AddCacheRecord(l, cacheFileName)
			c.File(md5FilePath)
			return
		}

		// 不需要cache
		c.DataFromReader(200, req.FileSize, "application/octet-stream", cacheFileReader, nil)
		return
	}

	// 所有的本地缓存尝试失败了, 从oss下载, 此时一定是需要本地缓存的
	l.Info("no cache found, download file from cg.")

	// 去oss下载
	downloadUrl := fmt.Sprintf("%s/api/v1/file/%s/download", req.Host, req.Token)
	resp, err := http.Get(downloadUrl)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	_, err = copyWithProgress(ctx, file, resp.Body)
	if err != nil {
		l.ErrorE(err, "download file [%s] from cg failed", tmpFilePath)
		return
	}

	tmpStat, err := os.Lstat(tmpFilePath)
	if err != nil {
		l.ErrorE(err, "download file [%s] finished, but stat failed, %s", tmpFilePath)
		return
	}

	if tmpStat.Size() != req.FileSize {
		l.Error("Failed to download file [%s], file size exception [%d!=%d]", tmpFilePath, req.FileSize, stat.Size())
		return
	}

	err = os.Rename(tmpFilePath, md5FilePath)
	if err != nil {
		l.ErrorE(err, "Internal error, rename file [%s] to [%s] failed", tmpFilePath, md5FilePath)
		return
	}

	diff_cache.AddCacheRecord(l, cacheFileName)
	c.File(md5FilePath)
	return
}

func CGDownloadModelTmpFileProgress(c *gin.Context) {
	// 只有一个函数, 把中间件校验直接放在此处
	if ok, _ := IsCgTokenOK(c.GetHeader(CgTokenKey)); !ok {
		c.String(401, "cgToken incorrect")
		return
	}

	l := logger.NewLogger("CGDownloadModel")
	var req ModelFileDownloadByAgentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		l.ErrorE(err, "request params bind failed")
		c.String(400, "request params bind failed")
		return
	}

	cacheFileName := fmt.Sprintf("cg-%s", req.MD5)
	md5FilePath := filepath.Join(agent_constant.MigrationOssTmpPath, cacheFileName)
	tmpFilePath := fmt.Sprintf("%s.%s.downloading", md5FilePath, req.ContainerID)

	stat, err := os.Stat(tmpFilePath)
	if err != nil {
		c.String(404, "have no tmp file")
		return
	}

	c.String(200, strconv.FormatInt(stat.Size(), 10))
	return
}
