package messenger

import (
	"context"
	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"server/pkg-agent/agent_constant"
	"server/pkg/serializer"
	"strings"
	"time"
)

func (m *Messenger) StartHandler(ctx context.Context, panic<PERSON>han chan<- error) (reader <-chan Message, writer chan<- Message, err error) {
	if m.conn == nil {
		err = errors.New("connection over server is nil, quit")
		return
	}

	receiver := make(chan Message, 100)
	sender := make(chan Message, 100)

	go func() {
		m.runReader(ctx, panic<PERSON>han, receiver)
		close(receiver)
	}()
	go func() {
		m.runWriter(ctx, panic<PERSON>han, sender)
		close(sender)
	}()
	return receiver, sender, nil
}

// runReader receive message from server
func (m *Messenger) runReader(ctx context.Context, panicChan chan<- error, out chan<- Message) {
	defer func() {
		closeErr := m.conn.Close()
		if closeErr != nil {
			m.logger.ErrorE(closeErr, "close connection failed")
		}
	}()

	for {
		select {
		case <-ctx.Done():
			m.logger.Info("reader quit gracefully...")
			return
		default:
			now := time.Now()
			err := m.conn.SetReadDeadline(now.Add(ReadDeadlinePeriod))
			if err != nil {
				err = errors.Wrapf(err, "set message read dead line from reader failed now: %s, timeout: %s", now.String(), now.Add(ReadDeadlinePeriod))
				panicChan <- err
				return
			}
			_, message, err := m.conn.ReadMessage()
			if err != nil {
				err = errors.Wrap(err, "read message from reader failed")
				panicChan <- err
				return
			}

			if agent_constant.VerboseMode || strings.Contains(string(message), string(ContainerStatusType)) {
				m.logger.Info("get msg from pipe: %s", string(message))
			}

			var inputMsg Message
			err = inputMsg.ParseFromString(message)
			if err != nil {
				m.logger.WithField("err", err).WithField("msg", string(message)).Warn("wrong format of receive msg, skip")
				err = nil
				continue
			}

			out <- inputMsg
		}

	}
}

// runWriter send msg to server
func (m *Messenger) runWriter(ctx context.Context, panicChan chan<- error, in <-chan Message) {
	defer func() {
		closeErr := m.conn.Close()
		if closeErr != nil {
			m.logger.ErrorE(closeErr, "close connection failed")
		}
	}()

	pingTicker := time.NewTicker(PingPeriod)

	for {
		select {
		case <-ctx.Done():
			m.logger.Info("writer quit gracefully...")
			return
		case <-pingTicker.C:
			_ = m.conn.SetWriteDeadline(time.Now().Add(WriteWait))
			err := m.conn.WriteMessage(websocket.PingMessage, []byte("ping msg"))
			if err != nil {
				err = errors.Wrap(err, "write ping message failed")
				m.logger.ErrorE(err, "panic")
				// panic级别错误, 需要退出程序重新连接
				panicChan <- err
				return
			}
		case msg, ok := <-in:
			if !ok {
				err := errors.New("writer chan is not ok")
				m.logger.ErrorE(err, "panic")
				// panic级别错误, 需要退出程序重新连接
				panicChan <- err
				return
			}
			content, err := msg.Marshal()
			if err != nil {
				m.logger.WithField("err", err).WithField("msg", serializer.IndentString(msg)).Warn("wrong format of send msg, skip")
				continue
			}

			if agent_constant.VerboseMode || strings.Contains(msg.Payload, "running") {
				m.logger.Info("send msg to pipe: %s", string(content))
			}

			_ = m.conn.SetWriteDeadline(time.Now().Add(WriteWait))
			err = m.conn.WriteMessage(websocket.TextMessage, content)
			if err != nil {
				err = errors.Wrap(err, "write message failed")
				m.logger.ErrorE(err, "panic")
				// panic级别错误, 需要退出程序重新连接
				panicChan <- err
				return
			}
		}
	}
}
