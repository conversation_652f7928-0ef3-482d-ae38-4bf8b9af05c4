package messenger

import (
	"context"
	"github.com/gorilla/websocket"
	"github.com/pkg/errors"
	"net/http"
	"server/pkg/logger"
	"time"
)

type MessengerType string

const (
	// 虽然作为websocket的 server 和 client, 处理数据的方式很相似, 但是还是存在细微的差别(初始化/处理信息的方式等)
	server MessengerType = "server"
	client MessengerType = "client"

	// Maximum message size allowed from peer.
	maxMessageSize     = 512000
	WriteWait          = 10 * time.Second
	PingPeriod         = time.Second * 15
	ReadDeadlinePeriod = time.Second * 65
)

type Messenger struct {
	logger *logger.Logger

	tp MessengerType

	// --- begin params for client
	serverURL string
	// use to trans authorization info and other info
	header http.Header
	// --- end params for client

	conn *websocket.Conn
}

func NewMessengerForServer(conn *websocket.Conn) (messenger *Messenger, err error) {
	newLogger := logger.NewLogger("Messenger")
	messenger = &Messenger{
		logger: newLogger,
		conn:   conn,
		tp:     server,
	}
	conn.SetPingHandler(nil)
	conn.SetPongHandler(func(string) error {
		setErr := conn.SetReadDeadline(time.Now().Add(ReadDeadlinePeriod))
		if setErr != nil {
			newLogger.ErrorE(setErr, "pong handler set read deadline failed")
		}
		return nil
	})
	err = messenger.checkConnectionHealth()
	return
}

func NewMessengerForClient(serverURL string, header http.Header) (messenger *Messenger) {
	newLogger := logger.NewLogger("Messenger")
	messenger = &Messenger{
		logger:    newLogger,
		serverURL: serverURL,
		header:    header,
		tp:        client,
	}
	return
}

func (m *Messenger) Dial(ctx context.Context) error {
	if m.tp != client {
		return errors.New("you can only use dial when your messenger is a client type")
	}
	c, _, err := websocket.DefaultDialer.DialContext(ctx, m.serverURL, m.header)
	if err != nil {
		m.logger.ErrorE(err, "dial connection [%s] to server failed", m.serverURL)
		return err
	}
	m.logger.Info("dial connection [%s] to server success", m.serverURL)
	m.conn = c
	m.conn.SetPingHandler(nil)
	m.conn.SetPongHandler(func(string) error {
		setErr := m.conn.SetReadDeadline(time.Now().Add(ReadDeadlinePeriod))
		if setErr != nil {
			m.logger.ErrorE(setErr, "pong handler set read deadline failed")
		}
		return nil
	})
	m.conn.SetCloseHandler(func(code int, text string) error {
		m.logger.WithField("text", text).WithField("code", code).Warn("server close the connection")
		return errors.New("server close the connection")
	})

	return m.checkConnectionHealth()
}

func (m *Messenger) checkConnectionHealth() error {
	err := m.conn.SetWriteDeadline(time.Now().Add(time.Second * 10))
	if err != nil {
		return err
	}
	return m.conn.WriteMessage(websocket.PingMessage, nil)
}
