package agent_container

import (
	"context"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/filters"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/logger"
)

func ImageList(ctx context.Context, l *logger.Logger, dockerClient constant.DockerClient, imageName string) (imageSummary []types.ImageSummary, err error) {
	filter := filters.NewArgs()
	/**
	reference” : that can be used in order to isolate images having a certain name or tag;
	“before” : to filter images created “before” a specific point in time;
	“since” : to filter images since a specific point in time (usually another image creation);
	“label” : if you used the LABEL instruction to create metadata for your image you can filter them later with this key
	“dangling” : in order to isolate images that are not used anymore.
	*/
	filter.Add("reference", imageName)
	opts := types.ImageListOptions{
		Filters: filter,
	}
	imageSummary, err = dockerClient.ImageList(ctx, opts)
	if err != nil {
		l.ErrorE(err, " remove image %s failed", imageName)
		return
	}
	l.Info("docker get image list success.")
	return
}
