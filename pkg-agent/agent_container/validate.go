package agent_container

import (
	constant "server/pkg-agent/agent_constant"
)

func validateContainerParams(param *constant.NewContainerParam, dockerClient constant.DockerClient) error {
	if param == nil || len(param.ContainerID) == 0 {
		return ParamsIsInvalid
	}
	if dockerClient == nil {
		return DockerClientIsInvalid
	}

	return nil
}

func validateContainerRuntimeParams(param *constant.NewContainerRuntimeParam, dockerClient constant.DockerClient) error {
	if param == nil || len(param.ContainerID) == 0 {
		return ParamsIsInvalid
	}
	if dockerClient == nil {
		return DockerClientIsInvalid
	}

	return nil
}
