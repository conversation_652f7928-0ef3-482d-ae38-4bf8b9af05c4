package agent_container

import (
	"context"
	"fmt"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/mount"
	volumetypes "github.com/docker/docker/api/types/volume"
	"github.com/docker/docker/client"
	"github.com/docker/docker/volume/mounts"
	"os"
	constant "server/pkg-agent/agent_constant"
	"server/pkg/libs"
	"server/pkg/serializer"
	"server/pkg/xfs_quota"
	"strings"
)

// TODO: Byte
func (c *Container) getVolume(name string, sizeInByte int64) (volume types.Volume, err error) {
	var exist bool
	exist, volume, err = c.checkVolumeExist(name)
	if err != nil {
		return
	}
	if exist {
		// 权宜之计： 临时新增的trick， 后续dockerd版本更新后会支持动态设置volumesize， 这里后面会删除
		// 如果容器的local硬盘发生了size变化， 则需要在启动完成后调用xfs调整quota为真实设置的size
		optsIsEqual := compareVolumeOpts(getVolumeOpts(sizeInByte), volume.Options)
		c.logger.Info("volume opts compare %t, new: %s, old %s", optsIsEqual, libs.IndentString(getVolumeOpts(sizeInByte)), libs.IndentString(volume.Options))
		if !optsIsEqual {
			c.PostHooks = append(c.PostHooks, func() error {
				return xfs_quota.UpdatePathQuotaSize(constant.QuotaMountPath, volume.Mountpoint, sizeInByte)
			})
		}
		return
	}

	volume, err = c.dockerClient.VolumeCreate(context.Background(), volumetypes.VolumeCreateBody{
		Driver:     "local",
		DriverOpts: getVolumeOpts(sizeInByte),
		Labels:     nil,
		Name:       name,
	})
	c.logger.Info("volume %s create result: %T", serializer.IndentString(volume), err)
	return
}

func compareVolumeOpts(optsA, optsB map[string]string) (equal bool) {
	if len(optsA) != len(optsB) {
		return
	}
	for k := range optsA {
		if optsA[k] != optsB[k] {
			return
		}
	}
	return true
}

func getVolumeOpts(sizeInByte int64) map[string]string {
	sizeInMB := constant.ToSize(sizeInByte, constant.TypeMB, constant.DefaultStorage, constant.FloorType)
	return map[string]string{
		"size": fmt.Sprintf("%dM", sizeInMB),
	}
}

func (c *Container) checkVolumeExist(name string) (exist bool, volume types.Volume, err error) {
	volume, err = c.dockerClient.VolumeInspect(context.Background(), name)
	if err == nil {
		exist = true
		err = nil
		return
	}
	if client.IsErrNotFound(err) {
		exist = false
		err = nil
		return
	}
	return
}

func (c *Container) getTmpfsVolume(name string, sizeInMB int64) (volume types.Volume, err error) {
	var exist bool
	exist, volume, err = c.checkVolumeExist(name)
	if err != nil {
		return
	}
	if exist {
		return
	}

	volume, err = c.dockerClient.VolumeCreate(context.Background(), volumetypes.VolumeCreateBody{
		Driver: "local",
		DriverOpts: map[string]string{
			"o":      fmt.Sprintf("size=%dM", sizeInMB),
			"type":   "tmpfs",
			"device": "tmpfs",
		},
		Labels: nil,
		Name:   name,
	})
	c.logger.Info("volume %s create result: %T", serializer.IndentString(volume), err)
	return
}

/*
 * isCreating: 在创建时, 如果网盘路径不存在直接跳过一行; 在启动时, 如果网盘路径不存在, 不跳过, 设置 source 为空.
 */
func (c *Container) getContainerMounts(mountList []string, isCreating bool) (mountOutput []mount.Mount) {
	parser := mounts.NewParser(mounts.OSLinux)

	for _, v := range mountList {
		m, err := parser.ParseMountRaw(v, "local")
		if err != nil {
			c.logger.ErrorE(err, "Error format of mount content, watch out your content: %s", v)
			continue
		}

		// 对网盘挂载特殊校验, '/storage'
		if strings.Contains(m.Source, constant.NetDiskOnMachineMountDir) ||
			strings.Contains(m.Source, constant.ADFSOnMachineMountDir) {
			// if not exist, continue
			if !libs.ExistPathWithCtx(context.Background(), m.Source) { // with timeout: 800ms
				if isCreating {
					continue
				} else {
					// 为了删掉挂载点，source设置为空，docker内部自定义的逻辑会清除该挂载点
					m.Source = ""
				}
			}
		}

		thisMount := mount.Mount{
			Type:     mount.TypeBind,
			Source:   m.Source,
			Target:   m.Destination,
			ReadOnly: !m.RW,
		}
		if m.Propagation == mount.PropagationShared {
			thisMount.BindOptions = &mount.BindOptions{Propagation: m.Propagation}
		}

		mountOutput = append(mountOutput, thisMount)
	}
	return
}

func (c *Container) preStartGetContainerMounts(mountList []string) (mountOutput []mount.Mount) {
	parser := mounts.NewParser(mounts.OSLinux)

	for _, v := range mountList {
		m, err := parser.ParseMountRaw(v, "local")
		if err != nil {
			c.logger.ErrorE(err, "Error format of mount content, watch out your content: %s", v)
			continue
		}

		os.MkdirAll(m.Source, os.ModePerm)

		thisMount := mount.Mount{
			Type:     mount.TypeBind,
			Source:   m.Source,
			Target:   m.Destination,
			ReadOnly: !m.RW,
		}
		if m.Propagation == mount.PropagationShared {
			thisMount.BindOptions = &mount.BindOptions{Propagation: m.Propagation}
		}

		mountOutput = append(mountOutput, thisMount)
	}
	return
}

func (c *Container) GetVolumeSize(containerID constant.ContainerID) (size, usedSize uint64, err error) {
	exist, volume, err := c.checkVolumeExist(GetStorageVolumeName(containerID))
	if err != nil {
		return
	}
	if !exist {
		c.logger.WithField("containerID", containerID).Warn("container volume is not exist")
		return 0, 0, nil
	}
	size, usedSize, err = xfs_quota.GetPathQuotaSize(volume.Mountpoint)
	if err != nil {
		return
	}
	return
}
