package agent_machine_monitor_test

import (
	"context"
	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/network"
	volumetypes "github.com/docker/docker/api/types/volume"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	specs "github.com/opencontainers/image-spec/specs-go/v1"
	"io"
	constant "server/pkg-agent/agent_constant"
	. "server/pkg-agent/agent_machine_monitor"
	"time"
)

var _ = Describe("Machine", func() {
	It("machine", func() {
		fakeDockerClient := NewFakeDockerClient()

		runtime := NewMachineMonitor(fakeDockerClient, "")
		_, err := runtime.GetMachineHardwareInfo()
		Ω(err).Should(HaveOccurred())

		fakeDockerClient.DockerVersion = constant.ChoseAnAvailableDockerdVersion()
		_, err = runtime.GetMachineHardwareInfo()
		Ω(err).ShouldNot(HaveOccurred())

		_, err = runtime.GetMachineHealthInfo()
		Ω(err).ShouldNot(HaveOccurred())
	})
})

type FakeDockerClient struct {
	DockerVersion string
}

func NewFakeDockerClient() *FakeDockerClient {
	return &FakeDockerClient{}
}

func (d *FakeDockerClient) Info(ctx context.Context) (res types.Info, err error) {
	res.ServerVersion = d.DockerVersion
	return
}

func (d *FakeDockerClient) Ping(ctx context.Context) (res types.Ping, err error) {
	return
}

func (d *FakeDockerClient) ContainerInspect(ctx context.Context, containerID string) (res types.ContainerJSON, err error) {
	return
}

func (d *FakeDockerClient) ContainerInspectWithRaw(ctx context.Context, containerID string, getSize bool) (res types.ContainerJSON, raw []byte, err error) {
	return
}

func (d *FakeDockerClient) ContainerStatsOneShot(ctx context.Context, containerID string) (res types.ContainerStats, err error) {
	return
}

func (d *FakeDockerClient) ContainerCreate(ctx context.Context, config *container.Config, hostConfig *container.HostConfig, networkingConfig *network.NetworkingConfig, platform *specs.Platform, containerName string) (res container.ContainerCreateCreatedBody, err error) {
	return
}

func (d *FakeDockerClient) ContainerUpdate(ctx context.Context, containerID string, updateConfig container.UpdateConfig) (res container.ContainerUpdateOKBody, err error) {
	return
}

func (d *FakeDockerClient) ContainerStart(ctx context.Context, containerID string, options types.ContainerStartOptions) (err error) {
	return
}

func (d *FakeDockerClient) ContainerStop(ctx context.Context, containerID string, timeout *time.Duration) (err error) {
	return
}

func (d *FakeDockerClient) ContainerRemove(ctx context.Context, containerID string, options types.ContainerRemoveOptions) (err error) {
	return
}

func (d *FakeDockerClient) ContainerExport(ctx context.Context, containerID string) (res io.ReadCloser, err error) {
	return
}

func (d *FakeDockerClient) ContainerList(ctx context.Context, options types.ContainerListOptions) (res []types.Container, err error) {
	return
}

func (d *FakeDockerClient) ImagePull(ctx context.Context, refStr string, options types.ImagePullOptions) (res io.ReadCloser, err error) {
	return
}

func (d *FakeDockerClient) VolumeRemove(ctx context.Context, volumeID string, force bool) (err error) {
	return
}

func (d *FakeDockerClient) VolumeCreate(ctx context.Context, options volumetypes.VolumeCreateBody) (res types.Volume, err error) {
	return
}

func (d *FakeDockerClient) VolumeInspect(ctx context.Context, volumeID string) (res types.Volume, err error) {
	return
}

func (d *FakeDockerClient) ImageList(ctx context.Context, options types.ImageListOptions) (res []types.ImageSummary, err error) {
	return
}

func (d *FakeDockerClient) ImageInspectWithRaw(ctx context.Context, imageID string) (res types.ImageInspect, b []byte, err error) {
	return
}
