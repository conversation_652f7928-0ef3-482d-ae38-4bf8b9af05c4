package hardware

import (
	"bytes"
	"encoding/json"
	"fmt"
	linuxproc "github.com/c9s/goprocinfo/linux"
	"github.com/pkg/errors"
	uuid "github.com/satori/go.uuid"
	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/host"
	log "github.com/sirupsen/logrus"
	"io"
	"os"
	"os/exec"
	"runtime"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/hardware/resource"
	"strconv"
	"strings"
	"sync"
	"time"
)

type ResourceType string

const (
	CPU    ResourceType = "cpu"
	Mem    ResourceType = "memory"
	Nvidia              = ResourceType(agent_constant.NvidiaGPUKey)
)

type SharedGPUUsedInfo struct {
	Shared bool `json:"shared"`
	// GB
	Total int `json:"total"`
	// GB
	Used int `json:"used"`
}

type ResourceSummary struct {
	HostInfo    *HostInfo                  `json:"host_info"`
	CpuInfo     *CpuInfo                   `json:"cpu_info"`
	GpuInfo     *GpuInfo                   `json:"gpu_info"`
	MemInfo     *MemInfo                   `json:"mem_info"`
	DiskInfo    []*agent_constant.DiskInfo `json:"disk_info"`
	VersionInfo *VersionInfo               `json:"version_info"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

func (rs *ResourceSummary) Parse(input string) error {
	return json.Unmarshal([]byte(input), &rs)
}

func (rs *ResourceSummary) ToString() string {
	tmp, _ := json.Marshal(rs)
	return string(tmp)
}

type GpuInfo struct {
	GPUS []GpuUnit `json:"gpus"`
}

type GpuUnit struct {
	Index             uint                   `json:"index"`
	UUID              string                 `json:"uuid"`
	GpuType           agent_constant.GPUType `json:"gpu_type"`
	DriverVersion     string                 `json:"driver_version"`
	Name              string                 `json:"name"`
	MemoryUsed        uint64                 `json:"memory_used"`
	MemoryTotal       uint64                 `json:"memory_total"`
	GpuUtilization    uint                   `json:"gpu_utilization"`
	MemoryUtilization uint                   `json:"mem_utilization"`
	PowerDraw         uint                   `json:"power_draw"`
	Temperature       uint                   `json:"temperature"`
	FanSpeed          uint                   `json:"fan_speed"`
	ChipCorp          string                 `json:"chip_corp"`
}

var nvmlFailedTimes int

func GetGpuResourcesSummary(gpuInterface resource.GpuInterface) (gpuInfo *GpuInfo, err error) {
	defer func() {
		if err != nil {
			return
			//err = errors.Wrap(err, fmt.Sprintf("kind of %s failed", gpuInterface.GpuKind()))
		}
		if rerr := recover(); rerr != nil {
			err = errors.Errorf("recover when get gpu resource info. %v", rerr)
			return
		}
	}()
	if err != nil {
		err = errors.Wrap(err, "Initialize() failed")
		return
	}
	defer gpuInterface.Shutdown()

	var driverVersion string
	driverVersion, err = gpuInterface.Initialize()
	if err != nil {
		err = errors.Wrap(err, "get driverVersion failed")
		return
	}

	var numDevices uint
	numDevices, err = gpuInterface.DeviceCount()
	if err != nil {
		err = errors.Wrap(err, "DeviceCount() failed")
		return
	}
	gpuInfo = &GpuInfo{GPUS: make([]GpuUnit, numDevices)}
	for i := 0; i < int(numDevices); i++ {
		gpuRes := GpuUnit{}
		var dev resource.GpuDevice
		dev, err = gpuInterface.GetDevice(uint(i))
		if err != nil {
			err = errors.Wrap(err, "DeviceHandleByIndex() failed")
			return
		}

		gpuRes.DriverVersion = driverVersion
		gpuRes.GpuType = gpuInterface.GpuKind()
		switch gpuRes.GpuType {
		case agent_constant.NvidiaGPUKey:
			gpuRes.ChipCorp = "nvidia"
		case agent_constant.AscendAclGPUKey:
			gpuRes.ChipCorp = "huawei"
		case agent_constant.CpuSetGPUKey:
			gpuRes.ChipCorp = "cpu"
		case agent_constant.CambriconGPUKey:
			gpuRes.ChipCorp = "cambricon"
		case agent_constant.MThreadsGPUKey:
			gpuRes.ChipCorp = "mthreads"
		}
		gpuRes.UUID, err = dev.UUID()
		if err != nil {
			if nvmlFailedTimes < 50 {
				nvmlFailedTimes++
				fmt.Println("dev.UUID(): ", err.Error())
			}
		}
		gpuRes.Index, _ = dev.MinorNumber()
		gpuRes.Name, _ = dev.Name()
		gpuRes.MemoryTotal, gpuRes.MemoryUsed, err = dev.MemoryInfo()
		if err != nil {
			if nvmlFailedTimes < 50 {
				nvmlFailedTimes++
				fmt.Println("dev.MemoryInfo(): ", err.Error())
			}
		}

		gpuRes.GpuUtilization, gpuRes.MemoryUtilization, err = dev.UtilizationRates()
		if err != nil {
			err = errors.Wrap(err, "dev.UtilizationRates() failed")
			return
		}

		gpuRes.PowerDraw, err = dev.PowerUsage()
		if err != nil {
			if nvmlFailedTimes < 50 {
				nvmlFailedTimes++
				fmt.Println("dev.PowerUsage(): ", err.Error())
			}
		}

		gpuRes.FanSpeed, _ = dev.FanSpeed()
		gpuRes.Temperature, err = dev.Temperature()
		if err != nil {
			if nvmlFailedTimes < 50 {
				nvmlFailedTimes++
				fmt.Println("dev.Temperature() failed", err.Error())
			}
		}

		gpuInfo.GPUS[i] = gpuRes
	}
	return
}

type CpuInfo struct {
	Count             int    `json:"count"`
	CpuName           string `json:"cpu_name"`
	CpuBasicFrequency string `json:"cpu_basic_frequency"`
	linuxproc.LoadAvg
	CpuArch string `json:"cpu_arch"` //cpu架构
}

func GetCpuInfo() (cpuInfo *CpuInfo, err error) {
	var loadInfo *linuxproc.LoadAvg
	cpuInfo = &CpuInfo{Count: runtime.NumCPU()}
	loadInfo, err = linuxproc.ReadLoadAvg("/proc/loadavg")
	if err != nil {
		return
	}
	cpuInfo.LoadAvg = *loadInfo
	cpuPrams, err := cpu.Info()
	if err != nil {
		log.WithField("err", err).Error("get cpu info failed")
		return
	}
	cpuInfo.Count = len(cpuPrams)
	cpuInfo.CpuName = cpuPrams[0].ModelName
	cpuInfo.CpuBasicFrequency = strconv.FormatFloat(cpuPrams[0].Mhz/1000, 'f', 2, 64) + "GHz"
	//获取cpu架构
	cpuInfo.CpuArch = GetCpuArch()

	return
}

func GetCpuInfoHuawei() (cpuInfo *CpuInfo, err error) {
	var loadInfo *linuxproc.LoadAvg
	cpuInfo = &CpuInfo{Count: runtime.NumCPU()}
	loadInfo, err = linuxproc.ReadLoadAvg("/proc/loadavg")
	if err != nil {
		return
	}
	cpuInfo.LoadAvg = *loadInfo

	cmd := exec.Command("lscpu")
	var out bytes.Buffer
	cmd.Stdout = &out
	err = cmd.Run()
	if err != nil {
		fmt.Printf("执行命令失败: %v\n", err)
		return
	}

	lines := strings.Split(out.String(), "\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			if key == "CPU(s)" {
				cpuInfo.Count, _ = strconv.Atoi(value)
			}
			if key == "Model name" {
				cpuInfo.CpuName = value
			}
			if key == "CPU MHz" {
				v, _ := strconv.Atoi(value)
				cpuInfo.CpuBasicFrequency = strconv.FormatFloat(float64(v)/1000, 'f', 2, 64) + "GHz"
			}
		}
	}
	//获取cpu架构
	cpuInfo.CpuArch = GetCpuArch()
	return
}

func GetCpuArch() (cpuArch string) {
	arch := runtime.GOARCH
	switch arch {
	case "amd64", "386":
		cpuArch = "x86"
	case "arm64", "arm":
		cpuArch = "arm"
	}
	return
}

type MemInfo struct {
	MemTotal     uint64 `json:"mem_total"`     // 单位为byte
	MemFree      uint64 `json:"mem_free"`      // 单位为byte
	MemAvailable uint64 `json:"mem_available"` // 单位为byte
}

func GetMemInfo() (memInfo *MemInfo, err error) {
	var readMem *linuxproc.MemInfo
	memInfo = &MemInfo{}
	readMem, err = linuxproc.ReadMemInfo("/proc/meminfo")
	if err != nil {
		return
	}
	memInfo.MemTotal = readMem.MemTotal * uint64(agent_constant.KB)
	memInfo.MemFree = readMem.MemFree * uint64(agent_constant.KB)
	memInfo.MemAvailable = readMem.MemAvailable * uint64(agent_constant.KB)
	return
}

func GetDiskInfo() (diskInfo []*agent_constant.DiskInfo, err error) {
	var readDisk *linuxproc.Mounts
	diskInfo = []*agent_constant.DiskInfo{}
	readDisk, err = linuxproc.ReadMounts("/proc/mounts")
	if err != nil {
		return
	}
	for _, m := range readDisk.Mounts {
		if m.MountPoint != agent_constant.GetStorageRootPath() {
			continue
		}
		f, err := agent_constant.NewDf(m.MountPoint)
		if err != nil {
			continue
		}
		diskInfo = append(diskInfo, &agent_constant.DiskInfo{
			NFSMount:      m.Device,
			DiskTotal:     uint64(f.Total(agent_constant.B)), // 单位为为byte
			DiskUsed:      uint64(f.Used(agent_constant.B)),  // 单位为为byte
			DiskAvailable: uint64(f.Avail(agent_constant.B)), // 单位为为byte
		})
	}
	return
}

type HostInfo struct {
	MachineID     string `json:"machine_id"`
	MachineName   string `json:"machine_name"`
	OsName        string `json:"os_name"`
	IP            string `json:"ip"`
	KernelVersion string `json:"kernel_version"`
	DockerVersion string `json:"docker_version"`
}

func GetHostInfo() (hostInfo *HostInfo, err error) {
	hostInfo = &HostInfo{}
	info, err := host.Info()
	if err != nil {
		log.WithField("err", err).Error("get host info failed")
		return
	}
	hostInfo.MachineID, err = getMachineID()
	if err != nil {
		return
	}
	hostInfo.MachineName = info.Hostname
	hostInfo.OsName = info.Platform + info.PlatformVersion
	hostInfo.KernelVersion = info.KernelVersion

	// 这里获取成功与否不影响实际使用, 并且发生过无预料的异常错误
	// time="2021-08-27T15:43:51+08:00" level=error msg="get machine ip address failed" err="route ip+net: no such network interface"
	// 为这等没有实际影响的err panic重启是一种浪费, 所以忽略此error
	ip := GetMachineIntranetIP()
	if ip == "" {
		ip, _ = getClientIp()
	}

	hostInfo.IP = ip
	return
}

var nvidiaSmiLock sync.RWMutex
var nvidiaSmi string

func setNvidiaSmi(s string) {
	nvidiaSmiLock.Lock()
	defer nvidiaSmiLock.Unlock()
	nvidiaSmi = s
}

type VersionInfo struct {
	CudaVersion   string `json:"cuda_version"`
	DriverVersion string `json:"driver_version"`
}

var detectVersionIsFinish bool
var detectVersionResult *VersionInfo

func GetVersionInfo() (versionInfo *VersionInfo, err error) {
	defer func() {
		detectVersionIsFinish = true
		detectVersionResult = versionInfo
	}()

	if detectVersionIsFinish {
		return detectVersionResult, nil
	}
	versionInfo = &VersionInfo{}
	var stdout bytes.Buffer

	nvidiaSmiCMD := []string{
		"/usr/bin/nvidia-smi",
		"/usr/lib/wsl/lib/nvidia-smi",
		"nvidia-smi",
	}

	errStr := ""
	for _, nvidiaSmiStr := range nvidiaSmiCMD {
		cmd := exec.Command("sh", "-c", fmt.Sprintf("%s -q | grep Version", nvidiaSmiStr))
		cmd.Stdout = &stdout
		err = cmd.Run()
		if err != nil {
			errStr = errStr + err.Error() + ";"
		} else {
			setNvidiaSmi(nvidiaSmiStr)
			break
		}
	}
	if err != nil {
		log.WithField("err", err).Warn("all nvidia-smi exec failed, cmd list: %s", errStr)
		return
	}

	// 获取命令输出并解析驱动版本和CUDA版本
	for {
		line, err := stdout.ReadString('\n')
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}

			log.WithField("err", err).Error(err, "nvidia-smi stdout readline failed")
			return nil, err
		}

		if strings.Contains(line, "Driver Version") {
			i := strings.Index(line, ":")
			if i < 0 {
				continue
			}
			versionInfo.DriverVersion = strings.TrimSpace(line[i+1:])
		}
		if strings.Contains(line, "CUDA Version") {
			i := strings.Index(line, ":")
			if i < 0 {
				continue
			}
			versionInfo.CudaVersion = strings.TrimSpace(line[i+1:])
		}
	}

	return
}

func getMachineID() (string, error) {
	_, err := os.Stat(agent_constant.GetMachineUUIDPath())
	if err == nil {
		// As of Go 1.16, this function simply calls os.ReadFile.
		content, err := os.ReadFile(agent_constant.GetMachineUUIDPath())
		if err != nil {
			log.WithField("err", err).Error("read machine_id failed")
			return "", err
		}
		if len(content) != agent_constant.MachineIDLength {
			err = os.Remove(agent_constant.GetMachineUUIDPath())
			if err != nil {
				log.WithField("err", err).Error("remove machine_id file failed")
				return "", err
			}
			return "", errors.New("machine_id length not right")
		}
		return string(content), nil

	} else if os.IsNotExist(err) {
		str := uuid.NewV4().String() //当前时间戳+Mac地址组合
		strs := strings.Split(str, "-")
		machineID := ""
		for _, c := range strs {
			machineID += c[:2]
		}
		err = os.WriteFile(agent_constant.GetMachineUUIDPath(), []byte(machineID), os.ModePerm)
		if err != nil {
			log.WithField("err", err).Error("write machine_id failed")
			return "", err
		}
		return machineID, nil
	} else {
		log.WithField("err", err).Error("os get machine_id file stat failed")
		return "", err
	}
}

func GetMachineIntranetIP() string {
	_, err := os.Stat(agent_constant.GetMachineIntranetIPPath())
	if err != nil {
		if !os.IsNotExist(err) {
			log.WithField("err", err).Error("get machine intranet ip failed")
		}
		return ""
	}

	content, err := os.ReadFile(agent_constant.GetMachineIntranetIPPath())
	if err != nil {
		log.WithField("err", err).Error("read private_ip failed")
		return ""
	}

	if len(strings.Split(string(content), ".")) != 4 {
		log.WithField("ip", string(content)).Error("private ip format error")
		return ""
	}
	return strings.TrimSpace(string(content))
}
