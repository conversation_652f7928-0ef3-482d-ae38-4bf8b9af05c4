package cambricon

import (
	"github.com/pkg/errors"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/hardware/resource"
	"server/pkg-agent/hardware/resource/cambricon/cndev"
)

type GpuCambriconDriveInfo struct {
}

func (cambriconDrive GpuCambriconDriveInfo) IsLibraryNotFound(err error) bool {
	if err == nil {
		return false
	}
	return errors.Is(err, cndev.ErrLibraryNotLoaded)
}

func (cambriconDrive GpuCambriconDriveInfo) GpuKind() (kind agent_constant.GPUType) {
	return agent_constant.CambriconGPUKey
}

func (cambriconDrive GpuCambriconDriveInfo) Initialize() (driverVersion string, err error) {
	err = cndev.Init()
	if err != nil {
		return
	}
	return cndev.DriverVersion(), err
}
func (cambriconDrive GpuCambriconDriveInfo) DeviceCount() (deviceCount uint, err error) {
	return cndev.GetDeviceCount()
}
func (cambriconDrive GpuCambriconDriveInfo) GetDevice(deviceID uint) (device resource.GpuDevice, err error) {
	var d *cndev.Device
	d, err = cndev.NewDeviceLite(deviceID)
	if err != nil {
		return
	}
	device = &GpuCambriconDevice{d}
	return
}
func (cambriconDrive GpuCambriconDriveInfo) Shutdown() (err error) {
	return cndev.Shutdown()
}

type GpuCambriconDevice struct {
	device *cndev.Device
}

func (cambriconDrive *GpuCambriconDevice) UUID() (string, error) {
	return cambriconDrive.device.UUID()
}
func (cambriconDrive *GpuCambriconDevice) Name() (string, error) {
	return cambriconDrive.device.Name()
}
func (cambriconDrive *GpuCambriconDevice) MinorNumber() (uint, error) {
	return cambriconDrive.device.MinorNumber()
}
func (cambriconDrive *GpuCambriconDevice) MemoryInfo() (uint64, uint64, error) {
	total, used, err := cambriconDrive.device.MemoryInfo()
	return uint64(total), uint64(used), err
}
func (cambriconDrive *GpuCambriconDevice) UtilizationRates() (uint, uint, error) {
	return cambriconDrive.device.UtilizationRates()
}
func (cambriconDrive *GpuCambriconDevice) PowerUsage() (uint, error) {
	return cambriconDrive.device.PowerUsage()
}
func (cambriconDrive *GpuCambriconDevice) FanSpeed() (uint, error) {
	return cambriconDrive.device.FanSpeed()
}
func (cambriconDrive *GpuCambriconDevice) Temperature() (uint, error) {
	return cambriconDrive.device.Temperature()
}
