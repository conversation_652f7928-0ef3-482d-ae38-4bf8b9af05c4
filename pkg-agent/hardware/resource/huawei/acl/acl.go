package acl

import "C"
import (
	"bytes"
	"fmt"
	log "github.com/sirupsen/logrus"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
)

type Device struct {
	handle

	uuid string
	path string
}

func execCommand(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		//log.Errorf("execCommand cmd.Run() failed with %s, err out: %s\n", err, stderr.String())
		return "", err
	}
	return out.String(), nil
}

func Init() error {
	return InitAcl("")
}

func Shutdown() error {
	return Finalize()
}

func DriverVersion() (version string, err error) {
	// 获取基础信息
	boardInfo, err := execCommand("npu-smi", "info")
	if err != nil {
		//log.Errorf("DriverVersion 执行npu-smi info获取基础信息失败：%v, boardInfo: %v\n", err, boardInfo)
		err = ErrLibraryNotLoaded
		return "", err
	}

	lines := strings.Split(boardInfo, "\n")
	var driverVersion string

	for _, line := range lines {
		if strings.Contains(line, "Version:") {
			fields := strings.Fields(line)
			driverVersion = fields[len(fields)-2]
			break
		}
	}
	if driverVersion == "" {
		//log.Errorf("DriverVersion 未找到驱动版本信息")
		err = ErrLibraryNotLoaded
		return "", err
	}

	//log.Infof("DriverVersion 当前NPU驱动版本：%s\n", driverVersion)
	return driverVersion, nil
}

func GetDeviceCount() (uint, error) {
	// 获取卡信息
	outputInfo, err := execCommand("npu-smi", "info", "-l")
	if err != nil {
		log.Errorf("GetDeviceCount 执行npu-smi info -l获取卡信息失败：%v\n", err)
		return 0, fmt.Errorf("执行npu-smi info -l获取卡信息失败：%v\n", err)
	}
	lines := strings.Split(outputInfo, "\n")
	var (
		deviceCount int
	)

	for _, line := range lines {
		// 查找包含Total Count标识的行（示例："Total Count: 8"）
		re := regexp.MustCompile(`Total Count\s+:\s*(\d+)`)
		matches := re.FindStringSubmatch(line)
		if len(matches) >= 2 {
			deviceCount, err = strconv.Atoi(matches[1])
			if err != nil {
				log.Errorf("GetDeviceCount 转换失败, err: %v", err)
				return 0, fmt.Errorf("字符串转整型失败, err: %v", err)
			}
			break
		}
	}

	log.Infof("GetDeviceCount 可用NPU设备数量是：%d\n", deviceCount)
	return uint(deviceCount), nil
}

type NPUInfo struct {
	NPUID string // NPU ID
	Name  string // 产品名称
	UUID  string // 序列号（作为UUID）
	Path  string // PCIe总线路径
}

func NewDeviceLite(idx uint) (device *Device, err error) {
	// 执行 npu-smi info -t board -i idx 命令获取卡信息
	outputInfo, err := execCommand("npu-smi", "info", "-t", "board", "-i", strconv.Itoa(int(idx)))
	if err != nil {
		log.Errorf("NewDeviceLite 执行npu-smi info -t board -i %v 获取卡信息失败, err：%v\n", idx, err)
		return nil, fmt.Errorf("执行npu-smi info -t board -i %v 获取卡信息失败, err：%v\n", idx, err)
	}
	lines := strings.Split(outputInfo, "\n")

	info := &NPUInfo{NPUID: strconv.Itoa(int(idx))}
	for _, line := range lines {
		parts := strings.SplitN(line, ":", 2)
		if len(parts) < 2 {
			continue
		}
		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		switch key {
		case "Serial Number":
			info.UUID = value
		case "PCIe Bus Info":
			// 转换PCIe路径格式：0000:C1:00.0 → /sys/bus/pci/devices/0000:c1:00.0
			pciPath := strings.Replace(value, ":", "", 1)
			info.Path = fmt.Sprintf("/sys/bus/pci/devices/%s", strings.ToLower(pciPath))
		}
	}

	//获取chip_name
	outputInfo, err = execCommand("npu-smi", "info", "-m")
	if err != nil {
		log.Errorf("NewDeviceLite 执行npu-smi info -m 芯片的映射关系信息失败, err：%v\n", err)
		return nil, fmt.Errorf("执行npu-smi info -m 芯片的映射关系信息失败, err：%v\n", err)
	}
	lines = strings.Split(outputInfo, "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) < 5 {
			continue
		}
		if fields[0] == strconv.Itoa(int(idx)) {
			info.Name = fields[len(fields)-1] //获取chip_name: 910B2
			break
		}
	}
	h := handle{
		CardName: info.Name,
		UUID:     info.UUID,
		MINOR:    idx,
		PATH:     info.Path,
	}

	device = nil

	device = &Device{
		handle: h,
		uuid:   h.UUID,
		path:   h.PATH,
	}
	log.Infof("NewDeviceLite device info : %v\n", device)
	return
}

func (d *Device) MinorNumber() (uint, error) {
	return d.MINOR, nil
}

func (d *Device) Name() (string, error) {
	return d.CardName, nil
}

func (d *Device) UUID() (string, error) {
	return d.uuid, nil
}

func (d *Device) MemoryInfo() (uint, uint, error) {
	totalMem, usedMem, err := getNPUMemory(strconv.Itoa(int(d.MINOR)))
	if err != nil {
		log.Errorf("MemoryInfo getNPUMemory err: %v\n", err)
		return 0, 0, fmt.Errorf("npu id: %v, getNPUMemory err: %v\n", d.MINOR, err)
	}
	log.Infof("MemoryInfo NPU %d - 内存总量: %d MB, 已使用: %d MB\n", d.MINOR, totalMem, usedMem)
	return uint(totalMem) * 1024 * 1024, uint(usedMem) * 1024 * 1024, nil
}

func getNPUMemory(npuID string) (uint64, uint64, error) {
	// 获取总内存容量
	total, err := getTotalMemory(npuID)
	if err != nil {
		log.Errorf("getNPUMemory err: %v, total: %v\n", err, total)
		return 0, 0, fmt.Errorf("npu id: %v, getTotalMemory err: %v\n", npuID, err)
	}
	// 获取已使用内存
	used, err := getUsedMemory(npuID)
	if err != nil {
		log.Errorf("getNPUMemory err: %v, used: %v\n", err, used)
		return 0, 0, fmt.Errorf("npu id: %v, getUsedMemory err: %v\n", npuID, err)
	}

	log.Infof("getNPUMemory total: %v, used: %v\n", total, used)
	return total, used, nil
}

func getTotalMemory(npuID string) (uint64, error) {
	// 获取芯片数量
	chipCount, err := getChipCount(npuID)
	if err != nil {
		log.Errorf("getTotalMemory getChipCount err: %v, chipCount: %v\n", err, chipCount)
		return 0, err
	}

	var totalMem uint64
	for chipID := 0; chipID < chipCount; chipID++ {
		// 执行内存查询命令
		output, err := execCommand("npu-smi", "info", "-t", "memory", "-i", npuID, "-c", strconv.Itoa(chipID))
		if err != nil {
			log.Errorf("getTotalMemory 命令npu-smi info -t memory -i %v -c %v 执行失败, err: %v, 输出: %s \n", npuID, chipID, err, output)
			return 0, fmt.Errorf("命令npu-smi info -t memory -i %v -c %v 执行失败, err: %v, 输出: %s \n", npuID, chipID, err, output)
		}

		// 解析 HBM 容量
		lines := strings.Split(output, "\n")
		for _, line := range lines {
			// 查找包含HBM Capacity(MB) :标识的行（示例："HBM Capacity(MB) : 65536"）
			switch {
			case strings.Contains(line, "HBM Capacity"):
				fields := strings.Fields(line)
				m := fields[len(fields)-1]
				mem, _ := strconv.ParseUint(m, 10, 64)
				totalMem += mem
			}
		}
	}
	log.Infof("getTotalMemory total: %v, chipCount: %v\n", totalMem, chipCount)
	return totalMem, nil
}

func getUsedMemory(npuID string) (uint64, error) {
	// 执行进程内存查询命令
	output, err := execCommand("npu-smi", "info", "-t", "proc-mem", "-i", npuID)
	if err != nil {
		log.Errorf("getUsedMemory err, 命令npu-smi info -t proc-mem -i %v 执行失败, err: %v, 输出: %s\n", npuID, err, output)
		return 0, fmt.Errorf("命令npu-smi info -t proc-mem -i %v 执行失败, err: %v, 输出: %s\n", npuID, err, output)
	}

	// 解析进程内存使用量
	re := regexp.MustCompile(`Process memory$MB$:\s+(\d+)`)
	matches := re.FindAllStringSubmatch(output, -1)

	var usedMem uint64
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}
		mem, _ := strconv.ParseUint(match[1], 10, 64)
		usedMem += mem
	}
	log.Infof("getUsedMemory used info: %v\n", usedMem)
	return usedMem, nil
}

func getChipCount(npuID string) (int, error) {
	// 获取芯片数量
	output, err := execCommand("npu-smi", "info", "-t", "proc-mem", "-i", npuID)
	if err != nil {
		log.Errorf("getChipCount 执行命令npu-smi info -t proc-mem -i %v 失败, err: %v, 输出: %s\n", npuID, err, output)
		return 0, fmt.Errorf("执行命令npu-smi info -t proc-mem -i %v 失败, err: %v, 输出: %s\n", npuID, err, output)
	}

	re := regexp.MustCompile(`Chip Count\s+:\s+(\d+)`)
	matches := re.FindStringSubmatch(output)
	if len(matches) < 2 {
		log.Errorf("无法解析芯片数量, matches: %v\n", matches)
		return 0, fmt.Errorf("无法解析芯片数量")
	}
	log.Infof("getChipCount chip info: %v\n", matches[1])
	return strconv.Atoi(matches[1])
}

func (d *Device) UtilizationRates() (uint, uint, error) {
	return getUtilizationRates(int(d.MINOR))
}

func getUtilizationRates(minor int) (uint, uint, error) {
	var (
		npuUtilization uint = 0
		memUtilization uint = 0
	)
	// 执行基本情况查询命令
	output, err := execCommand("npu-smi", "info", "-t", "common", "-i", strconv.Itoa(minor))
	if err != nil {
		log.Errorf("getUtilizationRates 命令npu-smi info -t common -i %v 执行失败, err: %v, 输出: %s \n", minor, err, output)
		return 0, 0, fmt.Errorf("命令npu-smi info -t common -i %v 执行失败, err: %v, 输出: %s \n", minor, err, output)
	}

	// 解析 HBM 容量
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		// 查找包含HBM Capacity(MB) :标识的行（示例："HBM Capacity(MB) : 65536"）
		switch {
		case strings.Contains(line, "HBM Usage Rate"):
			fields := strings.Fields(line)
			hbmUsage := fields[len(fields)-1]
			hbmU, _ := strconv.ParseUint(hbmUsage, 10, 64)
			npuUtilization = uint(hbmU)
		case strings.Contains(line, "Memory Usage Rate"):
			fields := strings.Fields(line)
			mUsage := fields[len(fields)-1]
			mU, _ := strconv.ParseUint(mUsage, 10, 64)
			memUtilization = uint(mU)
		}
	}
	return npuUtilization, memUtilization, nil
}

func (d *Device) PowerUsage() (uint, error) {
	output, err := execCommand("npu-smi", "info", "-t", "power", "-i", strconv.Itoa(int(d.MINOR)))
	if err != nil {
		log.Errorf("PowerUsage 执行命令npu-smi info -t power -i %v 失败, err: %v\n输出: %s", d.MINOR, err, output)
		return 0, fmt.Errorf("执行命令npu-smi info -t power -i %v 失败, err: %v\n输出: %s", d.MINOR, err, output)
	}
	// 解析功率信息
	var power float64
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		// 查找包含HBM Capacity(MB) :标识的行（示例："HBM Capacity(MB) : 65536"）
		switch {
		case strings.Contains(line, "NPU Real-time Power"):
			fields := strings.Fields(line)
			p := fields[len(fields)-1]
			power, _ = strconv.ParseFloat(p, 64)
		}
	}

	return uint(power), nil
}

func (d *Device) FanSpeed() (uint, error) {
	return 0, nil
}

func (d *Device) Temperature() (temp uint, err error) {
	output, err := execCommand("npu-smi", "info", "-t", "temp", "-i", strconv.Itoa(int(d.MINOR)))
	if err != nil {
		log.Errorf("Temperature 执行命令npu-smi info -t temp -i %v 失败, err: %v, 输出: %s", d.MINOR, err, output)
		return 0, fmt.Errorf("执行命令npu-smi info -t temp -i %v 失败, err: %v, 输出: %s", d.MINOR, err, output)
	}
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		switch {
		case strings.Contains(line, "NPU Temperature"):
			fields := strings.Fields(line)
			t := fields[len(fields)-1]
			parse, _ := strconv.ParseUint(t, 10, 64)
			temp = uint(parse)
		}
	}

	return temp, nil
}
