package acl

/*
   #cgo CXXFLAGS: -I ./
   #cgo LDFLAGS: -ldl -Wl,--unresolved-symbols=ignore-in-object-files
   #include "acl_wrapper.h"
*/
import "C"
import (
	"fmt"
)

var ErrLibraryNotLoaded = fmt.<PERSON><PERSON><PERSON>("could not found huawei npu")

type handle struct {
	CardName string
	UUID     string
	MINOR    uint
	PATH     string
}

// InitAcl 初始化 ACL 环境
func InitAcl(configPath string) error {
	cConfig := C.CString(configPath)

	ret := C.acl_init_wrapper(cConfig)
	if ret != 0 {
		return fmt.E<PERSON><PERSON>("ACL init failed, code: %v", ret)
	}
	return nil
}

// Finalize 释放资源
func Finalize() error {
	ret := C.acl_finalize_wrapper()
	if ret != 0 {
		return fmt.Errorf("ACL finalize failed, code: %d", ret)
	}
	return nil
}
