package huawei

import (
	"github.com/pkg/errors"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/hardware/resource"
	"server/pkg-agent/hardware/resource/huawei/acl"
)

type NpuHuaweiDriveInfo struct {
}

func (huaweiDrive NpuHuaweiDriveInfo) IsLibraryNotFound(err error) bool {
	if err == nil {
		return false
	}
	return errors.Is(err, acl.ErrLibraryNotLoaded)
}

func (huaweiDrive NpuHuaweiDriveInfo) GpuKind() (kind agent_constant.GPUType) {
	return agent_constant.AscendAclGPUKey
}

func (huaweiDrive NpuHuaweiDriveInfo) Initialize() (driverVersion string, err error) {
	return acl.DriverVersion()
}

func (huaweiDrive NpuHuaweiDriveInfo) DeviceCount() (deviceCount uint, err error) {
	return acl.GetDeviceCount()
}

func (huaweiDrive NpuHuaweiDriveInfo) GetDevice(deviceID uint) (device resource.GpuDevice, err error) {
	var d *acl.Device
	d, err = acl.NewDeviceLite(deviceID)
	if err != nil {
		return
	}
	device = &NpuHuaweiDevice{d}
	return
}
func (huaweiDrive NpuHuaweiDriveInfo) Shutdown() (err error) {
	return nil
}

type NpuHuaweiDevice struct {
	device *acl.Device
}

func (h NpuHuaweiDevice) UUID() (string, error) {
	return h.device.UUID()

}

func (h NpuHuaweiDevice) Name() (string, error) {
	return h.device.Name()

}

func (h NpuHuaweiDevice) MinorNumber() (uint, error) {
	return h.device.MinorNumber()

}

func (h NpuHuaweiDevice) MemoryInfo() (uint64, uint64, error) {
	total, used, err := h.device.MemoryInfo()
	return uint64(total), uint64(used), err
}

func (h NpuHuaweiDevice) UtilizationRates() (uint, uint, error) {
	return h.device.UtilizationRates()
}

func (h NpuHuaweiDevice) PowerUsage() (uint, error) {
	return h.device.PowerUsage()
}

func (h NpuHuaweiDevice) FanSpeed() (uint, error) {
	return h.device.FanSpeed()
}

func (h NpuHuaweiDevice) Temperature() (uint, error) {
	return h.device.Temperature()
}
