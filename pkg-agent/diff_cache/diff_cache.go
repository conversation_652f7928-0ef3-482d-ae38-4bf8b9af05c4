package diff_cache

import (
	"context"
	"fmt"
	"github.com/levigross/grequests"
	"github.com/shirou/gopsutil/disk"
	"io"
	mathRand "math/rand"
	"net/http"
	"server/pkg-agent/agent_constant"
	"server/pkg-agent/file_transfer"
	"server/pkg/logger"
	"strconv"
	"time"
)

type DiffCacheGetParams struct {
	FileName        string
	NotAvailableMap map[string]struct{}
}

func DiffCacheGet(log *logger.Logger, params *DiffCacheGetParams) (cacheFileOK bool, reader io.Reader, size int64, intranetIP string, needCache bool) {
	needCache = true
	if cli == nil || localIntranetIP == "" {
		return
	}

	var (
		err             error
		intranetList    []string
		l               = log.WithField("fileName", params.FileName)
		needCopyNumbers = diffCacheRequirementsRecord(l, params.FileName, len(params.NotAvailableMap) == 0)
	)

	// 一次性全取出来
	intranetList, err = cli.SMembers(context.Background(), params.FileName).Result()
	if err != nil {
		l.ErrorE(err, "get file cache info from redis failed")
		return
	}

	// 循环
	for {
		intranetIP = ""
		needCache = true
		// 清除不可用的项
		for k, v := range intranetList {
			if _, ok := params.NotAvailableMap[v]; ok {
				intranetList = append(intranetList[:k], intranetList[k+1:]...)
			}
		}

		// 找出本次可用的intranetIP
		if len(intranetList) == 0 {
			// 一个都没有
			return
		} else if len(intranetList) == 1 {
			intranetIP = intranetList[0]
		} else {
			mathRand.Seed(time.Now().UnixNano())
			intranetIP = intranetList[mathRand.Intn(len(intranetList))]
		}

		if len(intranetList) >= needCopyNumbers {
			needCache = false
		}

		l.Info("find file cache from redis, intranetIP: [%s], try to get it now....", intranetIP)

		// 有缓存,看是否可用
		url := fmt.Sprintf("http://%s:%d/api/v1/diff_cache", intranetIP, file_transfer.DefaultAPIPort)
		res, err := grequests.Post(url, &grequests.RequestOptions{
			Headers: map[string]string{agent_constant.HeaderAuthName: agent_constant.HeaderAuthContent},
			JSON:    map[string]interface{}{"file_name": params.FileName},
		})
		if err != nil {
			// 此处是请求错误, 无法确定给定ip的机器内缓存是否删除, 故暂时跳过
			// 更新，此处出错一般是因为目标机器下架了，直接删除吧
			l.ErrorE(err, "get cache file failed: http request failed, intranetIP: [%s]", intranetIP)
			params.NotAvailableMap[intranetIP] = struct{}{}
			deleteCacheRecord(log, intranetIP, params.FileName)
			continue
		}

		// 404 说明diff文件不存在, 这里承担纠错, 把这个错误信息从redis中删除
		if res.StatusCode == http.StatusNotFound {
			l.Info("code 404 with error from intranetIP [%s]:[%s], delete this record from redis...", intranetIP, res.String())
			params.NotAvailableMap[intranetIP] = struct{}{}
			deleteCacheRecord(log, intranetIP, params.FileName)
			continue
		}

		// 200 diff file is ok
		if res.StatusCode == http.StatusOK {
			sizeStr := res.Header.Get(agent_constant.HeaderFileSize)
			size, _ = strconv.ParseInt(sizeStr, 10, 64)
			reader = res
			cacheFileOK = true
			l.Info("find oss file cache from [%s], size: [%d]", intranetIP, size)
			return
		}

		// other http code
		l.Info("code [%d] with error from intranetIP [%s]:[%s]", res.StatusCode, intranetIP, res.String())
		params.NotAvailableMap[intranetIP] = struct{}{}
		continue
	}
}

func DiffNeedCache(l *logger.Logger) bool {
	usageStat, err := disk.Usage("/data")
	if err != nil {
		l.WarnE(err, "disk get /data usage failed")
		return true
	}

	if usageStat.UsedPercent < 93 {
		l.Info("/data usage is less than 93 percent and requires diff cache.")
		return true
	}

	l.Warn("/data usage is over 93 percent,no diff cache is required.")
	return false
}
