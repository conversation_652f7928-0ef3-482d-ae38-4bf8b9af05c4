package agent_guard

import (
	"context"
	"fmt"
	goset "github.com/deckarep/golang-set/v2"
	agentConstant "server/pkg-agent/agent_constant"
	"server/pkg/libs"
	"server/pkg/logger"
	"strings"
	"sync"
	"time"
)

type GpuOccupy struct {
	region      string
	machineID   string
	machineName string

	startDelay                 sync.Map
	containerRunningFromServer sync.Map

	occupyMap   map[string]map[string]struct{}
	occupyMapMu sync.Mutex

	// 用于存储，从开始关机，到关机完成方法返回其间的容器，防止定时任务把运行中状态加回来
	stopUnsetButNotFinished sync.Map

	getContainerStatus func(agentConstant.ContainerID) (agentConstant.ContainerStatus, error)
	l                  *logger.Logger
}

func NewGpuOccupy(region, machineID, machineName string, getContainerStatus func(agentConstant.ContainerID) (agentConstant.ContainerStatus, error)) *GpuOccupy {
	return &GpuOccupy{
		region:                     region,
		machineID:                  machineID,
		machineName:                machineName,
		startDelay:                 sync.Map{},
		occupyMap:                  map[string]map[string]struct{}{},
		occupyMapMu:                sync.Mutex{},
		getContainerStatus:         getContainerStatus,
		containerRunningFromServer: sync.Map{},
		l:                          logger.NewLogger("gpu_occupy"),
	}
}

func (g *GpuOccupy) StartSet(l *logger.Logger, containerID string, gpuUUIDList []string) {
	ctx, cancel := context.WithCancel(context.Background())
	g.startDelay.Store(containerID, cancel)

	g.l.Info("startSet[1]: containerID: %s, gpuUUIDList:%+v", containerID, gpuUUIDList)
	g.startSet(ctx, l, containerID, gpuUUIDList)
}

// 只在start调用，也只在此方法内部上报飞书消息
func (g *GpuOccupy) startSet(ctx context.Context, l *logger.Logger, containerID string, gpuUUIDList []string) {
	g.containerRunningFromServer.Store(containerID, true)

	// 延迟的含义：old关机接到关机命令，到真正完成关机期间，new已经运行起来，并且检测到old还在运行（响应关机中），会导致误报。
	//  new开机后，延迟10s检测gpu占用，就是处理这种情况
	t := time.NewTicker(time.Second * 10)

	select {
	case <-ctx.Done():
		g.l.Info("startSet[1.1]: %s startSet, but context canceled, return now...", containerID)
		return
	case <-t.C:
	}

	// 出现了一个异常情况，在unset时，cancel没有生效。也就是容器已经停止了，但是走到了这里
	// 此处再加一层判断
	_, ok := g.containerRunningFromServer.Load(containerID)
	if !ok {
		g.l.Info("startSet[1.2]: %s startSet, but load containerRunningFromServer failed, return now...", containerID)
		return
	}

	g.l.Info("startSet[2] after delay: containerID: %s, gpuUUIDList:%+v", containerID, gpuUUIDList)
	oldSet := goset.NewSet[string]()
	g.occupyMapMu.Lock()

	for _, gpuUUID := range gpuUUIDList {
		if _, ok := g.occupyMap[gpuUUID]; !ok {
			g.occupyMap[gpuUUID] = map[string]struct{}{}
		}

		for oldContainer, _ := range g.occupyMap[gpuUUID] {
			if oldContainer == containerID {
				continue
			}
			oldSet.Add(oldContainer)
			g.l.Info("startSet[3]: gpuUUID: %s, newContainer:%s found conflict:%s", gpuUUID, containerID, oldContainer)
		}
		g.l.Info("startSet[4]: gpu:%s set container:%s", gpuUUID, containerID)
		g.occupyMap[gpuUUID][containerID] = struct{}{}
	}
	g.occupyMapMu.Unlock()

	oldContainerList := oldSet.ToSlice()
	if len(oldContainerList) != 0 {
		g.l.Info("startSet[5]: found oldContainerList: %v", oldContainerList)
		for _, cid := range oldContainerList {
			var containerStatus agentConstant.ContainerStatus
			err := libs.Retry(libs.RetryParams{
				Do: func() error {
					var err error
					containerStatus, err = g.getContainerStatus(agentConstant.NewContainerID(cid))
					if err != nil {
						return err
					}
					return nil
				},
				RetryInterval: time.Second,
				RetryTimes:    3,
				Name:          "startSet[6]",
				L:             g.l,
			})
			if err != nil {
				g.l.Info("startSet[6.1]: get container [%s] status err:%v ... unset by hand now...", cid, err)
				g.unset(cid, true, false, false)
				continue
			}

			g.l.Info("startSet[7]: containerID: %s, containerStatus:%+v", cid, containerStatus.Status.Status)
			if containerStatus.Status.Status != agentConstant.Running {
				g.unset(cid, true, false, false)
			}
		}

		g.conflictRepeatCheck(ctx, l, containerID, gpuUUIDList)
	}
}

func (g *GpuOccupy) conflictRepeatCheck(ctx context.Context, l *logger.Logger, containerID string, gpuUUIDList []string) {
	select {
	case <-ctx.Done():
		return
	default:
	}

	g.l.Info("startSet[9]: newContainer:%s, found conflict before, check again", containerID)

	for _, gpuUUID := range gpuUUIDList {
		msg := g.conflictRepeatCheckGetMsg(containerID, gpuUUID)
		if msg != "" {
			msg = strings.TrimSuffix(msg, ", ")
			msg = fmt.Sprintf(temp, g.region, g.machineName, gpuUUID, containerID, msg)
			var err error
			if !IsTestEnv {
				err = libs.FeiShuRobotSendMSg(libs.GpuOccupyAlert, msg)
			}
			if err != nil {
				l.ErrorE(err, "gpu occupy set, found gpu conflict, send msg failed, msg: [%s]", msg)
			} else {
				l.Info("gpu occupy set, found gpu conflict, send msg [%s]", msg)
			}
		}
	}
}

func (g *GpuOccupy) conflictRepeatCheckGetMsg(containerID string, gpuUUID string) (msg string) {
	g.occupyMapMu.Lock()
	for oldContainer, _ := range g.occupyMap[gpuUUID] {
		if oldContainer == containerID {
			continue
		}
		//if _, ok := g.containerRunningFromServer.Load(oldContainer); ok {
		//	continue
		//}

		g.l.Info("startSet[10]: gpuUUID:%s, newContainerID:%s, oldContainerID:%s", gpuUUID, containerID, oldContainer)
		msg = fmt.Sprintf("%s%s, ", msg, oldContainer)
	}
	g.occupyMapMu.Unlock()
	return
}

var temp = `gpu占用冲突 🚨🚨
region: 	%s
machine: 	%s
gpu_uuid: 	%s
new_container: 	%s
old_container: 	%s
`

func (g *GpuOccupy) cronSet(containerID string, newStatue agentConstant.ContainerStatePhase, gpuUUIDList []string) {
	if newStatue == agentConstant.Running {
		// 该容器正在响应关机，此处不需要再回填回来
		if _, ok := g.stopUnsetButNotFinished.Load(containerID); ok {
			return
		}
		g.occupyMapMu.Lock()
		for _, gpuUUID := range gpuUUIDList {
			if _, ok := g.occupyMap[gpuUUID]; !ok {
				g.occupyMap[gpuUUID] = map[string]struct{}{}
			}
			if _, ok := g.occupyMap[gpuUUID][containerID]; !ok {
				g.occupyMap[gpuUUID][containerID] = struct{}{}
				g.l.Info("cronSet: gpu:%s set container:%s", gpuUUID, containerID)
			}
		}
		g.occupyMapMu.Unlock()
	} else {
		g.unset(containerID, false, false, false)
	}
}

// 只在stop,remove调用
func (g *GpuOccupy) unset(containerID string, debugLog, isStopOpt, isStopOptFinished bool) {
	if debugLog {
		g.l.Info("%s unset called, will remove startDelay and containerRunningFromServer flag", containerID)
	}

	if isStopOpt {
		g.stopUnsetButNotFinished.Store(containerID, true)
	}
	if isStopOptFinished {
		g.stopUnsetButNotFinished.Delete(containerID)
	}

	g.containerRunningFromServer.Delete(containerID)

	if cc, ok := g.startDelay.Load(containerID); ok {
		if cc != nil {
			cancel := cc.(context.CancelFunc)
			cancel()
		}
		g.startDelay.Delete(containerID)
	}

	g.occupyMapMu.Lock()
	for gpuUUID, containerMap := range g.occupyMap {
		if _, ok := containerMap[containerID]; ok {
			g.l.Info("gpu:%s unset container:%s, fromCron:%v", gpuUUID, containerID, !debugLog)
			delete(g.occupyMap[gpuUUID], containerID)
		}
	}
	g.occupyMapMu.Unlock()
}
