package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
	"time"
)

func (g *Guard) saveHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.Save")
	var writeMessage *messenger.Message
	defer func() {
		if writeMessage != nil {
			writer <- *writeMessage
		} else {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    messenger.DefaultSkipType,
				Payload: "",
			}
		}
	}()

	param := &constant.ContainerSaveParam{}
	err := param.ParseFromString(in.Payload)
	if err != nil {
		if err != constant.EchoMessageSkippedError {
			l.<PERSON>ield("msg", serializer.IndentString(in)).WarnE(err, "parse payload failed, skip")
		}
		return
	}
	defer func() {
		if err != nil {
			var resp = &constant.UploadOssInfo{
				ContainerID: param.NewContainerRuntimeParam.ContainerID,
				ImageUUID:   param.ImageUUID,
				Error:       err.Error(),
			}
			payloadString, toErr := resp.String()
			if toErr != nil {
				l.WithField("resp", resp).Warn("resp msg marshal failed")
				return
			}
			writeMessage = &messenger.Message{
				MsgID:   in.MsgID,
				Type:    in.Type,
				Payload: payloadString,
			}
		}
	}()

	bindCtx, cancel := context.WithCancel(ctx)
	release := g.waitContainerOptions(cancel, param.NewContainerRuntimeParam.ContainerID, in.MsgID)
	defer release()

	// lock
	waitAt := time.Now()
	l.Info("[save Lock] save container: Container '%s' is waiting for mig lock...", param.NewContainerRuntimeParam.ContainerID.String())

	select {
	case g.uploadDiffLimitChan <- struct{}{}:
	case <-bindCtx.Done():
		l.Info("cancel func called while waiting for [save lock], exit now...")
		return
	}

	lockAt := time.Now()
	l.Info("[save Lock] save container: Container '%s' got save lock. It took %f seconds.", param.NewContainerRuntimeParam.ContainerID.String(), lockAt.Sub(waitAt).Seconds())
	defer func() {
		<-g.uploadDiffLimitChan
		releaseAt := time.Now()
		l.Info("[save Lock] save container: Container '%s' released mig lock. It took %f seconds from lock. err=%+v", param.NewContainerRuntimeParam.ContainerID.String(), releaseAt.Sub(lockAt).Seconds(), err)
	}()

	param.ProgressHook = g.uploadOssHook(param.NewContainerRuntimeParam.ContainerID)
	data, imageID, err := g.containerMonitor.GetGraphDriverData(param.NewContainerRuntimeParam.ContainerID)
	if err != nil {
		l.WarnE(err, "Upload diff failed: cannot get GraphDriverData.")
		return
	}

	usage, err := g.containerMonitor.GetContainerUsage(param.NewContainerRuntimeParam.ContainerID)
	if err != nil {
		l.WithField("container id", param.NewContainerRuntimeParam.ContainerID).ErrorE(err, "get container usage failed")
		return
	}
	err = g.booter.SaveContainer(bindCtx, *param, usage.RootFSUsedSize, data.UpperDir, imageID)
	if err != nil {
		l.WarnE(err, "save container failed")
		return
	}
	var resp = &constant.UploadOssInfo{
		ContainerID: param.NewContainerRuntimeParam.ContainerID,
		ImageUUID:   param.ImageUUID,
		Finished:    true,
	}
	payloadString, toErr := resp.String()
	if toErr != nil {
		l.WithField("resp", resp).Warn("resp msg marshal failed")
		return
	}
	writeMessage = &messenger.Message{
		MsgID:   in.MsgID,
		Type:    in.Type,
		Payload: payloadString,
	}
	return
}

func (g *Guard) uploadOssHook(containerID constant.ContainerID) func(uploadOssInfo *constant.UploadOssInfo) {
	return func(uploadOssInfo *constant.UploadOssInfo) {
		uploadOssInfo.ContainerID = containerID
		g.usageMutex.Lock()
		defer g.usageMutex.Unlock()
		if _, ok := g.UploadOssInfo[containerID]; !ok {
			g.UploadOssInfo[containerID] = uploadOssInfo
			g.UploadOssInfo[containerID].IsUploadNew = true
		} else {
			if g.UploadOssInfo[containerID] != nil {
				g.UploadOssInfo[containerID].Progress = uploadOssInfo.Progress
				g.UploadOssInfo[containerID].ImageUUID = uploadOssInfo.ImageUUID
				g.UploadOssInfo[containerID].Error = uploadOssInfo.Error
				if uploadOssInfo.ImageSize != 0 {
					g.UploadOssInfo[containerID].ImageSize = uploadOssInfo.ImageSize
				}
				if uploadOssInfo.ObjectSize != 0 {
					g.UploadOssInfo[containerID].ObjectSize = uploadOssInfo.ObjectSize
				}
			} else {
				g.UploadOssInfo[containerID] = uploadOssInfo
			}
			g.UploadOssInfo[containerID].IsUploadNew = true
		}
	}
}
