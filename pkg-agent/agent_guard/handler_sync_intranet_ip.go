package agent_guard

import (
	"context"
	"server/pkg-agent/diff_cache"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
)

func (g *Guard) syncIntranetIpHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	var handlerErr error

	l := logger.NewLogger("Guard.Handler.syncIntranetIpHandler")
	defer func() {
		if handlerErr != nil {
			l.WarnE(handlerErr, "[FAILED] Agent handle syncIntranetIpHandler req failed.")
		}
		writer <- messenger.Message{
			MsgID:   in.MsgID,
			Type:    messenger.DefaultSkipType,
			Payload: "",
		}
	}()

	diff_cache.RunOssFileReporter(l, in.Payload)
}
