package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
)

func (g *Guard) containerCheckHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.Create")

	payload := &constant.ContainerCheckRequest{}
	parseErr := payload.ParseFromString(in.Payload)
	if parseErr != nil {
		if parseErr != constant.EchoMessageSkippedError {
			l.<PERSON>("msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
		}
		return
	}

	for _, containerID := range payload.Containers {
		msg := g.getContainerStatus(containerID, l)
		if msg != nil {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    in.Type,
				Payload: msg.Payload,
			}
		}
	}

	return
}
