package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
)

func (g *Guard) reInitHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.ReInit")

	var writeMessage *messenger.Message
	defer func() {
		if writeMessage != nil {
			writer <- *writeMessage
		} else {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    messenger.DefaultSkipType,
				Payload: "",
			}
		}
	}()

	payload := &constant.ContainerCreateAndRuntimeParam{}
	parseErr := payload.ParseFromString(in.Payload)
	if parseErr != nil {
		if parseErr != constant.EchoMessageSkippedError {
			l.WithField("msg", serializer.IndentString(in)).WarnE(parseErr, "parse payload failed, skip")
		}
		return
	}

	release := g.killAndWaitContainerOptions(payload.Param.ContainerID, in.MsgID)
	defer release()

	resp := constant.ContainerOperateResponse{
		ContainerID:           payload.Param.ContainerID,
		ResponseFromAgentFlag: true,
	}

	// re init 过程中需要冻结container的所有操作, 否则由于先删除->再创建的操作, 会造成严重的误判(外界认为该container已经被删除)
	g.freezeMutex.Lock()
	g.freezeSet[payload.Param.ContainerID] = true
	g.freezeMutex.Unlock()
	defer func() {
		g.freezeMutex.Lock()
		g.freezeSet[payload.Param.ContainerID] = false
		g.freezeMutex.Unlock()
	}()

	payload.Param.ProgressHook = g.pullImageHook(payload.Param.ContainerID)
	createContainerErr := g.booter.ReInitContainer(ctx, payload.Param, payload.RuntimeParam, writer)
	if createContainerErr != nil {
		resp.Code = constant.CodeErr
		resp.ErrMsg = createContainerErr.Error()
	}

	payloadString, toErr := resp.String()
	if toErr != nil {
		l.WithField("resp", resp).Warn("resp msg marshal failed")
		return
	}

	writeMessage = &messenger.Message{
		MsgID:   in.MsgID,
		Type:    in.Type,
		Payload: payloadString,
	}
	return
}
