package agent_guard

import (
	"context"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/messenger"
	"server/pkg/logger"
	"server/pkg/serializer"
)

func (g *Guard) CancelSaveImageHandler(ctx context.Context, in messenger.Message, writer chan<- messenger.Message) {
	l := logger.NewLogger("Guard.Handler.CancelSaveImageHandler")
	var writeMessage *messenger.Message
	defer func() {
		if writeMessage != nil {
			writer <- *writeMessage
		} else {
			writer <- messenger.Message{
				MsgID:   in.MsgID,
				Type:    messenger.DefaultSkipType,
				Payload: "",
			}
		}
	}()

	param := &constant.ContainerCancelSaveParam{}
	err := param.ParseFromString(in.Payload)
	if err != nil {
		if err != constant.EchoMessageSkippedError {
			l.<PERSON>ield("msg", serializer.IndentString(in)).WarnE(err, "parse payload failed, skip")
		}
		return
	}
	defer func() {
		if err != nil {
			var resp = &constant.CancelUploadImageInfo{
				ContainerID: param.NewContainerRuntimeParam.ContainerID,
				ImageUUID:   param.ImageUUID,
				Error:       err.Error(),
			}
			payloadString, toErr := resp.String()
			if toErr != nil {
				l.WithField("resp", resp).Warn("resp msg marshal failed")
				return
			}
			writeMessage = &messenger.Message{
				MsgID:   in.MsgID,
				Type:    in.Type,
				Payload: payloadString,
			}
		}
	}()
	l.Info("cancel container upload image, image_uuid:%s", param.ImageUUID)
	release := g.killAndWaitContainerOptions(param.NewContainerRuntimeParam.ContainerID, in.MsgID)
	defer release()
	var resp = &constant.CancelUploadImageInfo{
		ContainerID: param.NewContainerRuntimeParam.ContainerID,
		ImageUUID:   param.ImageUUID,
		Finished:    true,
	}
	payloadString, toErr := resp.String()
	if toErr != nil {
		l.WithField("resp", resp).Warn("resp msg marshal failed")
		return
	}
	writeMessage = &messenger.Message{
		MsgID:   in.MsgID,
		Type:    in.Type,
		Payload: payloadString,
	}
	return
}
