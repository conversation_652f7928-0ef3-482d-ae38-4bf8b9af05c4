package agent_module_definition

import (
	"github.com/docker/docker/api/types"
	constant "server/pkg-agent/agent_constant"
	"server/pkg-agent/agent_container"
)

type ContainerMonitor interface {
	GetAllContainers() (containers, adfsContainers constant.ContainerSimpleInfoList, err error)
	GetContainerStatus(containerID constant.ContainerID) (constant.ContainerStatus, error)
	GetContainerUsage(containerID constant.ContainerID) (constant.ContainerUsage, error)
	GetStatJSON(containerID constant.ContainerID) (containerJSON types.ContainerJSON, err error)
	GetGraphDriverData(containerID constant.ContainerID) (data agent_container.GraphDriverData, imageID string, err error)
}
