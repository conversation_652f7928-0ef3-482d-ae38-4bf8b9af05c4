package agent_files

import (
	"bytes"
	"fmt"
	"github.com/pkg/errors"
	"os"
	"os/exec"
	"path/filepath"
)

// WriteRandomFile
// 此方法会使用dd向 dst 文件写入 不超过 sizeInKB * 1024 bytes大小的文件
// 文件内容读取自 /dev/zero, 如果disk space达到上限, 则返回error != nil
// 比如: 向一个最大容量为 1GB的硬盘中写入2GB数据, 此方法会写入1GB数据, 然后返回error
// Full documentation at: <http://www.gnu.org/software/coreutils/dd>
func WriteRandomFile(dst string, sizeInKB int64) error {
	_, err := os.Lstat(filepath.Dir(dst))
	if err != nil {
		return err
	}
	dd := exec.Command("dd", "bs=1K", fmt.Sprintf("count=%d", sizeInKB), "if=/dev/zero", fmt.Sprintf("of=%s", dst))
	bufErr := new(bytes.Buffer)
	dd.Stderr = bufErr
	err = dd.Run()

	if err != nil {
		return errors.Wrap(err, bufErr.String())
	}
	return nil
}
