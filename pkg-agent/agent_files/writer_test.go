package agent_files_test

import (
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"os"

	. "server/pkg-agent/agent_files"
)

var _ = Describe("Writer", func() {
	testDirPath := "/tmp/testWrite"
	BeforeEach(func() {
		err := os.MkdirAll(testDirPath, os.ModePerm)
		Expect(err).Should(Succeed())
	})
	It("test 1", func() {
		err := WriteContainerInitFilesToDirectory("../../_output", ContainerInitRequiredParams{
			RootPassword:    "test_password",
			ProxtHost:       "test_proxy_host",
			ProxyPort:       "9990",
			ProxyToken:      "test_proxy_token",
			JupyterToken:    "test_jupyter_token",
			SSHPort:         9980,
			JupyterPort:     9981,
			TensorboardPort: 9982,
		})
		Expect(err).Should(Succeed())
		err = os.RemoveAll(testDirPath)
		Expect(err).Should(Succeed())
	})
})
