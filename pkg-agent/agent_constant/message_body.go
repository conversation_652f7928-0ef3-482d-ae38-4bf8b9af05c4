package agent_constant

import (
	"encoding/json"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"github.com/pkg/errors"
	"strings"
)

type ContainerCreateParam struct {
	NewContainerParam
	ProgressHook             func(pullProgress, downloadProgress float64) `json:"-"`
	DownloadPrivateImageInfo *DownloadPrivateImageInfo                    `json:"download_private_image_info"`
}

type ContainerRuntimeParam struct {
	NewContainerRuntimeParam
}

type ContainerCreateAndRuntimeParam struct {
	Param        ContainerCreateParam
	RuntimeParam ContainerRuntimeParam
}

type ContainerOperateResponse struct {
	ContainerID ContainerID `json:"container_id"`
	Code        string      `json:"code"`
	ErrMsg      string      `json:"err_msg"`

	// container在create之后, 会返回docker_container_uuid, 用作记录容器的全局唯一标识, 用作debug等场景
	DockerContainerUUID string `json:"docker_container_uuid"`

	// ResponseFromAgentFlag, 用于标识此返回值由agent发出, 防止错误的消息被agent处理
	ResponseFromAgentFlag bool `json:"response_from_agent_flag"`
}

type ResponseCode string

const (
	CodeOK  = ""
	CodeErr = "Err"
)

func (c *ContainerCreateParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return c.valid()
}

func (c *ContainerCreateParam) valid() error {
	if len(c.Image) == 0 {
		return errors.New("image is nil")
	}
	return nil
}

func (c *ContainerRuntimeParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return c.valid()
}

func (c *ContainerRuntimeParam) valid() error {

	return nil
}

func (c *ContainerCreateAndRuntimeParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return c.valid()
}

func (c *ContainerCreateAndRuntimeParam) valid() error {
	if err := c.Param.valid(); err != nil {
		return err
	}

	if err := c.RuntimeParam.valid(); err != nil {
		return err
	}

	return nil
}

func (c *ContainerOperateResponse) ParseFromString(in string) error {
	return json.Unmarshal([]byte(in), c)
}

func (c *ContainerOperateResponse) String() (string, error) {
	c.ErrMsg = CutErrMessage(c.ErrMsg)
	out, err := json.Marshal(c)
	return string(out), err
}

func CutErrMessage(errMsg string) string {
	if len(errMsg) > 300 {
		errMsg = errMsg[:300]
	}
	return errMsg
}

type MachineStatusParam struct {
	MachineHealthInfo
}

type MachineRegionParam struct {
	MachineID  string `json:"machine_id"`
	RegionSign string `json:"region_sign"`
}

type MachineRegisterParam struct {
	MachineHardwareInfo
}

type ContainerStatusParam struct {
	ContainerStatus
}

type ContainerUsageParam struct {
	ContainerUsage
}

type ContainerMigrateParam struct {
	ContainerPerformMigrate
}

// ContainerCheckRequest : server 主动发出的检查容器的请求. 可以扩展为主动检查状态, usage 等.
type ContainerCheckRequest struct {
	Containers []ContainerID `json:"containers"`
}

// ContainerSkippedResponse 对 server 的主动检查命令的回应.
type ContainerSkippedResponse struct {
}

func (c *MachineStatusParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}
	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *MachineRegionParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}
	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *MachineRegisterParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *ContainerStatusParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *ContainerUsageParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *ContainerCheckRequest) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *ContainerCheckRequest) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

func (c *ContainerSkippedResponse) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return err
}

func (c *ContainerSkippedResponse) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

func NewMinioClient(cred MinioCredentials) (*minio.Client, error) {
	endpoint := cred.Endpoint
	httpsPrefix := "https://"
	var secure bool

	if strings.Contains(cred.Endpoint, httpsPrefix) {
		secure = true
		endpoint = strings.TrimPrefix(strings.TrimSpace(cred.Endpoint), httpsPrefix)
	}
	return minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cred.AccessKeyID, cred.SecretAccessKey, ""),
		Secure: secure,
	})
}

type ContainerSaveParam struct {
	ImageUUID                string          `json:"image_uuid"`
	MinioBucketInfo          MinioBucketInfo `json:"minio_bucket_info"`
	NewContainerRuntimeParam NewContainerRuntimeParam
	ProgressHook             func(uploadOssInfo *UploadOssInfo) `json:"-"`
	MinioCredentials         MinioCredentials                   `json:"minio_credentials"`
}

type DownloadPrivateImageInfo struct {
	ReadLayerImageName string           `json:"read_layer_image_name"`
	MinioBucketInfo    MinioBucketInfo  `json:"minio_bucket_info"`
	MinioCredentials   MinioCredentials `json:"minio_credentials"`
}

func (d *DownloadPrivateImageInfo) String() string {
	s, _ := json.Marshal(d)
	return string(s)
}
func (d *DownloadPrivateImageInfo) ParseFromContent(content string) error {
	return json.Unmarshal([]byte(content), d)
}

type MinioBucketInfo struct {
	BucketName string `json:"bucket_name"`
	ObjectName string `json:"object_name"`
	ObjectSize int64  `json:"object_size"`
}

type StorageOSSSignType string // oss的唯一标识

func (rs StorageOSSSignType) String() string {
	return string(rs)
}

type MinioCredentials struct {
	StorageOSSRegionSign StorageOSSSignType `json:"storage_oss_region_sign"`
	DefaultBucketName    string             `json:"default_bucket_name"`

	Endpoint        string `json:"endpoint"`
	AccessKeyID     string `json:"access_key_id"`
	SecretAccessKey string `json:"secret_access_key"`
}

type TmpDiffFileParam struct {
	ContainerID  ContainerID `json:"source_container_id"`
	MergeTagName string      `json:"merge_tag_name"`
	ObjectName   string      `json:"object_name"`
}

func (c *ContainerSaveParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}
	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return c.valid()
}

func (c *ContainerSaveParam) valid() error {
	if c.MinioBucketInfo.BucketName == "" || c.MinioBucketInfo.ObjectName == "" {
		return errors.New("bucket info is nil")
	}
	return nil
}

func (c *ContainerMigrateParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}

	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}

	return err
}

func (c *ContainerMigrateParam) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

type ContainerCancelSaveParam struct {
	ImageUUID                string `json:"image_uuid"`
	NewContainerRuntimeParam NewContainerRuntimeParam
}

func (c *ContainerCancelSaveParam) String() (string, error) {
	out, err := json.Marshal(c)
	return string(out), err
}

func (c *ContainerCancelSaveParam) ParseFromString(in string) error {
	// 检测socket是否是一个回声消息, 如果是, skip
	if strings.Contains(in, "response_from_agent_flag") {
		return EchoMessageSkippedError
	}
	err := json.Unmarshal([]byte(in), c)
	if err != nil {
		return err
	}
	return c.valid()
}

func (c *ContainerCancelSaveParam) valid() error {
	if c.NewContainerRuntimeParam.ContainerID == "" || c.ImageUUID == "" {
		return errors.New("container_id or image_uuid info is nil")
	}
	return nil
}
