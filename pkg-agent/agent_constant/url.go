package agent_constant

import (
	"fmt"
	"net/url"
)

type URL struct {
	host   string
	secure bool
}

// url: *************:33001
func ParseUrl(input string) (u *URL) {
	u = &URL{}
	uu, err := url.Parse(input)
	if err != nil {
		u.host = input
		return
	}
	if len(uu.Host) == 0 {
		u.host = input
		return
	}

	u.host = uu.Host
	u.secure = uu.Scheme == "https" || uu.Scheme == "wss"
	return
}

func (u *URL) GetHost() (host string) {
	return u.host
}
func (u *URL) GetSecure() bool {
	return u.secure
}

/*
 * GPU agent
 */

func (u *URL) GetWebsocketConnectUrls() (urls []string) {
	if u.secure {
		urls = append(urls, fmt.Sprintf("wss://%s/%s", u.host, WebsocketApiPrefix))
	} else {
		urls = append(urls, fmt.Sprintf("ws://%s/%s", u.host, WebsocketApiPrefix))
		urls = append(urls, fmt.Sprintf("wss://%s/%s", u.host, WebsocketApiPrefix))
	}

	return
}

func (u *URL) GetSaveLogUrls() (urls []string) {
	if u.secure {
		urls = append(urls, fmt.Sprintf("https://%s/%s", u.host, SaveLogApiPrefix))
	} else {
		urls = append(urls, fmt.Sprintf("http://%s/%s", u.host, SaveLogApiPrefix))
		urls = append(urls, fmt.Sprintf("https://%s/%s", u.host, SaveLogApiPrefix))
	}

	return
}

/*
 * Storage agent
 */

func (u *URL) GetStorageAgentWebsocketConnectUrls() (urls []string) {
	prefix := storageAgentWebsocketApiRoute

	if u.secure {
		urls = append(urls, fmt.Sprintf("wss://%s/%s", u.host, prefix))
	} else {
		urls = append(urls, fmt.Sprintf("ws://%s/%s", u.host, prefix))
		urls = append(urls, fmt.Sprintf("wss://%s/%s", u.host, prefix))
	}

	return
}

func (u *URL) GetStorageAgentSaveLogUrls() (urls []string) {
	prefix := storageAgentSaveLogApiRoute
	if u.secure {
		urls = append(urls, fmt.Sprintf("https://%s/%s", u.host, prefix))
	} else {
		urls = append(urls, fmt.Sprintf("http://%s/%s", u.host, prefix))
		urls = append(urls, fmt.Sprintf("https://%s/%s", u.host, prefix))
	}

	return
}

const (
	WebsocketApiPrefix string = "agent/v1/connect"
	SaveLogApiPrefix   string = "agent/v1/logs"

	storageAgentWebsocketApiRoute string = "agent/v1/storage/connect"
	storageAgentSaveLogApiRoute   string = "agent/v1/storage/logs"

	StorageAgentAuthApiRoute string = "agent/v1/storage/auth"
)

// ServerConnectedURL 成功连接的URL
var ServerConnectedURL *URL

func GetGlobalKVServerURL() string {
	prefix := "api/v1/internal/kv/key/"

	if ServerConnectedURL == nil {
		return "https://test.autodl.com:33443" + prefix
	}

	if ServerConnectedURL.GetSecure() {
		return fmt.Sprintf("https://%s/%s", ServerConnectedURL.GetHost(), prefix)
	}

	return fmt.Sprintf("http://%s/%s", ServerConnectedURL.GetHost(), prefix)
}

func GetGlobalFTServerURL() string {
	prefix := "api/v1/internal/ft/status/"

	if ServerConnectedURL == nil {
		return "https://test.autodl.com:33443" + prefix
	}

	if ServerConnectedURL.GetSecure() {
		return fmt.Sprintf("https://%s/%s", ServerConnectedURL.GetHost(), prefix)
	}

	return fmt.Sprintf("http://%s/%s", ServerConnectedURL.GetHost(), prefix)
}
