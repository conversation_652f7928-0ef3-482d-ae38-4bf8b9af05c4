package agent_constant

import (
	"context"
	"fmt"
	"github.com/docker/docker/client"
	"time"
)

type ContainerSimpleInfoList []ContainerSimpleInfo

func (c ContainerSimpleInfoList) ToMapping() map[ContainerID]ContainerSimpleInfo {
	res := make(map[ContainerID]ContainerSimpleInfo)

	for i := range c {
		res[c[i].ID] = c[i]
	}
	return res
}

type ContainerSimpleInfo struct {
	ID         ContainerID
	Image      string
	StatePhase ContainerStatePhase
	CreatedAt  time.Time
}

const (
	ContainerBuiltinLabelKey   = "origin.seetaas"
	ContainerBuiltinLabelValue = "true"

	ContainerV2BuiltinLabelKey = "origin.autodlv2"
)

type RuntimeVersion string

func (v RuntimeVersion) GetMachineUUIDLacation() string {
	if v == "v2" {
		return ".machine_uuid_v2"
	}

	return ".machine_uuid"
}

func (v RuntimeVersion) GetContainerBuiltinLabelKey() string {
	if v == "v2" {
		return ContainerV2BuiltinLabelKey
	}

	return ContainerBuiltinLabelKey
}

func (v RuntimeVersion) GetApiPort() string {
	if v == "v2" {
		return "8091"
	}

	return "8090"
}

var CurrentRuntimeVersion RuntimeVersion = "v1"

func NewDockerClient() (dockerClient DockerClient, err error) {
	dockerSocketPath := "unix:///var/run/docker.sock"
	dockerClient, err = client.NewClientWithOpts(
		client.WithHost(dockerSocketPath),
		//client.WithTimeout(time.Second*5),
		client.WithVersion("1.41"),
	)
	if err != nil {
		return
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	_, err = dockerClient.Ping(ctx)
	return
}

const MigrationNetDisk = "/storage/nas/migrate" // for test
const MigrationOssTmpPath = "/data/oss"         //临时存放oss下载或上传的文件
const DockerVolumePath = "/data/docker/volumes"

// MigrateConcurrentLimit MigrationLimitOfUploadingDiff MigrationLimitOfMergingDiff 限制同时迁移的数量
const MigrateConcurrentLimit = 1
const MigrationLimitOfUploadingDiff = 2
const MigrationLimitOfMergingDiff = 1

func NewDiffTemporaryName(sourcePath string, containerUUID ContainerID) string {
	return fmt.Sprintf("%s-temporary-%s", sourcePath, containerUUID)
}
