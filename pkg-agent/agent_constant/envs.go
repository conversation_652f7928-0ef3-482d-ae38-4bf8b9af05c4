package agent_constant

import (
	"fmt"
	"golang.org/x/exp/slices"
)

const (
	// RuntimeEnv 表明当前ga运行于何种环境, one of ["direct", "docker-compose", "k8s"]
	RuntimeEnv = "GA_RUNTIME"

	InitDirectoryLocation    = "/init"
	StorageDirectoryLocation = "/root/autodl-tmp"

	// TODO: 路径待确认

	NetDiskInDockerMount            = "/root/autodl-nas"                    // 容器内用户看到的路经
	NetDiskQuotaWarningOnMachineDir = "/storage/nas/1410065407-nfs-warning" // project id 最大为 1410065407
	NetDiskOnMachineMountDir        = "/storage/nas"
	NetDiskOnMachineMountFmt        = NetDiskOnMachineMountDir + "/%d" // '/storage/%d' gpu 机器上本地挂载的路径. e.g. /storage/2

	CommonDataInContainerMount  = "/root/autodl-pub"
	CommonDataOnMachineMountDir = "/storage/nas/1"

	/*
		adfs公共数据集位于宿主机/data/adfs/pub/data, 由运维运行挂载
		docker run -d --name=adfs-pub --privileged --network host -v /tmp/weed_cache:/cache -v /data/adfs/pub:/storage:shared -w /root registry.cn-beijing.aliyuncs.com/codewithgpu/seaweedfs:3.14sr bash -c "bash /root/mount.sh quota-1 *************:8888,*************:8888,*************:8888 6 10TiB 3000000"
		adfs用户目录位于宿主机/data/adfs/{uid}/data，由agent启动adfs mount容器，执行shared挂载
	*/
	ADFSOnMachineMountDir           = "/data/adfs"
	CommonADFSDataOnMachineMountDir = ADFSOnMachineMountDir + "/pub/data"
	ADFSOnMachineMountFmt           = ADFSOnMachineMountDir + "/%d-%s"
	ADFSInDockerMount               = "/root/autodl-fs"
	ADFSOnMachineCachePath          = "/storage/weed_cache"
	ADFSOnMachineCacheFmt           = ADFSOnMachineCachePath + "/%d-%s"
	ExclusiveNfsOnMachineMountFmt   = "/data/adfs/nfs/%d-%s"

	// 实际需要/var/tmp/OptixCache_root，但是挂载时目录不存在会报错，所以挂载/var/tmp
	RenderOptixCacheOnMachineDir     = "/var/tmp"
	RenderOptixCacheInContainerMount = "/var/tmp"

	//nfs,adfs,autofs image_name
	Exclusivenfs   = "registry.cn-beijing.aliyuncs.com/codewithgpu2/exclusivenfs:1.0"
	ExclusivenfsV2 = "registry.cn-beijing.aliyuncs.com/codewithgpu2/exclusivenfs:2.0" // 新镜像

	Seaweedfs = "registry.cn-beijing.aliyuncs.com/codewithgpu/seaweedfs:3.34ss"

	Autofs = "registry.cn-beijing.aliyuncs.com/codewithgpu/autofs:1.04"
)

func GetUIDNetDiskMountInfo(regionSign string, uid int) string {
	if slices.Contains([]string{
		"neimeng-A",
		"neimeng-C",
		"neimeng-D",
	}, regionSign) && slices.Contains([]int{
		14,
		44609,
	}, uid) {
		return fmt.Sprintf("%s-2/%d", NetDiskOnMachineMountDir, uid)
	}

	return fmt.Sprintf(NetDiskOnMachineMountFmt, uid)
}
